﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
namespace X.PenServer.Contracts.Queue
{
	public class InteractiveWorkbookSingleItemAnswer
	{
		[JsonPropertyName(nameof(ItemId))]
		[JsonInclude]
		public string ItemId { get; set; }

		[JsonPropertyName(nameof(UserId))]
		[JsonInclude]
		public string UserId { get; set; }

		[JsonPropertyName(nameof(PaperId))]
		[JsonInclude]
		public string PaperId { get; set; }

		[JsonPropertyName(nameof(ClassId))]
		[JsonInclude]
		public string ClassId { get; set; }
	}
}
