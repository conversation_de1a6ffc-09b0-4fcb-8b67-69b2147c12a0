﻿// -- Function: PenMapping.cs
// --- Project: PenServer
// ---- Remark: 
// ---- Author: Lucifer
// ------ Date: 2023/10/08 17:35

using System.Text.Json.Serialization;

namespace X.PenServer.Contracts;

public class PenMapping
{
	/// <summary>
	/// MAC地址
	/// </summary>
	[JsonPropertyName(nameof(Mac))]
	[JsonInclude]
	public string Mac { get; set; }

	/// <summary>
	/// 用户id
	/// </summary>
	[JsonPropertyName(nameof(UserId))]
	[JsonInclude]
	public string UserId { get; set; }

	/// <summary>
	/// 班级id
	/// </summary>
	[JsonPropertyName(nameof(ClassId))]
	[JsonInclude]
	public string ClassId { get; set; }

	/// <summary>
	/// 用户类型
	/// </summary>
	[JsonPropertyName(nameof(UserType))]
	[JsonInclude]
	public int UserType { get; set; }

	/// <summary>
	/// 电量
	/// </summary>
	[JsonPropertyName(nameof(Battery))]
	[JsonInclude]
	public int Battery { get; set; }

	/// <summary>
	/// WIFI
	/// </summary>
	[JsonPropertyName(nameof(Ssid))]
	[JsonInclude]
	public string Ssid { get; set; }

	/// <summary>
	/// 点阵笔绑定时间
	/// </summary>
	[JsonPropertyName(nameof(CreateTime))]
	[JsonInclude]
	public DateTime CreateTime { get; set; }
}