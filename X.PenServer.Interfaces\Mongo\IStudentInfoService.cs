﻿// -- Function: IStudentInfoService.cs
// --- Project: X.PenServer.Interfaces
// ---- Remark: 
// ---- Author: Lucifer
// ------ Date: 2023/10/31 15:27

using X.PenServer.Models.Mongo;

namespace X.PenServer.Interfaces.Mongo;

using Lifecycle;

/// <summary>
/// 学生信息
/// </summary>
public interface IStudentInfoService : IMongoService<StudentInfo>, ISingletonService
{
    /// <summary>
    /// 获取学生信息
    /// </summary>
    /// <param name="classid">班级id</param>
    /// <param name="studentno">学号</param>
    /// <returns></returns>
    Task<StudentInfo> GetStudentInfoAsync(string classid, string studentno);

    /// <summary>
    /// 获取学生信息
    /// </summary>
    /// <param name="studentid">学生id</param>
    /// <returns></returns>
    Task<StudentInfo> GetStudentInfoAsync(string studentid);
}