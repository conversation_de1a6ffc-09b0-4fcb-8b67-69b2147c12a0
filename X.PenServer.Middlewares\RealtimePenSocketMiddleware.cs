﻿// -- Function: RealtimePenSocketMiddleware.cs
// --- Project: X.PenServer.Middlewares
// ---- Remark: 
// ---- Author: Lucifer
// ------ Date: 2023/10/27 13:06

using System.Collections.Concurrent;
using System.Net.WebSockets;
using Masa.BuildingBlocks.Data;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using X.PenServer.Contracts.Realtime;
using X.PenServer.Infrastructure;
using X.PenServer.Interfaces;
using X.PenServer.Interfaces.Redis;
using static X.PenServer.Middlewares.MiddlewareExtensions;

namespace X.PenServer.Middlewares;

using System.Collections.Immutable;
using static ApiExtensions;

/// <summary>
/// 点阵笔实时数据
/// </summary>
public class RealtimePenSocketMiddleware : IMiddleware
{
    private readonly ILogger<RealtimePenSocketMiddleware> _log;
    private readonly IRealtimePenService _service;
    private readonly ICommonRedisService _redis_service;
    private readonly ISequentialGuidGenerator _guid_generator;

    public RealtimePenSocketMiddleware(ILogger<RealtimePenSocketMiddleware> log, IRealtimePenService service, ICommonRedisService redis_service, ISequentialGuidGenerator guid_generator)
    {
        _log = log;
        _service = service;
        _redis_service = redis_service;
        _guid_generator = guid_generator;
    }

    /// <summary>
    /// 当前所有已连接的客户端
    /// </summary>
    private static readonly ConcurrentDictionary<string, WebSocket> _all_socket_clients = new();

    /// <summary>
    /// 被实时查看的学生客户端连接
    /// </summary>
    private static readonly ConcurrentDictionary<string, HashSet<string>> _student_clients = new();

    /// <summary>
    /// 被实时查看的正在做试卷题目的学生客户端连接
    /// </summary>
    private static readonly ConcurrentDictionary<string, HashSet<string>> _student_do_paper_item_clients = new();

    /// <summary>
    /// 当前已连接的班级
    /// </summary>
    private static readonly ConcurrentDictionary<string, HashSet<string>> _class_clients = new();

    /// <inheritdoc />
    public async Task InvokeAsync(HttpContext context, RequestDelegate next)
    {
        if (!context.WebSockets.IsWebSocketRequest)
        {
            await next(context);
            return;
        }

        var socket = await context.WebSockets.AcceptWebSocketAsync
        (
            new WebSocketAcceptContext
            {
                KeepAliveInterval = TimeSpan.FromSeconds(60),
                DangerousEnableCompression = false
            }
        );
        var token = context.RequestAborted;
        var wid = _guid_generator.NewStringId();
        if (!_all_socket_clients.ContainsKey(wid))
        {
            _all_socket_clients.TryAdd(wid, socket);
        }
        else
        {
            _all_socket_clients[wid] = socket;
        }

        try
        {
            while (!token.IsCancellationRequested)
            {
                if (socket.State != WebSocketState.Open && socket.State != WebSocketState.Connecting)
                {
                    break;
                }

                var receive = await ReceiveAsync(socket, token);
                if (string.IsNullOrWhiteSpace(receive))
                {
                    continue;
                }

                var data = receive.ToEntity<ReceiveData>();
                var classid = data.ClassId;
                if (string.IsNullOrWhiteSpace(classid))
                {
                    continue;
                }

                //按班级划分用户
                if (!_class_clients.TryGetValue(classid, out var clients))
                {
                    _class_clients.TryAdd
                    (
                        classid, [wid]
                    );
                }
                else
                {
                    clients.TryAdd(wid);
                }

                switch (data.Type)
                {
                    case "100":
                    {
                        var teacher = receive.ToEntity<ReceiveData<TeacherInfo>>();
                        if (teacher.Content == null || string.IsNullOrWhiteSpace(teacher.Content.TeacherId))
                        {
                            break;
                        }

                        var userlist = await _service.GetUserList(data.ClassId, teacher.Content.TeacherId);
                        var xdata = new ReceiveData<List<UserInfo>>()
                        {
                            Type = "200",
                            ClassId = data.ClassId,
                            Content = userlist
                        };
                        var jdata = xdata.ToJsonString();

                        //转发数据
                        await SendAsync(socket, jdata, token);
                        break;
                    }
                    case "101":
                    {
                        var student = receive.ToEntity<ReceiveData<StudentPaperInfo>>();
                        if (student is null)
                        {
                            break;
                        }

                        var xcontent = student.Content;
                        if (student.Content == null ||
                            string.IsNullOrWhiteSpace(xcontent.StudentId) ||
                            string.IsNullOrWhiteSpace(xcontent.PaperId))
                        {
                            break;
                        }

                        var xkey = $"{xcontent.PaperId}|{xcontent.StudentId}";
                        var xhashkey = HashEncrypt(xkey);
                        if (!_student_clients.TryGetValue(xhashkey, out var student_client))
                        {
                            _student_clients.TryAdd
                            (
                                xhashkey, [wid]
                            );
                        }
                        else
                        {
                            student_client.Add(wid);
                        }

                        var student_key = $"PenWebSocket|{xkey}";
                        await _redis_service.SAddAsync(student_key, wid);
                        await _redis_service.ExpireAsync(student_key, TimeSpan.FromHours(2));
                        break;
                    }
                    case "102":
                    {
                        var info = receive.ToEntity<ReceiveData<StudentPaperItemInfo>>();
                        if (info! == null)
                        {
                            break;
                        }

                        var xcontent = info.Content;
                        if (xcontent == null || string.IsNullOrWhiteSpace(xcontent.StudentId) || string.IsNullOrWhiteSpace(xcontent.PaperId) || string.IsNullOrWhiteSpace(xcontent.ItemId))
                        {
                            break;
                        }

                        var xkey = $"{xcontent.PaperId}|{xcontent.StudentId}|{xcontent.ItemId}";
                        var xhashkey = HashEncrypt(xkey);
                        if (!_student_do_paper_item_clients.TryGetValue(xhashkey, out var item_client))
                        {
                            _student_do_paper_item_clients.TryAdd
                            (
                                xhashkey, [wid]
                            );
                        }
                        else
                        {
                            item_client.Add(wid);
                        }

                        var student_key = $"PenWebSocket|PaperItem|{xkey}";
                        await _redis_service.SAddAsync(student_key, wid).ConfigureAwait(false);
                        await _redis_service.ExpireAsync(student_key, TimeSpan.FromHours(2)).ConfigureAwait(false);
                        break;
                    }
                    default:
                        //转发收到的消息
                        await SendAsync(socket, receive, token).ConfigureAwait(false);
                        break;
                }
            }

            _all_socket_clients.TryRemove(wid);
            //当前websocket断开连接,从学生和教师客户端中移除当前连接
            foreach (var client in _student_clients)
            {
                if (client.Value is {Count: > 0} && client.Value.Contains(wid))
                {
                    client.Value.Remove(wid);
                }
            }

            //当前websocket断开连接,从学生正在作答题目客户端中移除当前连接
            foreach (var client in _student_do_paper_item_clients)
            {
                if (client.Value is {Count: > 0} && client.Value.Contains(wid))
                {
                    client.Value.Remove(wid);
                }
            }

            //移除班级信息
            foreach (var client in _class_clients)
            {
                if (client.Value is {Count: > 0} && client.Value.Contains(wid))
                {
                    client.Value.Remove(wid);
                }
            }
        }
        catch (Exception e)
        {
            _log.LogCritical(e, "error: {error}", e.Message);
        }
        finally
        {
            if (socket.State != WebSocketState.Closed)
            {
                socket.Abort();
                socket.Dispose();
            }
        }
    }

    /// <summary>
    /// 获取已连接的客户端列表
    /// </summary>
    /// <returns></returns>
    public static ConcurrentDictionary<string, WebSocket> GetAllClients()
    {
        return _all_socket_clients;
    }

    /// <summary>
    /// 获取当前被查看正在做试卷题目的学生已连接列表
    /// </summary>
    /// <param name="paperid">试卷id</param>
    /// <param name="studentid">学生id</param>
    /// <param name="itemid">题目id</param>
    /// <returns></returns>
    public static ImmutableHashSet<string> GetStudentWorkbookClients(string paperid, string studentid, string itemid)
    {
        var key = $"{paperid}|{studentid}|{itemid}";
        var hashkey = HashEncrypt(key);
        return _student_do_paper_item_clients.TryGetValue(hashkey, out var clients) ? clients.ToImmutableHashSet() : ImmutableHashSet.Create<string>();
    }

    /// <summary>
    /// 获取当前被查看的学生已连接的列表
    /// </summary>
    /// <param name="paperid">试卷id</param>
    /// <param name="studentid">学生id</param>
    /// <returns></returns>
    public static ImmutableHashSet<string> GetStudentClients(string paperid, string studentid)
    {
        var key = $"{paperid}|{studentid}";
        var hashkey = HashEncrypt(key);
        return _student_clients.TryGetValue(hashkey, out var clients) ? clients.ToImmutableHashSet() : ImmutableHashSet.Create<string>();
    }

    /// <summary>
    /// 获取当前班级已连接客户端列表
    /// </summary>
    /// <param name="classid">班级id</param>
    /// <returns></returns>
    public static ImmutableHashSet<string> GetClassClients(string classid)
    {
        return _class_clients.TryGetValue(classid, out var clients) ? clients.ToImmutableHashSet() : ImmutableHashSet.Create<string>();
    }
    
    /// <summary>
    /// 根据连接id获取当前websocket连接
    /// </summary>
    /// <param name="wid">连接id</param>
    /// <returns></returns>
    public static WebSocket GetSocketClient(string wid)
    {
        var ispass = _all_socket_clients.TryGetValue(wid, out var socket);
        return ispass ? socket : null;
    }
}