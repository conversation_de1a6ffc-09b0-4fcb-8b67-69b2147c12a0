﻿namespace X.PenServer.Services.Mongo
{
    /// <inheritdoc cref="IPen8KLogService" />
    public class Pen8KLogService : MongoAutoService<Pen8KLog>, IPen8KLogService
    {
        /// <inheritdoc />
        public Pen8KLogService(IMongoConfig config) : base(config)
        {
        }

        /// <inheritdoc />
        protected override void CreateIndex(IMongoCollection<Pen8KLog> collection)
        {
            var userid_builder = Builders<Pen8KLog>.IndexKeys
                .Ascending(x => x.UserId)
                .Ascending(x => x.PageId)
                .Ascending(x => x.Page)
                .Ascending(x => x.AddTime)
                .Ascending(x => x.Mac);
            collection.Indexes.CreateIndex(userid_builder, collection.CollectionNamespace.CollectionName + "_UserId_Key");

            var mac_builder = Builders<Pen8KLog>.IndexKeys
                .Ascending(x => x.Mac)
                .Ascending(x => x.UserId)
                .Ascending(x => x.PageId)
                .Ascending(x => x.Page)
                .Ascending(x => x.AddTime);

            collection.Indexes.CreateIndex(mac_builder, collection.CollectionNamespace.CollectionName + "_Mac_Key");
        }
    }
}
