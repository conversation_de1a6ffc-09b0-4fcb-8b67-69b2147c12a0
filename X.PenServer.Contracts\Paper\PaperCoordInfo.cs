﻿// -- Function: PaperCoordInfo.cs
// --- Project: X.PenServer.Contracts
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2024/3/8 16:50
namespace X.PenServer.Contracts.Paper;

/// <summary>
/// 语音试卷坐标信息
/// </summary>
public class PaperCoordInfo
{
    public string Id { get; set; }

    /// <summary>
    /// 试卷id
    /// </summary>
    public string PaperId { get; set; }

    /// <summary>
    /// 试卷页id
    /// </summary>
    public string WorkBookPageId { get; set; }

    /// <summary>
    /// 题目id
    /// </summary>
    public string ItemId { get; set; }

    /// <summary>
    /// 坐标区域范围
    /// </summary>
    public string Range { get; set; }

    public int Page { get; set; }

    /// <summary>
    /// 坐标类型
    /// </summary>
    /// <value>1: 题干</value>
    /// <value>2: 学生录音</value>
    /// <value>3: 学生播放</value>
    /// <value>4: 教师点评录音</value>
    /// <value>5: 教师点评播放</value>
    public int CoordType { get; set; }

    public int Index { get; set; }

    public int PageId { get; set; }
}

public class WorkBookPageDto
{
    /// <summary>
    /// 试卷页id
    /// </summary>
    public string WorkBookPageId { get; set; }

    public int Page { get; set; }

    public int Index { get; set; }

    public int PageId { get; set; }

}