﻿// -- Function: Program.cs
// --- Project: X.PenServer.PenQueue
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2023/10/27 13:13

using System.Text;
using System.Text.Json;
using Autofac;
using Autofac.Extensions.DependencyInjection;
using Masa.BuildingBlocks.Data;
using MassTransit;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NLog;
using NLog.Web;
using X.PenServer.Contracts.Config;
using X.PenServer.Contracts.Queue;
using X.PenServer.Infrastructure;
using X.PenServer.Modules;
using X.PenServer.PenQueue.Consumers;
using X.PenServer.Profiles;
using System.Net;
using System.Text.Encodings.Web;
using System.Text.Json.Serialization;
using Uwoo.Models.Paper;
using X.Elegant.Redis;
using X.Elegant.Redis.Infrastructure.Converters;
using X.PenServer.Contracts.Socket;
using X.PenServer.Interfaces;
using X.PenServer.PenQueue.Implement;
using static X.PenServer.Protos.RpcService;

namespace X.PenServer.PenQueue;

using System.Security.Cryptography;
using static MatrixService;
using static SequentialGuidType;

public static class Program
{
    public static async Task Main(string[] args)
    {
        var nlog = Path.Combine(AppContext.BaseDirectory, "nlog.config");
        var log = LogManager.Setup().LoadConfigurationFromFile(nlog).GetCurrentClassLogger();
        try
        {
            Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
            var builder = Host.CreateDefaultBuilder(args);
            builder.UseServiceProviderFactory(new AutofacServiceProviderFactory()).ConfigureServices
                (
                    (xctx, services) =>
                    {
                        var config = xctx.Configuration;
                        services.AddOptions();
                        services.Configure<PenConfig>(config.GetSection(nameof(PenConfig)));
                        services.Configure<MongoConfig>(config.GetSection(nameof(MongoConfig)));
                        services.Configure<RabbitConfig>(config.GetSection(nameof(RabbitConfig)));
                        services.Configure<MyScriptConfig>(config.GetSection(nameof(MyScriptConfig)));
                        services.Configure<SsoConfig>(config.GetSection(nameof(SsoConfig)));
                        services.AddSingleton
                        (
                            new ParallelOptions
                            {
                                MaxDegreeOfParallelism = Environment.ProcessorCount - 1
                            }
                        );
                        services.AddSingleton<IMongoConfig>(x => x.GetRequiredService<IOptions<MongoConfig>>().Value);
                        services.AddSingleton<IRabbitConfig>(x => x.GetRequiredService<IOptions<RabbitConfig>>().Value);
                        services.AddSingleton<IMyScriptConfig>(x => x.GetRequiredService<IOptions<MyScriptConfig>>().Value);
                        services.AddSingleton<ISsoConfig>(x => x.GetRequiredService<IOptions<SsoConfig>>().Value);
                        services.AddSingleton(x => x.GetRequiredService<IOptions<JsonSerializerOptions>>().Value);
                        services.AddSequentialGuidGenerator(SequentialAsBinary);
                        var workerid = RandomNumberGenerator.GetInt32(1, 1023);
                        Environment.SetEnvironmentVariable("WORKER_ID", workerid.ToString());
                        services.AddSnowflake(x => { x.MaxCallBackTime = 1500; });
                        services.AddHttpClient();
                        services.AddElegantRedisService(x =>
                        {
                            x.PropertyNameCaseInsensitive = true;
                            x.PropertyNamingPolicy = null;
                            x.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull;
                            x.NumberHandling = JsonNumberHandling.AllowReadingFromString;
                            x.Converters.Add(new DateTimeJsonConverter());
                            x.Encoder = JavaScriptEncoder.UnsafeRelaxedJsonEscaping;
                            x.ReadCommentHandling = JsonCommentHandling.Skip;
                        });

                        //添加事件总线和队列
                        services.AddMassTransit
                        (
                            x =>
                            {
                                var mqcfg = config.GetSection(nameof(RabbitConfig)).Get<RabbitConfig>();
                                x.SetKebabCaseEndpointNameFormatter();
                                x.AddDelayedMessageScheduler();
                                x.AddConsumer<PenDotDataConsumer>();
                                x.AddConsumer<PenRecordDataConsumer>();
                                x.AddConsumer<OfflineCorrectPenDotDataConsumer>();
                                x.UsingRabbitMq
                                (
                                    (ctx, cfg) =>
                                    {
                                        cfg.Host
                                        (
                                            mqcfg.Host, mqcfg.Port, mqcfg.VirtualHost, h =>
                                            {
                                                h.Username(mqcfg.UserName);
                                                h.Password(mqcfg.Password);
                                                h.Heartbeat
                                                (
                                                    mqcfg.Heartbeat > 0
                                                        ? TimeSpan.FromSeconds(mqcfg.Heartbeat)
                                                        : TimeSpan.Zero
                                                );
                                                h.ConfigureBatchPublish
                                                (
                                                    c => { c.Enabled = true; }
                                                );
                                                h.PublisherConfirmation = true;
                                            }
                                        );
                                        cfg.PrefetchCount = 64;
                                        cfg.PurgeOnStartup = false;
                                        cfg.AutoDelete = false;
                                        cfg.SingleActiveConsumer = false;
                                        cfg.ConcurrentMessageLimit = 2048;
                                        cfg.Durable = true;
                                        cfg.AutoStart = true;
                                        cfg.QueueExpiration = TimeSpan.FromDays(30);
                                        cfg.OverrideDefaultBusEndpointQueueName("Lucifer");
                                        cfg.UseDelayedRedelivery
                                        (
                                            z => { z.Interval(3, TimeSpan.FromSeconds(20)); }
                                        );

                                        //定义消费队列名称
                                        cfg.ReceiveEndpoint
                                        (
                                            "X-All-PenDot", e => { e.ConfigureConsumer<PenDotDataConsumer>(ctx); }
                                        );
                                        cfg.ReceiveEndpoint
                                        (
                                            "X-All-PenRecord", e => { e.ConfigureConsumer<PenRecordDataConsumer>(ctx); }
                                        );
                                        cfg.ReceiveEndpoint
                                        (
                                            "X-All-Offline-Correct-PenDot", e => { e.ConfigureConsumer<OfflineCorrectPenDotDataConsumer>(ctx); }
                                        );
                                        cfg.ConfigureEndpoints
                                        (
                                            ctx, z =>
                                            {
                                                //点阵笔完成提交和后续任务
                                                z.Include<PenDotFinalData>();
                                                z.Include<TeacherCorrectData>();
                                                z.Include<StudentAnswerStateModel>();
                                                z.Include<Pen8KDotFinalData>();

                                                //实时点位笔迹
                                                z.Include<RealtimeDotModel>();

                                                //实时批改学生
                                                z.Include<RealtimeCorrectStudentModel>();

                                                //练习薄实时点位笔迹
                                                z.Include<RealtimeWorkbookDotModel>();

                                                //英语单元答题卡-教师开始批改学生
                                                z.Include<EnglishUnitMarkingStudentData>();

                                                //英语单元答题卡-客观题作答结果
                                                z.Include<EnglishUnitObjectiveAnswerResultData>();

                                                //英语单元答题卡-主观题批阅结果
                                                z.Include<EnglishUnitSubjectiveAnswerResultData>();

                                                //通用答题卡
                                                z.Include<CardAnswerResultData>();

                                                //互批
                                                z.Include<RealtimeEachCorrectModel>();

                                                // 互动练习簿
												z.Include<InteractiveWorkbookSingleItemAnswer>();

												//单题提交
												z.Include<SingleItemSubmitDto>();

                                                //录音
                                                z.Include<ExamItemMediaEto>();
                                            }, new SnakeCaseEndpointNameFormatter(false)
                                        );
                                    }
                                );
                            }
                        );

                        //配置AutoMapper
                        var automapper = typeof(ProfileBase).Assembly;
                        services.AddAutoMapper(automapper);

                        //配置RPC客户端
                        services.AddGrpcClient<RpcServiceClient>
                        (
                            x =>
                            {
                                var rpc_url = config.GetValue<string>("GrpcServerUrl");
                                x.Address = new Uri(rpc_url);
                            }
                        ).EnableCallContextPropagation
                        (
                            x => { x.SuppressContextNotFoundErrors = true; }
                        ).AddCallCredentials
                        (
                            async (_, metadata, provider) =>
                            {
                                var token_provider = provider.GetRequiredService<ITokenProviderService>();
                                var token = await token_provider.GetTokenAsync();
                                metadata.Add("Authorization", $"Bearer {token}");
                            }
                        ).ConfigureChannel
                        (
                            x =>
                            {
                                x.HttpHandler = new SocketsHttpHandler
                                {
                                    AutomaticDecompression = DecompressionMethods.All,
                                    EnableMultipleHttp2Connections = true,
                                    PooledConnectionIdleTimeout = Timeout.InfiniteTimeSpan,
                                    KeepAlivePingTimeout = TimeSpan.FromSeconds(30),
                                    KeepAlivePingDelay = TimeSpan.FromSeconds(60)
                                };
                                x.DisposeHttpClient = true;
                                x.UnsafeUseInsecureChannelCallCredentials = true;
                            }
                        );
                    }
                )
                .ConfigureLogging
                (
                    x => { x.ClearProviders(); }
                )
                .UseNLog()
                .UseWindowsService()
                .UseConsoleLifetime()
                .ConfigureContainer<ContainerBuilder>
                (
                    (_, x) =>
                    {
                        //注册公用模块
                        x.RegisterModule<AutofacModule>();
                    }
                );
            var app = builder.Build();
            AutofacProvider.Container = app.Services.GetAutofacRoot();
            await InitAsync();
            await app.RunAsync();
        }
        catch (Exception e)
        {
            log.Fatal(e);
        }
        finally
        {
            LogManager.Shutdown();
        }
    }
}