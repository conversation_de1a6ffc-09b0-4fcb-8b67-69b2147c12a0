﻿using System.Text.Json.Serialization;

namespace X.PenServer.Contracts
{
    public class DotDto
    {
        [JsonInclude]
        public ushort X;

        [JsonInclude]
        public ushort Y;

        [JsonInclude]
        public int RawX;

        [JsonInclude]
        public int RawY;

        [<PERSON>sonInclude]
        public uint page;

        [<PERSON>sonInclude]
        public ushort book_no;

        [JsonInclude]
        public uint book_width;

        [JsonInclude]
        public uint book_height;

        [JsonInclude]
        public uint reserved1;

        [JsonInclude]
        public byte type;

        [JsonInclude]
        public ushort pr;

        [JsonInclude]
        public DateTime LogTime { get; set; }
    }
}
