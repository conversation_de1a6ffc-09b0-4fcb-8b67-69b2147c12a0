﻿// -- Function: TestPaperInfoRedis.cs
// --- Project: X.PenServer.UnitTest
// ---- Remark: 
// ---- Author: Lucifer
// ------ Date: 2024/06/04 15:06

using X.PenServer.Interfaces.Redis;
using Xunit.Abstractions;
using Xunit.Microsoft.DependencyInjection.Abstracts;

namespace X.PenServer.UnitTest;

/// <inheritdoc />
public class TestPaperInfoRedis : TestBed<TestFixture>
{
    private readonly IPaperInfoRedisService _redis_service;
    private readonly ITestOutputHelper _test_output_helper;

    /// <inheritdoc />
    public TestPaperInfoRedis(ITestOutputHelper test_output_helper,TestFixture test_fixture) : base(test_output_helper, test_fixture)
    {
        _test_output_helper = test_output_helper;
        _redis_service = test_fixture.GetService<IPaperInfoRedisService>(test_output_helper);
    }

    [Fact]
    public async Task MockAsync()
    {
        const string paperid = "1766049470279098368";
        var workbooks = await _redis_service.HGetAllPaperPageListAsync(paperid);
        foreach (var item in workbooks)
        {
            var workbook = await _redis_service.HGetPaperPageInfoAsync(item.PaperId, item.Page);
            _test_output_helper.WriteLine($"{workbook.ToJson()}{Environment.NewLine}");
            var xpaperid = await _redis_service.HGetPaperIdAsync(item.PageId);
            _test_output_helper.WriteLine($"paperid: {xpaperid}");
            Assert.Equal(paperid, xpaperid);
        }

        var ismedia = await _redis_service.HGetPaperIdIsMediaAsync(paperid);
        Assert.True(ismedia);
        var mark_paperid = "1295633565077213184";
        var markinfos = await _redis_service.SMemberMarkInfoAsync(mark_paperid);
        _test_output_helper.WriteLine($"markinfos: {markinfos.ToJson()}");
        var combina_keys = await _redis_service.HKeysAsync("PaperPageIdCombination");
        var combina_pageid = combina_keys.FirstOrDefault();
        var is_combina = await _redis_service.HGetPaperInfoIsCombinationAsync(Convert.ToInt32(combina_pageid));
        Assert.True(is_combina);
        var combina_infos = await _redis_service.SMemberCombinationMarkRangeInfoListAsync(Convert.ToInt32(combina_pageid));
        _test_output_helper.WriteLine($"range_infos: {combina_infos.ToJson()}");
    }
}