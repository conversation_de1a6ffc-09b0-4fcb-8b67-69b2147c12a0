// -- Function: MD_PaperMacMapping.cs
// --- Project: X.PenServer.Contracts
// ---- Remark: Mac地址与试卷页面映射关系
// ---- Author: System
// ------ Date: 2024/12/19

namespace X.PenServer.Contracts.Paper;

/// <summary>
/// Mac地址与试卷页面映射关系
/// </summary>
public class MD_PaperMacMapping
{
    /// <summary>
    /// 主键ID
    /// </summary>
    public string Id { get; set; }

    /// <summary>
    /// 试卷ID
    /// </summary>
    public string PaperId { get; set; }

    /// <summary>
    /// 页码（对应data.Page）
    /// </summary>
    public int PaperCode { get; set; }

    /// <summary>
    /// Mac地址
    /// </summary>
    public string PMac { get; set; }

    /// <summary>
    /// 试卷页面索引（指向WorkbookPage.Page）
    /// </summary>
    public int PaperPageIndex { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; }

    /// <summary>
    /// 是否删除
    /// </summary>
    public bool Deleted { get; set; } = false;
}
