﻿// -- Function: ItemMatrix.cs
// --- Project: X.PenServer.Infrastructure
// ---- Remark: 
// ---- Author: Lucifer
// ------ Date: 2024/2/20 14:46
namespace X.PenServer.Infrastructure;

using System.Text.Json.Serialization;

/// <summary>
/// 题号矩阵
/// </summary>
public class MatrixItem : MatrixBase
{
    /// <summary>
    /// 选项索引
    /// </summary>
    /// <remarks>
    ///  1.选择题按照顺序从选项A开始依次类推<br />
    ///  2.判断题按照对->错的顺序依次类推
    /// </remarks>
    [JsonInclude]
    [JsonPropertyName(nameof(Index))]
    public int Index { get; set; }

    private string _option;

    /// <summary>
    /// 选项值
    /// </summary>
    [JsonInclude]
    [JsonPropertyName(nameof(Option))]
    public string Option
    {
        get
        {
            _option = ItemType switch
            {
                1 => ((char)(Index + 0x40)).ToString(),
                2 => Index == 1 ? "√" : "×",
                6 => "Record",
                7 => "Play",
                8 => "Eraser",
                _ => Index.ToString()
            };
            return _option;
        }
        private set => _option = value;
    }

    /// <summary>
    /// 类型
    /// </summary>
    /// <remarks>
    /// 1.题目选项(非填空题生效)<br/>
    /// 2.分数框(填空题生效)
    /// </remarks>
    [JsonInclude]
    [JsonPropertyName(nameof(Type))]
    public int Type { get; set; }

    /// <summary>
    /// 题型
    /// </summary>
    /// <remarks>
    /// 1.单选<br />
    /// 2.判断<br />
    /// 3.填空<br />
    /// 4.卷码号<br />
    /// 5.学号<br />
    /// 6.录音<br />
    /// 7.语音<br />
    /// 8.橡皮<br />
    /// </remarks>
    [JsonInclude]
    [JsonPropertyName(nameof(ItemType))]
    public int ItemType { get; set; }

    public string GetItemTypeDesc() => this.ItemType switch
    {
        1 => "选择题",
        2 => "判断题",
        3 => "填空题",
        4 => "卷码号",
        5 => "学号",
        6 => "录音",
        7 => "语音",
        8 => "橡皮",
        _ => ""
    };

}