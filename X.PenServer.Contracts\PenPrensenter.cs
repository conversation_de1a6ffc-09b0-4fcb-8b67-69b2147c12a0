﻿//  -- Function: PenPrensenter.cs
//  --- Project: PenServer
//  ---- Remark: 
//  ---- Author: Lucifer
//  ------ Date: 2023/02/22 14:44

using System.Collections.Concurrent;
using System.Text.Json;
using X.PenServer.Contracts.Queue;

namespace X.PenServer.Contracts;

public class PenPrensenter
{
    public PenPrensenter(ulong id)
    {
        Id = id;
    }

    /// <summary>
    /// Id
    /// </summary>
    public ulong Id { get; }

    /// <summary>
    /// 离线点位数量
    /// </summary>
    public int Count { get; set; }

    /// <summary>
    /// 固件版本号
    /// </summary>
    public string Version { get; set; } = "";

    /// <summary>
    /// 电量: 0-10
    /// </summary>
    public string Battery { get; set; } = "";

    /// <summary>
    /// WIFI名称
    /// </summary>
    public string Ssid { get; set; } = "";

    /// <summary>
    /// 休眠时间
    /// </summary>
    public int StandBy { get; set; }

    /// <summary>
    /// 实时点位
    /// </summary>
    public ConcurrentQueue<PenDot> Dots { get; } = new();

    /// <summary>
    /// 离线点位
    /// </summary>
    public ConcurrentQueue<PenDot> OfflineDots { get; } = new();

    /// <summary>
    /// 添加点位数据
    /// </summary>
    /// <param name="type">1.实时 2.离线</param>
    /// <param name="dot"></param>
    /// <param name="isfix"></param>
    public void AddDots(int type, PenDot dot, bool isfix = false)
    {
        var x = dot.X;
        var y = dot.Y;
        var page = dot.Page;
        var result = FixDot(page, x, y, isfix);
        dot.X = result.Item1;
        dot.Y = result.Item2;

        if (type == 1)
        {
            Dots.Enqueue(dot);
        }
        else
        {
            OfflineDots.Enqueue(dot);
        }
    }

    #region 点位DPI纠偏

    /// <summary>
    /// 点位纠偏
    /// </summary>
    /// <param name="page">页码</param>
    /// <param name="x">X</param>
    /// <param name="y">Y</param>
    /// <param name="isfix"></param>
    /// <returns></returns>
    private static (int, int) FixDot(int page, int x, int y, bool isfix = false)
    {
        var fx = Convert.ToInt32(x / 3);
        var fy = Convert.ToInt32(y / 3);
        if (page is not (5 or 6))
        {
            return (fx, fy);
        }

        var xfx = fx;
        var xfy = fy;
        if (isfix)
        {
            if (page == 5)
            {
                fx = Convert.ToInt32((x - 36) / 3);
                fy = Convert.ToInt32((y + 36) / 3);
            }
            else
            {
                fx = Convert.ToInt32((x + 9) / 3);
                fy = Convert.ToInt32((y - 30) / 3);
            }
        }

        if (fx <= 0)
        {
            fx = xfx;
        }

        if (fy <= 0)
        {
            fy = xfy;
        }

        return (fx, fy);
    }

    #endregion

    #region Overrides of Object

    /// <inheritdoc />
    public override string ToString()
    {
        return JsonSerializer.Serialize(this);
    }

    #endregion
}