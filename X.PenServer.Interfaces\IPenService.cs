﻿//  -- Function: IPenService.cs
//  --- Project: PenServer
//  ---- Remark: 
//  ---- Author: Lucifer
//  ------ Date: 2023/02/22 13:28

using X.PenServer.Interfaces.Lifecycle;

namespace X.PenServer.Interfaces;

using System.Collections.Concurrent;
using Contracts;

/// <summary>
/// 点阵笔服务
/// </summary>
public interface IPenService : ISingletonService
{
    /// <summary>
    /// 开启服务
    /// </summary>
    void Start();

    /// <summary>
    /// 停止服务
    /// </summary>
    void Stop();

    /// <summary>
    /// 获取当前连接列表
    /// </summary>
    /// <returns></returns>
    ConcurrentDictionary<ulong, PenPrensenter> GetCurrentList();
}