﻿// -- Function: RealtimeWorkbookDotConsumer.cs
// --- Project: X.PenServer.Sockets
// ---- Remark: 
// ---- Author: Lucifer
// ------ Date: 2024/3/21 13:51

using System.Collections.Immutable;

namespace X.PenServer.Sockets.Consumers;

using System.Net.WebSockets;
using MassTransit;
using Contracts.Realtime;
using Contracts.Socket;
using Infrastructure;
using static Middlewares.MiddlewareExtensions;
using static Middlewares.RealtimePenSocketMiddleware;

/// <summary>
/// 接收练习薄实时点位消费队列
/// </summary>

// ReSharper disable once ClassNeverInstantiated.Global
public class RealtimeWorkbookDotConsumer : IConsumer<Batch<RealtimeWorkbookDotModel>>
{
	private readonly ParallelOptions _parallel_options;

	public RealtimeWorkbookDotConsumer(ParallelOptions parallel_options)
	{
		_parallel_options = parallel_options;
	}

	/// <inheritdoc />
	public async Task Consume(ConsumeContext<Batch<RealtimeWorkbookDotModel>> context)
	{
		//接收实时点位
		foreach (var item in context.Message)
		{
			var pendata = item.Message;

			//学生笔迹
			var wids = GetStudentWorkbookClients(pendata.PaperId, pendata.StudentId, pendata.ItemId);
			if (wids is null || wids.IsEmpty)
			{
				continue;
			}

			var sockets = ImmutableHashSet.Create<WebSocket>();
			sockets = wids.Select(GetSocketClient).Where(socket => socket is
			{
				State: WebSocketState.Open
			}).Aggregate(sockets, (current, socket) => current.Add(socket));
			if (sockets.Count <= 0)
			{
				continue;
			}

			await Parallel.ForEachAsync
			(
				sockets, _parallel_options, async (x, xtoken) =>
				{
					var xdata = new SendData
					{
						Type = "207",
						Content = pendata.ToJsonString()
					};
					await SendAsync(x, xdata.ToJsonString(), xtoken).ConfigureAwait(false);
				}
			);
		}

		await context.ConsumeCompleted.ConfigureAwait(false);
	}
}