﻿//  -- Function: PenBatteryData.cs
//  --- Project: PenServer
//  ---- Remark: 
//  ---- Author: Lucifer
//  ------ Date: 2023/03/31 15:12

using System.Text.Json.Serialization;

namespace X.PenServer.Contracts;

/// <summary>
/// 电量数据
/// </summary>
[Serializable]
public class PenBatteryData
{
	/// <summary>
	/// 电量
	/// </summary>
	[JsonPropertyName(nameof(Battery))]
	[JsonInclude]
	public int Battery { get; set; }

	/// <summary>
	/// 状态
	/// </summary>
	[JsonPropertyName(nameof(Status))]
	[JsonInclude]
	public string Status { get; set; }
}