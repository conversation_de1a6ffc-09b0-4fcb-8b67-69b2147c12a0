﻿// -- Function: RealtimePenService.cs
// --- Project: X.PenServer.Sockets
// ---- Remark: 
// ---- Author: Lucifer
// ------ Date: 2023/10/27 13:13

using X.PenServer.Contracts.Realtime;
using X.PenServer.Interfaces;

namespace X.PenServer.Sockets.Implement;

/// <inheritdoc />
public class RealtimePenService : IRealtimePenService
{
    private readonly IDapperService _dapper;

    public RealtimePenService(IDapperService dapper)
    {
        _dapper = dapper;
    }

    /// <inheritdoc />
    public async Task<List<UserInfo>> GetUserList(string classid, string teacherid)
    {
        const string sql = """
                           SELECT [s].[Id] AS [UserId],
                                  [m].[state] AS [State],
                                  [m].[Ssid],
                                  [m].[PMac],
                                  [m].[Battery],
                                  [m].[UserType]
                           FROM [dbo].[Exam_Student] AS s
                               LEFT JOIN [dbo].[MD_UserPenMapping] AS m
                                   ON [m].[UserId] = [s].[Id]
                           WHERE [s].[Deleted] = 0
                                 AND [m].[Deleted] = 0
                                 AND [s].[ClassId] = @classid
                                 AND [m].[state] = 1
                           UNION
                           (SELECT TOP (1)
                                   [b].[Id] AS [UserId],
                                   [m].[state] AS [State],
                                   [m].[Ssid],
                                   [m].[PMac],
                                   [m].[Battery],
                                   [m].[UserType]
                            FROM [dbo].[Base_User] AS b
                                LEFT JOIN [dbo].[MD_UserPenMapping] AS m
                                    ON [b].[Id] = [m].[UserId]
                            WHERE [m].[UserId] = @teacherid);
                           """;
        var xlist = await _dapper.QueryListAsync<UserInfo>
        (
            sql, new
            {
                classid,
                teacherid
            }
        );
        var list = xlist.ToList();
        return list;
    }
}