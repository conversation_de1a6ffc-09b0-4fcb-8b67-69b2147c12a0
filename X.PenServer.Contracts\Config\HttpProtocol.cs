﻿// -- Function: HttpProtocol.cs
// --- Project: X.PenServer.Contracts
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2024/3/26 11:9
namespace X.PenServer.Contracts.Config;

using System.Runtime.Serialization;

/// <summary>
/// http协议
/// </summary>
[Flags]
public enum HttpProtocol
{
	/// <summary>
	/// None
	/// </summary>
	[EnumMember(Value = "None")]
	None = 0,

	/// <summary>
	/// Http1
	/// </summary>
	[EnumMember(Value = "Http1")]
	Http1 = 1,

	/// <summary>
	/// Http2
	/// </summary>
	[EnumMember(Value = "Http2")]
	Http2 = 2,

	/// <summary>
	/// Http1AndHttp2
	/// </summary>
	[EnumMember(Value = "Http1AndHttp2")]
	Http1AndHttp2 = Http2 | Http1
}