﻿using X.Elegant.Redis.Infrastructure;
using X.PenServer.Interfaces.Lifecycle;
using X.PenServer.Models.Mongo;

namespace X.PenServer.Interfaces.Redis
{
    public interface IUserInfoRedisService : IRedisService, ISingletonService
    {
        /// <summary>
        /// 获取学生信息
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        Task<StudentInfo> GetStudentInfoAsync(string userId);

        /// <summary>
        /// 根据班级Id、学号获取学生信息
        /// </summary>
        /// <param name="classId"></param>
        /// <param name="studentNo"></param>
        /// <returns></returns>
        Task<StudentInfo> GetStudentInfoAsync(string classId,string studentNo);
    }
}
