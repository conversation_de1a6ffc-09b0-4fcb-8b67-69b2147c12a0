﻿// -- Function: RealtimeCorrectStudentModel.cs
// --- Project: Uwoo.ContractModels
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2023/10/09 14:58

using System.Text.Json.Serialization;

namespace X.PenServer.Contracts.Socket;

/// <summary>
/// 正在批改学生
/// </summary>
public class RealtimeCorrectStudentModel : RealtimeMessageBase
{
	/// <summary>
	/// 状态
	/// </summary>
	/// <remarks>0:取消 1:批改</remarks>
	[JsonPropertyName("state")]
	[JsonInclude]
	public int State { get; set; }
}