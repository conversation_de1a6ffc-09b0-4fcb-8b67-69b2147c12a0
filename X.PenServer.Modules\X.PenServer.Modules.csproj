<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>disable</Nullable>
        <ErrorOnDuplicatePublishOutputFiles>false</ErrorOnDuplicatePublishOutputFiles>
        <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
        <AppendRuntimeIdentifierToOutputPath>false</AppendRuntimeIdentifierToOutputPath>
        <!--auto version start-->
        <Deterministic>false</Deterministic>
        <GenerateAssemblyFileVersionAttribute>false</GenerateAssemblyFileVersionAttribute>
        <GenerateAssemblyInformationalVersionAttribute>false</GenerateAssemblyInformationalVersionAttribute>
        <AssemblyVersion>1.0.*</AssemblyVersion>
        <!--auto version end-->
    </PropertyGroup>
    <PropertyGroup>
        <Copyright>Copyright © Lucifer. All rights reserved.</Copyright>
        <PackageId>X.PenServer.Modules</PackageId>
        <PackageProjectUrl>https://github.com/X-Lucifer</PackageProjectUrl>
        <RepositoryUrl>https://github.com/X-Lucifer</RepositoryUrl>
        <Company>X Lucifer</Company>
        <Authors>Lucifer</Authors>
        <AutoIncrementPackageRevision>true</AutoIncrementPackageRevision>
    </PropertyGroup>
    <ItemGroup>
      <ProjectReference Include="..\X.PenServer.Contracts\X.PenServer.Contracts.csproj" />
      <ProjectReference Include="..\X.PenServer.Infrastructure\X.PenServer.Infrastructure.csproj" />
      <ProjectReference Include="..\X.PenServer.Interfaces\X.PenServer.Interfaces.csproj" />
      <ProjectReference Include="..\X.PenServer.Models\X.PenServer.Models.csproj" />
    </ItemGroup>
    <ItemGroup>
        <FrameworkReference Include="Microsoft.AspNetCore.App" />
    </ItemGroup>
    <ItemGroup>
      <PackageReference Include="Microsoft.NETCore.Platforms" Version="7.0.4" />
    </ItemGroup>
</Project>
