﻿using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Bson;
using System.Text.Json.Serialization;

namespace X.PenServer.Models.Mongo
{
    /// <summary>
    /// <para><AUTHOR> <PERSON></para>
    /// <para>@Date   2025-05-06 14:32</para>
    /// <para>@Description 点阵笔数据8K</para>
    /// </summary>
    public class Pen8KLog : MongoBaseModel
    {
        [BsonElement(nameof(Page))]
        [BsonRepresentation(BsonType.Int32)]
        public int Page { get; set; }

        [BsonElement(nameof(PageId))]
        [BsonRepresentation(BsonType.Int32)]
        public int PageId { get; set; }

        [BsonElement(nameof(Mac))]
        [BsonRepresentation(BsonType.String)]
        public string Mac { get; set; }

        [BsonElement(nameof(UserId))]
        [BsonRepresentation(BsonType.String)]
        public string UserId { get; set; }

        [BsonElement(nameof(Dots))]
        [JsonPropertyName(nameof(Dots))]
        public List<DotBase> Dots { get; set; } = [];
    }
}
