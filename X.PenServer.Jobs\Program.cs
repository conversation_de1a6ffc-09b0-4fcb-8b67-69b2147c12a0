﻿// -- Function: Program.cs
// --- Project: X.PenServer.Jobs
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2024/04/22 13:13

using Autofac;
using Autofac.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using NLog;
using NLog.Web;
using X.PenServer.Infrastructure;
using X.PenServer.Jobs.Implement;
using X.PenServer.Modules;

namespace X.PenServer.Jobs;

public static class Program
{
    public static async Task Main(string[] args)
    {
        var nlog = Path.Combine(AppContext.BaseDirectory, "nlog.config");
        var log = LogManager.Setup().LoadConfigurationFromFile(nlog).GetCurrentClassLogger();
        try
        {
            var builder = Host.CreateDefaultBuilder(args);
            builder.UseServiceProviderFactory(new AutofacServiceProviderFactory())
                .UseAutofac()
                .ConfigureServices(x =>
                {
                    x.AddApplication<AutoJobModule>();
                }).UseNLog()
                .UseWindowsService()
                .UseConsoleLifetime()
                .ConfigureContainer<ContainerBuilder>(x =>
                {
                    //register autofac module
                    x.RegisterModule<AutofacModule>();
                });
            var app = builder.Build();
            AutofacProvider.Container = app.Services.GetAutofacRoot();
            await app.RunAsync();
        }
        catch (Exception e)
        {
            log.Fatal(e, "error: {error}", e.Message);
        }
        finally
        {
            LogManager.Shutdown();
        }
    }
}