// -- Function: DotQueueService.cs
// --- Project: X.PenServer.PenQueue
// ---- Remark: 
// ---- Author: Lucifer
// ------ Date: 2023/10/30 10:30

using Uwoo.Models.Paper;
using X.PenServer.Interfaces.Redis;

namespace X.PenServer.PenQueue.Implement;

using AutoMapper;
using Contracts.Paper;
using Contracts.Queue;
using Contracts.Redis;
using Contracts.Socket;
using Infrastructure;
using Interfaces;
using Interfaces.Mongo;
using Masa.BuildingBlocks.Data;
using MassTransit;
using Microsoft.Extensions.Logging;
using Models.Mongo;
using MongoDB.Driver;
using Protos;
using System;
using System.Runtime.Intrinsics.X86;
using System.Numerics;
using System.Text;
using System.Text.RegularExpressions;
using Uwoo.Util;
using Uwoo.Util.Doubao;
//using X.PenServer.Services.Redis;
using static MatrixService;
using static Protos.RpcService;
using static System.Runtime.InteropServices.JavaScript.JSType;
using TeacherInfo = Models.Mongo.TeacherInfo;

/// <inheritdoc />
public class DotQueueService : IDotQueueService
{
    private readonly ILogger<DotQueueService> _log;
    private readonly IDapperService _dapper_service;
    private readonly IPublishEndpoint _publisher;
    private readonly IMapper _mapper;
    private readonly IPenMappingRedisService _pen_mapping_redis_service;
    private readonly ITeacherCorrectRedisService _teacher_correct_redis_service;
    private readonly IPaperLogRedisService _paper_log_redis_service;
    private readonly IPaperInfoRedisService _paper_info_redis_service;
    private readonly ITeacherInfoService _teacher_info_service;
    private readonly IStudentInfoService _student_info_service;
    private readonly ITeacherPenLogService _teacher_pen_log_service;
    private readonly IPenLogService _pen_log_service;
    private readonly ICorrectPenLogService _correct_pen_log_service;
    private readonly ICardPenLogService _card_pen_log_service;
    private readonly IWorkbookPenLogService _workbook_pen_log_service;
    private readonly IMyScriptClient _my_script_client;
    private readonly ISnowflakeGenerator _snowflake;
    private readonly RpcServiceClient _rpc_service_client;
    private readonly ISingleItemPenLogService _single_item_pen_log_service;
    private readonly ISequentialGuidGenerator _guid_generator;
    private readonly IUserInfoRedisService _userInfoRedisService;
    private readonly IStudentAnswerPaperRedisService _studentAnswerPaperRedisService;
    private readonly ICommonRedisService _commonRedisService;
    private readonly ICanvasService _canvasService;
    private readonly IDoubaoChatCompletionsService _doubaoChatCompletionsService;
    private readonly IDoubaoHuaweiOBSUrlRedisService _doubaoHuaweiOBSUrlRedisService;
    private readonly IPen8KLogService _pen8KLogService;
    private readonly ICorrectPen8KLogService _correctPen8KLogService;

    private readonly List<int> _paper_num_list = [5, 24,19,20];
    readonly static double Middle8K_X_Position = 1531.49;

    public DotQueueService(ILogger<DotQueueService> log, IDapperService dapper_service, IPublishEndpoint publisher, IMapper mapper, IPenMappingRedisService pen_mapping_redis_service, ITeacherCorrectRedisService teacher_correct_redis_service, IPaperLogRedisService paper_log_redis_service, IPaperInfoRedisService paper_info_redis_service, ITeacherInfoService teacher_info_service, IStudentInfoService student_info_service, ITeacherPenLogService teacher_pen_log_service, IPenLogService pen_log_service,
        ICorrectPenLogService correct_pen_log_service, ICardPenLogService card_pen_log_service, IWorkbookPenLogService workbook_pen_log_service, IMyScriptClient my_script_client, ISnowflakeGenerator snowflake, RpcServiceClient rpc_service_client, ISingleItemPenLogService single_item_pen_log_service, ISequentialGuidGenerator guid_generator
        , IUserInfoRedisService userInfoRedisService
        , IStudentAnswerPaperRedisService studentAnswerPaperRedisService
        , ICommonRedisService commonRedisService
        , ICanvasService canvasService
        , IDoubaoChatCompletionsService doubaoChatCompletionsService
        , IDoubaoHuaweiOBSUrlRedisService doubaoHuaweiOBSUrlRedisService
        , IPen8KLogService pen8KLogService
        , ICorrectPen8KLogService correctPen8KLogService
        )
    {
        _log = log;
        _dapper_service = dapper_service;
        _publisher = publisher;
        _mapper = mapper;
        _pen_mapping_redis_service = pen_mapping_redis_service;
        _teacher_correct_redis_service = teacher_correct_redis_service;
        _paper_log_redis_service = paper_log_redis_service;
        _paper_info_redis_service = paper_info_redis_service;
        _teacher_info_service = teacher_info_service;
        _student_info_service = student_info_service;
        _teacher_pen_log_service = teacher_pen_log_service;
        _pen_log_service = pen_log_service;
        _correct_pen_log_service = correct_pen_log_service;
        _card_pen_log_service = card_pen_log_service;
        _workbook_pen_log_service = workbook_pen_log_service;
        _my_script_client = my_script_client;
        _snowflake = snowflake;
        _rpc_service_client = rpc_service_client;
        _single_item_pen_log_service = single_item_pen_log_service;
        _guid_generator = guid_generator;

        _userInfoRedisService=userInfoRedisService;
        _studentAnswerPaperRedisService=studentAnswerPaperRedisService;

        _commonRedisService=commonRedisService;

        _canvasService =canvasService;
        _doubaoChatCompletionsService=doubaoChatCompletionsService;
        _doubaoHuaweiOBSUrlRedisService=doubaoHuaweiOBSUrlRedisService;

        _pen8KLogService=pen8KLogService;
        _correctPen8KLogService=correctPen8KLogService;
    }

    #region Implementation of ProcessAsync

    /// <inheritdoc />
    public async Task ProcessAsync(PenDotData data)
    {
        var userid = await GetUserIdAsync(mac: data.Mac, data: data);

        var raw_data = data.ToJsonString();
        //点阵笔还未绑定用户
        if (string.IsNullOrWhiteSpace(userid))
        {
            _log.LogError("点阵笔未绑定-实时数据: {msg}", raw_data);
            return;
        }

        //获取点阵笔用户类型
        var usertype = await _pen_mapping_redis_service.HGetPenRoleAsync(data.Mac);
        if (usertype == 0)
        {
            data.UserId = userid;
            await StudentPenDot(data);
        }
        else
        {
            #region Teacher

            var teacher_info = await GetTeacher(userid);
            if (teacher_info == null)
            {
                _log.LogInformation("未查询到绑定教师信息: {msg}", raw_data);
                return;
            }

            var dots = data.Dots;

            //学号
            var studentno = "";

            //页码: 1,3,5,7
            var pageno = 0;

            //当前批阅班级
            var correct_class_id = await _teacher_correct_redis_service.HGetCorrectClassIdAsync(teacher_info.UserId);
            if (string.IsNullOrWhiteSpace(correct_class_id))
            {
                _log.LogInformation("未查询到当前正在批阅的班级: {msg}", raw_data);
                return;
            }

            var correct = await _paper_log_redis_service.GetCurrentPaperIdAsync(correct_class_id);
            var avg_x = Convert.ToSingle(dots.Average(x => x.X));
            var avg_y = Convert.ToSingle(dots.Average(x => x.Y));
            var isdb = false;
            var commonA4IsFirst = true;
            #region 页码处理

            switch (data.Page)
            {
                case 5 or 6:
                    {
                        if (data.Page == 5)
                        {
                            studentno = GetCheckedStudentNo(avg_y, avg_x);

                            //未获取到学号, 则代表在其他区域书写
                            if (string.IsNullOrWhiteSpace(studentno))
                            {
                                //尝试获取页码, 如果获取不到, 则是在批改题目
                                pageno = GetPageNo(avg_x, avg_y);
                                if (pageno > 0)
                                {
                                    //勾选页码区域
                                    var studentid = await _teacher_correct_redis_service.HGetCorrectStudentAsync(userid) ?? "";
                                    if (correct != null)
                                    {
                                        //设置当前教师批改的学生和具体页面缓存
                                        await _teacher_correct_redis_service.SetCorrectStudentAsync
                                        (
                                            teacher_info.UserId, correct.PaperId,
                                            correct.ClassId, studentid, pageno
                                        ).ConfigureAwait(false);
                                    }
                                    else
                                    {
                                        _log.LogInformation("[当前批阅] mac:{mac} teacherId:{userid} classid:{classid} studentid:{studentid} 没有获取到当前一键赋码的试卷", data.Mac, teacher_info.UserId, correct_class_id, studentid);
                                    }
                                }
                            }
                        }
                        break;
                    }
                case 10:
                    {
                        studentno = GetTagNo(avg_x, avg_y);
                        break;
                    }
                case 24 or 25 when correct == null:
                    {
                        _log.LogError("未获取到教师正在批阅的英语单元答题卡试卷信息: {error}", raw_data);
                        return;
                    }
                case 24 or 25:
                    {
                        if (data.Page == 24)
                        {
                            var studentno_region = LocateStudentNoRegion(avg_x, avg_y, data.Page);
                            if (studentno_region != null)
                            {
                                //正确获取到学号
                                studentno = studentno_region.Option;
                                var zstudent = await GetStudent(correct_class_id, studentno);
                                if (zstudent == null)
                                {
                                    _log.LogError("未获取到教师正在批阅的学生信息: {error}", raw_data);
                                    return;
                                }

                                //推送正在批阅学生通知
                                var mark_data = new EnglishUnitMarkingStudentData
                                {
                                    PaperId = correct.PaperId,
                                    TeacherId = teacher_info.UserId,
                                    StudentNo = zstudent.StudentNo,
                                    StudentId = zstudent.UserId
                                };

                                //推送通知
                                await _publisher.Publish(mark_data).ConfigureAwait(false);

                                //缓存当前正在批阅的学生
                                await _teacher_correct_redis_service.SetCorrectStudentAsync
                                (
                                    teacher_info.UserId,
                                    correct.PaperId,
                                    correct.ClassId,
                                    zstudent.UserId,
                                    data.Page == 24 ? 1 : 2
                                );
                                await _teacher_correct_redis_service.HSetCorrectStudentAsync(teacher_info.UserId, zstudent.UserId);
                                pageno = 1;
                                break;
                            }
                        }

                        var correcting_student = await _teacher_correct_redis_service.GetCorrectStudentAsync(teacher_info.UserId);
                        if (correcting_student == null)
                        {
                            _log.LogError("未查询到当前正在批阅的学生信息: {error}", raw_data);
                            return;
                        }

                        var xstudent_info = await _student_info_service.GetStudentInfoAsync(correcting_student.StudentId);
                        if (xstudent_info == null)
                        {
                            _log.LogError("未查询到当前正在批阅的学生信息: {error}", raw_data);
                            return;
                        }

                        //其他区域代表正在批阅中
                        var correct_item_region = EnglishUnitLocateSubjectiveItemRegion(avg_x, avg_y, data.Page);

                        //获取试卷信息
                        if (correct_item_region == null || correct_item_region.Item == null)
                        {
                            _log.LogError("未定位到当前批阅有效区域: {error}", raw_data);
                            return;
                        }

                        //当前是否命中分数框
                        var ischeck = correct_item_region.Item.Type == 2;
                        if (!ischeck)
                        {
                            return;
                        }

                        //推送教师批阅英语答题卡通知
                        var english_subjective_result_data = new EnglishUnitSubjectiveAnswerResultData
                        {
                            Page = data.Page,
                            Mac = data.Mac,
                            PaperId = correcting_student.PaperId,
                            TeacherId = teacher_info.UserId,
                            ItemNo = correct_item_region.ItemNo,
                            Result = correct_item_region.IsMultiScore ? correct_item_region.Item.Option : "×",
                            StudentId = xstudent_info.UserId,
                            StudentNo = xstudent_info.StudentNo,
                            SubItemNo = correct_item_region.Item.Index,
                            PageId = data.PageId,
                            Score = correct_item_region.Score,
                            IsMultiScore = correct_item_region.IsMultiScore
                        };
                        await _publisher.Publish(english_subjective_result_data).ConfigureAwait(false);
                        return;
                    }
                case 19 or 20:
                    if (data.Page == 19)
                    {
                        studentno = GetCheckedStudentNoFor8K(avg_y, avg_x);
                    }
                  break;
                case >= 2001 and <= 2500 when correct == null:
                    {
                        _log.LogError("未获取到教师正在批阅的试卷信息: {error}", raw_data);
                        return;
                    }
                case >= 2001 and <= 2500:
                case >= 5001 and <= 5200:
                    // 根据试卷Id correct.PaperId，correct.ClassId  和data.Page 查询 MD_PaperMacMapping 表中 对应的学生信息，得到学号
                    string querySql= @"select s.StudentNo,pmm.PaperPageIndex as PageNo from MD_PaperMacMapping(nolock) as pmm 
                    join Exam_Student(nolock) as s on s.Id = pmm.UserId
where s.ClassId = @classId and s.Deleted = 0 and pmm.PaperCode = @paperCode and pmm.PaperId = @paperId";
                    // 先从缓存查询学生作答页面映射信息
                    var cacheKey = $"StudentDoPageMapping|{correct.ClassId}|{correct.PaperId}|{data.Page}";
                    var studentDoInfo = await _teacher_correct_redis_service.GetAsync<StudentDoPageMapping>(cacheKey);

                    if (studentDoInfo == null)
                    {
                        // 缓存中没有，查询数据库
                        studentDoInfo = await _dapper_service.QueryFirstAsync<StudentDoPageMapping>(querySql, new { classId = correct.ClassId, paperId = correct.PaperId, paperCode = data.Page });

                        if (studentDoInfo != null)
                        {
                            // 缓存查询结果，缓存2小时
                            await _teacher_correct_redis_service.SetAsync(cacheKey, studentDoInfo, TimeSpan.FromHours(2));
                        }
                        commonA4IsFirst = true;
                    }
                    else
                    {
                        commonA4IsFirst = false;
                    }
                    studentno = studentDoInfo.StudentNo;
                    pageno = studentDoInfo.PageNo;
                    break;
                case > 10000:
                    {
                        data.PageId = data.Page;
                        break;
                    }
            }

            #endregion

            _log.LogInformation("[当前批阅] mac:{mac} studentno:{studentno} teacherId:{userid} classid:{classid} data:{data}", data.Mac, studentno, teacher_info.UserId, correct_class_id, data.ToJsonString());

            if (string.IsNullOrWhiteSpace(studentno) && pageno <= 0)
            {
                //未获取到学号,未获取到页码,代表在其他区域书写
                isdb = true;
            }

            if (isdb)
            {
                if (data.Page > 10000)
                {
                    //练习册-获取到当前批阅的学生id
                    var studentid = await _teacher_correct_redis_service.HGetCorrectStudentAsync(teacher_info.UserId);

                    //尝试从mongodb获取学生
                    var student = await GetStudent(studentid);
                    if (student == null)
                    {
                        _log.LogInformation("mac:{mac} 学生信息不存在: {studentid}", data.Mac, studentid);
                        return;
                    }

                    //查询是否为组合页面
                    var is_combination = await _paper_info_redis_service.HGetPaperInfoIsCombinationAsync(data.Page);
                    string paperid;
                    if (!is_combination)
                    {
                        //获取普通的试卷信息
                        paperid = await _paper_info_redis_service.HGetPaperIdAsync(data.Page);
                        if (string.IsNullOrWhiteSpace(paperid))
                        {
                            //缓存中没有试卷信息, 从数据库读取
                            const string workpage_sql = """
                                                        SELECT TOP (1) PaperId
                                                        FROM WorkbookPage
                                                        WHERE PaperType = 2
                                                              AND PageId = @pageid;
                                                        """;
                            var workpage = await _dapper_service.QueryFirstAsync<WorkbookPage>
                            (
                                workpage_sql, new
                                {
                                    pageid = data.PageId
                                }
                            );
                            if (workpage != null)
                            {
                                paperid = workpage.PaperId;

                                //缓存试卷信息
                                await _paper_info_redis_service.HSetPaperIdAsync(data.PageId, paperid);
                            }
                        }

                        if (string.IsNullOrWhiteSpace(paperid))
                        {
                            _log.LogInformation("mac:{mac} 未查询到试卷信息 , {msg}", data.Mac, raw_data);
                            return;
                        }
                    }
                    else
                    {
                        var combination_result = await GetCombinationPaperId(data.Dots, data.PageId);
                        if (string.IsNullOrWhiteSpace(combination_result.Item1) || combination_result.Item2 <= 0)
                        {
                            _log.LogInformation("mac:{mac} 未查询到组合页试卷信息 , {msg}", data.Mac, raw_data);
                            return;
                        }

                        paperid = combination_result.Item1;
                    }

                    if (string.IsNullOrWhiteSpace(paperid))
                    {
                        _log.LogInformation("mac:{mac} 未查询到试卷信息 , {msg}", data.Mac, raw_data);
                        return;
                    }

                    //获取试卷页面列表
                    var workpage_list = await _paper_info_redis_service.HGetAllPaperPageListAsync(paperid);
                    if (workpage_list is not
                        {
                            Count: > 0
                        })
                    {
                        const string sql = """
                                           SELECT *
                                           FROM [dbo].[WorkbookPage]
                                           WHERE [PaperType] = 2
                                                 AND [PaperId] = @paperid;
                                           """;
                        var workpages = await _dapper_service.QueryListAsync<WorkbookPage>
                        (
                            sql, new
                            {
                                paperid
                            }
                        );
                        if (workpages != null && workpages.Any())
                        {
                            workpage_list = workpages.ToList();
                            foreach (var item in workpage_list)
                            {
                                //缓存试卷页码信息
                                await _paper_info_redis_service.HSetPaperPageInfoAsync(item.PaperId, item.Page, item);
                            }
                        }
                    }

                    if (workpage_list is not
                        {
                            Count: > 0
                        })
                    {
                        _log.LogInformation("mac:{mac} 未查询到试卷信息 , {msg}", data.Mac, raw_data);
                        return;
                    }

                    var current_workpage = workpage_list.FirstOrDefault(x => x.PageId == data.PageId);
                    if (current_workpage == null)
                    {
                        _log.LogInformation("mac:{mac} 未查询到试卷信息 , {msg}", data.Mac, raw_data);
                        return;
                    }

                    var current_correct = await _teacher_correct_redis_service.GetCorrectStudentAsync(teacher_info.UserId);

                    //如果是当前页首次批阅或者已经翻页,更新正在批阅缓存
                    if (current_workpage.PageId != data.PageId || current_correct == null)
                    {
                        await _teacher_correct_redis_service.SetCorrectStudentAsync
                        (
                            teacher_info.UserId, paperid, correct_class_id,
                            studentid,
                            current_workpage.Page
                        );
                    }

                    _log.LogInformation("mac:{mac} 练习册教师笔迹处理 , {msg}", data.Mac, raw_data);

                    var dot = new TeacherPenDotData
                    {
                        UserId = student.UserId,
                        TeacherId = teacher_info.UserId,
                        Mac = data.Mac,
                        Dots = data.Dots,
                        Time = data.Time,
                        RequestId = data.RequestId,
                        Page = data.Page,
                        PageId = data.PageId,
                        CurrentPageNo = current_workpage.Page
                    };
                    await TeacherPenDot(dot, student.SchoolId, paperid, correct_class_id);
                }
                else
                {
                    if (correct == null)
                    {
                        _log.LogInformation("mac:{mac} 没有找到当前一键赋码的试卷", data.Mac);
                    }

                    //获取当前教师正在批改的学生
                    var xstudent = await _teacher_correct_redis_service.GetCorrectStudentAsync(teacher_info.UserId);

                    if (xstudent==null)
                    {
                        _log.LogInformation("mac:{mac} 没有找到当前正在批改的学生和试卷信息", data.Mac);
                    }

                    if (xstudent != null && correct != null)
                    {
                        _log.LogInformation("教师批改：{}，查询学生信息: {}", teacher_info.UserId, xstudent.StudentId);
                        //尝试从mongodb获取学生
                        var student = await GetStudent(xstudent.StudentId);
                        if (student == null)
                        {
                            _log.LogInformation("mac:{mac} 学生信息不存在: {studentid}", data.Mac, xstudent.StudentId);
                            return;
                        }

                        _log.LogInformation("mac:{mac} 教师批改: {teacherId} 学生Id:{studentId} Page:{page}", data.Mac, teacher_info.UserId, xstudent.StudentId, data.Page);

                        //获取批改的页码
                        var xpageno = xstudent.Page;
                        if (data.Page is 5 or 6)
                        {
                            if (data.Page == 6)
                            {
                                //自动翻页
                                xpageno += 1;
                            }

                            var wpage = await _paper_info_redis_service.HGetPaperPageInfoAsync(correct.PaperId, xpageno) ?? await GetWorkbookPage(correct.PaperId, xpageno);
                            if (wpage == null)
                            {
                                _log.LogInformation("mac:{mac} paperid: {paperid} , 未找到试卷页面信息", data.Mac, correct.PaperId);
                                return;
                            }

                            //普通铺码或练习册这两个页面是一样的. 卷码纸不同!
                            var dot = new TeacherPenDotData
                            {
                                UserId = xstudent.StudentId,
                                TeacherId = teacher_info.UserId,
                                Mac = data.Mac,
                                Dots = data.Dots,
                                Time = data.Time,
                                RequestId = data.RequestId,
                                Page = data.Page,
                                PageId = data.Page > 1000 ? data.PageId : wpage.PageId,
                                CurrentPageNo = wpage.Page
                            };
                            await TeacherPenDot(dot, student.SchoolId, correct.PaperId, correct.ClassId);
                        }
                    }
                }
            }
            else
            {
                // 通用A4纸：先处理批改关系，再处理笔迹
                if((data.Page >= 2000 && data.Page <= 2500) || (data.Page >= 5000 && data.Page <= 5200))
                {
                    // 处理教师笔迹（第一次和非第一次都需要）
                    //获取当前教师正在批改的学生
                    if (!commonA4IsFirst)
                    {
                        var checkStudent = await _teacher_correct_redis_service.GetCorrectStudentAsync(teacher_info.UserId);
                        if (checkStudent == null)
                        {
                            _log.LogInformation("mac:{mac} 没有找到当前正在批改的学生和试卷信息", data.Mac);
                            return;
                        }
                        if (checkStudent.StudentNo != studentno)
                        {
                            commonA4IsFirst = true;
                        }
                    }
                  

                    // 第一次批改：先执行推送和缓存逻辑
                    if (commonA4IsFirst)
                    {
                        // 推送正在批改的学生信息、缓存教师正在批阅的学生等
                        var current_student_info = await GetStudent(correct_class_id, studentno);
                        if (current_student_info == null)
                        {
                            return;
                        }

                        _log.LogInformation("mac:{mac} 推送批改学生信息，学号：{studentNo} StudentId:{studentId}", data.Mac, studentno, current_student_info.UserId);

                        //推送正在批改的学生
                        var correct_student_data = new RealtimeCorrectStudentModel
                        {
                            State = 1,
                            ClassId = current_student_info.ClassId,
                            UserId = current_student_info.UserId,
                            UserType = 0
                        };
                        await _publisher.Publish(correct_student_data).ConfigureAwait(false);

                        //缓存教师正在批阅的学生
                        await _teacher_correct_redis_service.HSetCorrectStudentAsync(teacher_info.UserId, current_student_info.UserId);

                        //非练习册标签纸类型的批阅
                        if (correct != null)
                        {
                            await _teacher_correct_redis_service.SetCorrectStudentAsync
                            (
                                teacher_info.UserId, correct.PaperId, correct.ClassId,
                                current_student_info.UserId,
                                pageno <= 0 ? 1 : pageno,studentno
                            );
                        }
                    }

                    var xstudent = await _teacher_correct_redis_service.GetCorrectStudentAsync(teacher_info.UserId);
                    if (xstudent == null)
                    {
                        _log.LogInformation("mac:{mac} 没有找到当前正在批改的学生和试卷信息", data.Mac);
                        return;
                    }

                    //尝试从mongodb获取学生
                    var student = await GetStudent(xstudent.StudentId);
                    if (student == null)
                    {
                        _log.LogInformation("mac:{mac} 学生信息不存在: {studentid}", data.Mac, xstudent);
                        return;
                    }
                    var wpage = await _paper_info_redis_service.HGetPaperPageInfoAsync(correct.PaperId, pageno) ?? await GetWorkbookPage(correct.PaperId, pageno);
                    if (wpage == null)
                    {
                        _log.LogInformation("mac:{mac} paperid: {paperid} , 未找到试卷页面信息", data.Mac, correct.PaperId);
                        return;
                    }

                    //普通铺码或练习册这两个页面是一样的. 卷码纸不同!
                    var dot = new TeacherPenDotData
                    {
                        UserId = xstudent.StudentId,
                        TeacherId = teacher_info.UserId,
                        Mac = data.Mac,
                        Dots = data.Dots,
                        Time = data.Time,
                        RequestId = data.RequestId,
                        Page = pageno,
                        PageId = wpage.PageId,
                        CurrentPageNo = wpage.Page
                    };
                    await TeacherPenDot(dot, student.SchoolId, correct.PaperId, correct.ClassId);

                    // 通用A4纸处理完成，直接返回
                    return;
                }
                var student_info = await GetStudent(correct_class_id, studentno);
                if (student_info == null)
                {
                    return;
                }

                _log.LogInformation("mac:{mac} 推送批改学生信息，学号：{studentNo} StudentId:{studentId}", data.Mac, studentno, student_info.UserId);

                //推送正在批改的学生
                var xdata = new RealtimeCorrectStudentModel
                {
                    State = 1,
                    ClassId = student_info.ClassId,
                    UserId = student_info.UserId,
                    UserType = 0
                };
                await _publisher.Publish(xdata).ConfigureAwait(false);

                //缓存教师正在批阅的学生
                await _teacher_correct_redis_service.HSetCorrectStudentAsync(teacher_info.UserId, student_info.UserId);

                //非练习册标签纸类型的批阅
                if ((_paper_num_list.Contains(data.Page) || (data.Page >= 2000 && data.Page<=2500)) && correct != null)
                {
                    await _teacher_correct_redis_service.SetCorrectStudentAsync
                    (
                        teacher_info.UserId, correct.PaperId, correct.ClassId,
                        student_info.UserId,
                        pageno <= 0 ? 1 : pageno
                    );
                }
            }

            #endregion
        }
    }

    #region Business

    #region StudentInfo

    /// <summary>
    /// 学生笔笔迹处理
    /// </summary>
    /// <param name="data"></param>
    private async Task StudentPenDot(PenDotData data)
    {
        try
        {
            var student = await GetStudent(data.UserId);
            if (student == null)
            {
                _log.LogInformation("mac:{} studentId:{} 没有找到学生信息", data.Mac, data.UserId);
                return;
            }
            var raw_data = data.ToJsonString();
            //试卷id
            var paperid = "";

            //试卷页面唯一id
            var uwoo_pageid = 0;

            if (data.Dots is not { Count: > 0 })
            {
                return;
            }

            var dots = data.Dots.OrderBy(x => x.Oid).ToList();

            string color = null;

            #region 页码处理
            var page = data.Page;
            switch (data.Page)
            {
                case 5 or 6:
                case 100 or 1379:
                    {
                        #region 5 or 6  普通A4 铺码纸

                        //尝试获取一键赋码数据
                        var current_paperid = data.DataType == 1
                            ? await _paper_log_redis_service.GetCurrentPaperIdAsync(student.ClassId)
                            : await _paper_log_redis_service.GetCurrentOfflinePaperIdAsync(student.ClassId);
                        if (current_paperid != null)
                        {
                            #region 一键赋码

                            paperid = current_paperid.PaperId;

                            var paper = await GetPaperAsync(paperid);

                            color =await GetPaperHandwritingColorCacheAsync(
                                 userId: student.UserId,
                                 subjectId: paper.SubjectId,
                                 paperId: paperid
                                );

                            //获取平均值, 减少边界值偏差
                            var avg_x = dots.Average(x => x.X);
                            var avg_y = dots.Average(x => x.Y);

                            #region 颜色

                            if (!string.IsNullOrWhiteSpace(paperid))
                            {
                                var paperItemColorWrap = await GetPaperItemColorWrapAsync(paperid);

                                if (paperItemColorWrap.IsExist)
                                {
                                    var itemColorRange = GetItemColorRange(paperItemColorWrap.ItemColorRanges, avg_x, avg_y);

                                    if (itemColorRange!=null)
                                    {
                                        if (itemColorRange.IsColor)
                                        {
                                            color=itemColorRange.Color;
                                            await SetPaperHandwritingColorAsync(
                                                 userId: student.UserId,
                                                 subjectId: paper.SubjectId,
                                                 paperId: paper.Id,
                                                 color: itemColorRange.Color
                                                 );
                                        }
                                        else
                                        {
                                            await ReStoreSetPaperHandwritingColorAsync(
                                                userId: student.UserId,
                                                subjectId: paper.SubjectId,
                                                paperId: paper.Id
                                                );
                                        }

                                        return;
                                    }
                                }
                            }

                            #endregion

                            //当前默认第一页
                            var current_pageno = 1;
                            if (data.Page == 5)
                            {
                                //尝试获取页码
                                var xtmp_pageno = GetPageNo(avg_x, avg_y);
                                if (xtmp_pageno > 0)
                                {
                                    //如果勾选了页码将当前页码设置为勾选的页码
                                    current_pageno = xtmp_pageno;
                                    _log.LogInformation("mac:{} 勾选页码：{}", data.Mac, xtmp_pageno);
                                }

                                //尝试获取当前用户正在作答的页面
                                var current_workpage = await _paper_log_redis_service.GetStudentCurrentDoPaperPageInfoAsync(data.UserId);
                                if (current_workpage != null&&current_workpage.PaperId==current_paperid.PaperId)
                                {
                                    current_pageno = current_workpage.Page;
                                    if (xtmp_pageno >= 1)
                                    {
                                        current_pageno = xtmp_pageno;
                                    }
                                }

                                //尝试从缓存中获取试卷多页数据
                                var workpage = await _paper_info_redis_service.HGetPaperPageInfoAsync(paperid, current_pageno) ?? await GetWorkbookPage(paperid, current_pageno);
                                if (workpage == null)
                                {
                                    _log.LogInformation("mac:{} paperid: {paperid} , 未找到试卷页面信息", data.Mac, paperid);
                                    return;
                                }

                                //试卷页面唯一id
                                uwoo_pageid = workpage.PageId;
                                data.PageId = workpage.PageId;
                                data.PageNo = workpage.Page;
                                //缓存学生正在作答的页面
                                await _paper_log_redis_service.SetStudentCurrentDoPaperPageInfoAsync(data.UserId, workpage);
                            }
                            else
                            {
                                //尝试获取当前用户正在书写的页面
                                var workpage = await _paper_log_redis_service.GetStudentCurrentDoPaperPageInfoAsync(data.UserId);
                                if (workpage != null)
                                {
                                    #region 正常书写反面

                                    //翻页
                                    current_pageno = workpage.Page + 1;

                                    //尝试从缓存获取第二页数据
                                    var next_workpage = await _paper_info_redis_service.HGetPaperPageInfoAsync(paperid, current_pageno) ?? await GetWorkbookPage(paperid, current_pageno);

                                    if (next_workpage == null)
                                    {
                                        _log.LogInformation("mac:{} paperid: {paperid} current_pageno:{} , 未找到试卷页面信息01", data.Mac, paperid, current_pageno);
                                        return;
                                    }

                                    //试卷页面唯一id
                                    uwoo_pageid = next_workpage.PageId;
                                    data.PageId = next_workpage.PageId;
                                    data.PageNo = next_workpage.Page;

                                    #endregion
                                }
                                else
                                {
                                    #region 直接书写反面

                                    //尝试从缓存获取试卷页面信息
                                    var current_workpage = await _paper_info_redis_service.HGetPaperPageInfoAsync(paperid, 2) ?? await GetWorkbookPage(paperid, 2);
                                    if (current_workpage == null)
                                    {
                                        _log.LogInformation("mac:{} paperid: {paperid}  , 未找到试卷页面信息02", data.Mac, paperid);
                                        return;
                                    }

                                    uwoo_pageid = current_workpage.PageId;
                                    data.PageId = current_workpage.PageId;
                                    data.PageNo = current_workpage.Page;

                                    //手动缓存当前作答的正面信息
                                    var current_workpage_front = await _paper_info_redis_service.HGetPaperPageInfoAsync(paperid, 1) ?? await GetWorkbookPage(paperid, 1);
                                    if (current_workpage_front == null)
                                    {
                                        _log.LogInformation("mac:{} paperid: {paperid}  , 未找到试卷页面信息03", data.Mac, paperid);
                                        return;
                                    }

                                    //缓存学生正在作答的页面
                                    await _paper_log_redis_service.SetStudentCurrentDoPaperPageInfoAsync(data.UserId, current_workpage_front);

                                    #endregion
                                }
                            }

                            #region 媒体事件

                            if (!string.IsNullOrWhiteSpace(paperid))
                            {
                                var is_media = await _paper_info_redis_service.HGetPaperIdIsMediaAsync(paperid);
                                if (is_media)
                                {
                                    _log.LogInformation("mac:{} paperid:{} currentPageNo:{} page:{} x:{} y:{} 语音1", data.Mac, paperid, current_pageno, data.Page, avg_x, avg_y);
                                    var coord = await GetCoordInfo(avg_x, avg_y, paperid, currentPageNo: current_pageno, page: data.Page);
                                    if (coord != null)
                                    {
                                        _log.LogInformation("mac:{} paperid:{} 语音1-1 coordType:{}", data.Mac, paperid, coord.CoordType);
                                        switch (coord.CoordType)
                                        {
                                            case 2:
                                                {
                                                    #region 请求录音

                                                    var record_request = new RecordRequest
                                                    {
                                                        ItemId = coord.ItemId,
                                                        WorkbookPageId = coord.WorkBookPageId,
                                                        BaseRequest = new PenMediaRequest
                                                        {
                                                            Page = data.Page,
                                                            PageId = uwoo_pageid,
                                                            StudentId = student.UserId,
                                                            PaperId = paperid,
                                                            Mac = data.Mac,
                                                            UserRole = 0,
                                                            TeacherId = current_paperid.TeacherId
                                                        }
                                                    };
                                                    await _rpc_service_client.RecordServiceAsync(record_request);
                                                    return;

                                                    #endregion
                                                }
                                            case 1 or 3 or 5:
                                                {
                                                    #region 请求播放

                                                    var media_info = coord.CoordType switch
                                                    {
                                                        1 => await GetMediaInfo(paperid, coord.ItemId, 1, 0),
                                                        3 => await GetMediaInfo(paperid, coord.ItemId, 2, 0, current_paperid.TeacherId, student.UserId),
                                                        5 => await GetMediaInfo(paperid, coord.ItemId, 3, 0, current_paperid.TeacherId, student.UserId),
                                                        _ => null
                                                    };

                                                    if (media_info == null || string.IsNullOrWhiteSpace(media_info.Url))
                                                    {
                                                        return;
                                                    }

                                                    var play_request = new PlayRequest
                                                    {
                                                        Url = media_info.Url,
                                                        BaseRequest = new PenMediaRequest
                                                        {
                                                            Page = data.Page,
                                                            PageId = data.PageId,
                                                            StudentId = student.UserId,
                                                            PaperId = paperid,
                                                            Mac = data.Mac,
                                                            UserRole = 0,
                                                            TeacherId = current_paperid.TeacherId
                                                        }
                                                    };
                                                    await _rpc_service_client.PlayServiceAsync(play_request);
                                                    return;

                                                    #endregion
                                                }
                                            default:
                                                return;
                                        }
                                    }
                                    else
                                    {
                                        _log.LogInformation("mac:{} paperid:{} 语音2", data.Mac, paperid);
                                    }
                                }
                            }

                            #endregion

                            #endregion
                        }
                        else
                        {
                            #region 普通书写

                            //获取平均值, 减少边界值偏差
                            var avg_x = dots.Average(x => x.X);
                            var avg_y = dots.Average(x => x.Y);

                            //正面书写
                            if (data.Page == 5)
                            {
                                if (GetPageRight(avg_x, avg_y))
                                {
                                    /*在卷码位置书写,临时缓存在该区域书写的所有笔迹*/
                                    await _paper_log_redis_service.SAddUserPaperNumAsync(data.UserId, dots);

                                    //设置过期时间, 防止下次书写卷码笔迹重叠
                                    await _paper_log_redis_service.ExpirePaperNumAsync(data.UserId, TimeSpan.FromHours(1));
                                    return;
                                }
                                else if (GetScorePosition(avg_x, avg_y))
                                {
                                    //TODO: score
                                }
                                else
                                {
                                    /*在其他位置书写*/
                                    //尝试获取铺码纸中的页码(1,3,5,7)
                                    var pageno = GetPageNo(avg_x, avg_y);
                                    if (pageno== 0)
                                    {
                                        pageno = 1;
                                    }
                                    _log.LogInformation("mac:{} 识别页码: {pageno} , 用户: {userId}", data.Mac, pageno.ToString(), data.UserId);

                                    var temp_pageno = await _paper_log_redis_service.GetRecognitionNumberAsync(data.UserId);
                                    var paper_num_log_list = await _paper_log_redis_service.SMemberUserPaperNumAsync(data.UserId);
                                    if (paper_num_log_list.Count>0)
                                    {
                                        paper_num_log_list = paper_num_log_list.OrderBy(x => x.Oid).ToList();

                                        var penLogs = paper_num_log_list.Select(z => new DotDto
                                        {
                                            X= z.X,
                                            Y=z.Y,
                                            Type=z.Type,
                                            Oid=z.Oid,
                                            CreateTime=z.Time
                                        }).ToList();

                                        var codeMark = "[{\"X\":850,\"Y\":227},{\"X\":1120,\"Y\":227},{\"X\":1120,\"Y\":300},{\"X\":850,\"Y\":300}]";

                                        var response = _canvasService.DrawCaptureUploadObsByHandwriting(
                                            dots: penLogs,
                                            markPoints: new List<MarkPointsDto>()
                                            {
                                                        new MarkPointsDto {MarkId=Guid.NewGuid().ToString("N"),XYs=Newtonsoft.Json.JsonConvert.DeserializeObject<List<XY>>(codeMark)
                                                        }
                                            });

                                        if (response.IsSuccess)
                                        {
                                            _doubaoHuaweiOBSUrlRedisService.SetItems(response.ObjectNames);
                                            var imgurl = response.Urls.FirstOrDefault()??string.Empty;
                                            var msgContent = string.Empty;

                                            if (string.IsNullOrWhiteSpace(imgurl))
                                            {
                                                _log.LogInformation("mac:{} 没有对应的OBS地址", data.Mac);
                                                return;
                                            }

                                            _log.LogInformation("mac:{} imgurl:{}", data.Mac, imgurl);

                                            var txtContents = new List<DoubaoChatCompletionsRequest.TxtContent>();
                                            var txt = "只输出数字连起来的识别结果";

                                            txtContents.Add(new DoubaoChatCompletionsRequest.TxtContent
                                            {
                                                text=txt
                                            });

                                            var requestContents = new List<DoubaoChatCompletionsRequest.ContentBase>();
                                            requestContents.AddRange(txtContents);

                                            requestContents.Add(new DoubaoChatCompletionsRequest.ImageContent()
                                            {
                                                image_url=new DoubaoChatCompletionsRequest.Image_url
                                                {
                                                    url=imgurl
                                                }
                                            });

                                            var chatResponseWrap = await _doubaoChatCompletionsService.Ocr(requestContents);

                                            if (chatResponseWrap.IsSuccess)
                                            {
                                                msgContent = chatResponseWrap.Data.choices?.FirstOrDefault()?.message.content;
                                                msgContent=msgContent?.Trim();
                                                if (string.IsNullOrWhiteSpace(msgContent))
                                                {
                                                    _log.LogInformation("mac:{} 沒有识别结果返回", data.Mac);
                                                    return;
                                                }
                                            }
                                            else
                                            {
                                                msgContent=chatResponseWrap.Message;
                                                _log.LogInformation("mac:{} Third:"+msgContent, data.Mac);
                                                return;
                                            }

                                            var isSussess = int.TryParse(msgContent, out temp_pageno);

                                            if (!isSussess||temp_pageno<=0)
                                            {
                                                _log.LogInformation("mac:{} 卷码识别出来，转换无效", data.Mac);
                                                return;
                                            }
                                        }

                                        _log.LogInformation
                                       (
                                           "mac:{mac} 识别卷码: {temp_pageno} , 用户: {userId}", data.Mac, temp_pageno,
                                           data.UserId
                                       );

                                        if (temp_pageno >= 1000&&temp_pageno<=9999)
                                        {
                                            //临时保存当前识别的卷码数据
                                            await _paper_log_redis_service.SaveRecognitionNumberAsync
                                            (
                                                data.UserId,
                                                temp_pageno.ToString()
                                            );
                                        }
                                        else
                                        {
                                            _log.LogInformation("mac:{} 识别卷码:{},小于1000不保存", data.Mac, temp_pageno);
                                        }

                                        //清理卷码区域书写笔迹, 防止笔迹重叠问题
                                        await _paper_log_redis_service.DelUserPaperNumAsync(data.UserId);
                                    }

                                    if (temp_pageno < 1000&&temp_pageno!=-1)
                                    {
                                        _log.LogInformation("mac:{} 非一键赋码作答未获取到卷码: {error}", data.Mac, raw_data);
                                        return;
                                    }

                                    //获取学生正在书写的页面
                                    var workpage = await _paper_log_redis_service.GetStudentCurrentDoPaperPageInfoAsync(data.UserId);

                                    if (temp_pageno!=-1)
                                    {
                                        paperid = await _paper_log_redis_service.HGetPaperNumAsync
                                              (
                                                  temp_pageno.ToString()
                                              );
                                    }

                                    /*能获取到页码, 代表用户已经勾选对应页码.开始获取卷码笔迹并进行识别*/
                                    //勾选第一页
                                    if (pageno == 1)
                                    {
                                        #region 勾选第一页页码

                                        if (workpage == null||paperid!=workpage.PaperId)
                                        {
                                            if (workpage!=null&&!string.IsNullOrWhiteSpace(paperid)&& paperid!=workpage.PaperId)
                                            {
                                                _log.LogInformation("mac:{mac} studentid: {userid} ,正在尝试找卷码的试卷关系，由于卷码找到试卷和当前学生作答的试卷不一致,卷码：{temp_pageno} 卷码试卷：{paperid} 学生当前作答试卷{workpagePaperId}", data.Mac, data.UserId, temp_pageno, paperid, workpage.PaperId);
                                            }

                                            var ocr_paper_no = temp_pageno;
                                            if (ocr_paper_no is > 1000 and <= 9999)
                                            {
                                                //卷码范围为1001 ~ 9999, 该范围内才有效
                                                //尝试从缓存中通过卷码获取试卷信息
                                                paperid = await _paper_log_redis_service.HGetPaperNumAsync
                                                (
                                                    ocr_paper_no.ToString()
                                                );
                                                if (string.IsNullOrWhiteSpace(paperid))
                                                {
                                                    //根据卷码查询对应试卷信息
                                                    const string paper_num_sql = """
                                                                             SELECT TOP (1)
                                                                                    *
                                                                             FROM [dbo].[MD_PaperNum]
                                                                             WHERE [Num] = @number;
                                                                             """;

                                                    //获取系统中的卷码数据
                                                    var paper_num = await _dapper_service.QueryFirstAsync<PaperNum>
                                                    (
                                                        paper_num_sql, new
                                                        {
                                                            number = ocr_paper_no
                                                        }
                                                    );
                                                    if (paper_num != null)
                                                    {
                                                        paperid = paper_num.PaperId;

                                                        //缓存中无卷码数据,重新缓存卷码
                                                        await _paper_log_redis_service.HSetPaperNumAsync
                                                        (
                                                            ocr_paper_no.ToString(), paper_num.PaperId
                                                        );
                                                    }
                                                }

                                                //尝试从缓存中获取试卷多页数据
                                                workpage =
                                                    await _paper_info_redis_service.HGetPaperPageInfoAsync(paperid, pageno) ?? await GetWorkbookPage(paperid, pageno);

                                                if (workpage == null)
                                                {
                                                    _log.LogInformation("mac:{} paperid: {paperid} pageno:{}, 未找到试卷页面信息", data.Mac, paperid, pageno);
                                                    return;
                                                }

                                                //试卷页面唯一id
                                                uwoo_pageid = workpage.PageId;
                                                data.PageId = workpage.PageId;

                                                //缓存学生正在作答的页面
                                                await _paper_log_redis_service.SetStudentCurrentDoPaperPageInfoAsync
                                                (
                                                    data.UserId,
                                                    workpage
                                                );
                                            }
                                        }
                                        else
                                        {
                                            if (paperid!=workpage.PaperId)
                                            {
                                                _log.LogError("【2】 mac:{mac} studentid: {userid} , 卷码找到试卷和当前学生作答的试卷不一致,卷码：{temp_pageno} 卷码试卷：{paperid} 学生当前作答试卷{workpagePaperId} ", data.Mac, data.UserId, temp_pageno, paperid, workpage.PaperId);
                                                return;
                                            }

                                            _log.LogInformation("mac:{} 学生作答页面: {PaperId} , 用户: {userId}，班级：{classId}", data.Mac, workpage.PaperId, data.UserId, student.ClassId);

                                            //试卷页面唯一id
                                            uwoo_pageid = workpage.PageId;
                                            data.PageId = workpage.PageId;
                                            paperid = workpage.PaperId;
                                        }
                                        #endregion
                                    }
                                    else
                                    {
                                        #region 勾选其余页码(3,5,7)

                                        if (workpage == null)
                                        {
                                            _log.LogInformation("mac:{} pageno:{} studentid: {userid} , 未找到正在作答试卷页面信息2", data.Mac, temp_pageno, data.UserId);
                                            return;
                                        }

                                        if (paperid!=workpage.PaperId)
                                        {
                                            _log.LogError("【3】 mac:{mac} studentid: {userid} , 卷码找到试卷和当前学生作答的试卷不一致,卷码：{temp_pageno} 卷码试卷：{paperid} 学生当前作答试卷{workpagePaperId} ", data.Mac, data.UserId, temp_pageno, paperid, workpage.PaperId);
                                            return;
                                        }

                                        //获取当前勾选的页面数据
                                        var current_workpage =
                                            await _paper_info_redis_service.HGetPaperPageInfoAsync(paperid, pageno) ?? await GetWorkbookPage(paperid, pageno);

                                        if (current_workpage == null)
                                        {
                                            _log.LogInformation("mac:{} paperid: {paperid} pageno:{} studentid: {userid}, 未找到试卷页面信息", data.Mac, paperid, pageno, data.UserId);
                                            return;
                                        }

                                        //设置当前页面的唯一页面id
                                        uwoo_pageid = current_workpage.PageId;
                                        data.PageId = workpage.PageId;

                                        //更新当前正在作答的页面信息
                                        await _paper_log_redis_service.SetStudentCurrentDoPaperPageInfoAsync
                                        (
                                            data.UserId,
                                            current_workpage
                                        );

                                        #endregion
                                    }


                                }
                            }
                            else
                            {
                                #region 反面作答

                                var workpage = await _paper_log_redis_service.GetStudentCurrentDoPaperPageInfoAsync(data.UserId);
                                if (workpage == null)
                                {
                                    _log.LogInformation("【反面】 mac:{} studentid: {userid} , 未找到正在作答正面试卷页面信息", data.Mac, data.UserId);
                                    return;
                                }

                                //当前作答正面的页码
                                var current_pageno = workpage.Page;

                                //反面页码自动+1
                                current_pageno += 1;

                                //获取反面的试卷页面信息
                                var current_workpage =
                                    await _paper_info_redis_service.HGetPaperPageInfoAsync(workpage.PaperId, current_pageno);
                                if (current_workpage == null)
                                {
                                    _log.LogInformation("【反面】 mac:{} studentid: {userid} , 未找到正在作答反面试卷页面信息", data.Mac, data.UserId);
                                    return;
                                }

                                //获取反面作答的试卷和页码信息
                                paperid = current_workpage.PaperId;
                                uwoo_pageid = current_workpage.PageId;
                                data.PageId = current_workpage.PageId;

                                #endregion
                            }

                            #region 颜色

                            if (!string.IsNullOrWhiteSpace(paperid))
                            {
                                var paper = await GetPaperAsync(paperid);

                                color = await GetPaperHandwritingColorCacheAsync(
                                  userId: student.UserId,
                                  subjectId: paper.SubjectId,
                                  paperId: paperid
                                 );

                                var paperItemColorWrap = await GetPaperItemColorWrapAsync(paperid);

                                if (paperItemColorWrap.IsExist)
                                {
                                    var itemColorRange = GetItemColorRange(paperItemColorWrap.ItemColorRanges, avg_x, avg_y);

                                    if (itemColorRange!=null)
                                    {
                                        if (itemColorRange.IsColor)
                                        {
                                            color=itemColorRange.Color;
                                            await SetPaperHandwritingColorAsync(
                                                 userId: student.UserId,
                                                 subjectId: paper.SubjectId,
                                                 paperId: paper.Id,
                                                 color: itemColorRange.Color
                                                 );
                                        }
                                        else
                                        {
                                            await ReStoreSetPaperHandwritingColorAsync(
                                                userId: student.UserId,
                                                subjectId: paper.SubjectId,
                                                paperId: paper.Id
                                                );
                                        }

                                        return;
                                    }
                                }
                            }

                            #endregion

                            #endregion
                        }

                        if (uwoo_pageid <= 0 || data.PageId <= 0)
                        {
                            _log.LogInformation("mac:{} paperid: {paperid} , 未找到试卷页面信息", data.Mac, paperid);
                            return;
                        }
                        break;

                        #endregion
                    }
                case 15:
                    {
                        #region 15   答题卡

                        //尝试获取当前卷码
                        var current_paperid = await _paper_log_redis_service.GetCurrentPaperIdAsync(student.ClassId);
                        if (current_paperid == null)
                        {
                            _log.LogError("未获取到当前正在作答的卷码数据: {mac}", data.Mac);
                            return;
                        }

                        if (await IsA4(current_paperid))
                        {
                            _log.LogError("请用A4铺码纸作答: {mac}", data.Mac);
                            return;
                        }

                        var avg_x = Convert.ToSingle(dots.Average(x => x.X));
                        var avg_y = Convert.ToSingle(dots.Average(x => x.Y));
                        var is_effective = IsInEffectiveCard(avg_x, avg_y, data.Page);
                        if (!is_effective)
                        {
                            _log.LogError("未在答题卡有效区域作答: {mac} , {paperid}", data.Mac, current_paperid.PaperId);
                            return;
                        }

                        //定位题目区域
                        var region = LocateItemRegion(avg_x, avg_y, data.Page);
                        if (region == null)
                        {
                            _log.LogError("未定位到题目有效区域: {mac} , {paperid}", data.Mac, current_paperid.PaperId);
                            return;
                        }

                        var request_id = _guid_generator.NewStringId();
                        _log.LogInformation("答题卡作答记录: requestid:{requestid} , mac:{mac} , paperid:{paperid} , itemno:{itemno} , itemtype:{itemtype}，option:{option}", request_id, data.Mac, current_paperid.PaperId, region.ItemNo.ToString(), region.Item.ItemType.ToString(), region.Item.Option);

                        var eraser_state = await _paper_log_redis_service.GetEraserStateAsync(data.Mac, current_paperid.PaperId, region.ItemNo.ToString());

                        if (eraser_state)
                        {
                            await _paper_log_redis_service.DeleteEraserStateAsync(data.Mac, current_paperid.PaperId, region.ItemNo.ToString()).ConfigureAwait(false);
                        }

                        switch (region.Item.ItemType)
                        {
                            case 1 or 2:
                                {
                                    var pubdata = new CardAnswerResultData
                                    {
                                        RequestId = request_id,
                                        PaperId = current_paperid.PaperId,
                                        ItemNo = region.ItemNo,
                                        Mac = data.Mac,
                                        UserId = student.UserId,
                                        Result = region.Item.Option,
                                        IsEraser = eraser_state
                                    };
                                    await _publisher.Publish(pubdata).ConfigureAwait(false);

                                    _log.LogInformation("推送答题卡作答数据-{desc}: pushdata:{pushdata},region:{region}", region.Item.GetItemTypeDesc(), pubdata.ToJsonString(), region.ToJsonString());

                                    return;
                                }
                            case 6:
                                {
                                    var examItem = await GetExamItemAsync(paperId: current_paperid.PaperId, itemNo: region.ItemNo, classId: current_paperid.ClassId);
                                    if (examItem==null)
                                    {
                                        _log.LogError("答题卡录音:没有找到题目 requestid:{requestid} , mac:{mac} , paperid:{paperid} , itemno:{itemno} , itemtype:{itemtype}，option:{option}", request_id, data.Mac, current_paperid.PaperId, region.ItemNo.ToString(), region.Item.ItemType.ToString(), region.Item.Option);

                                        return;
                                    }

                                    var record_request = new RecordRequest
                                    {
                                        ItemId = examItem.Id,
                                        WorkbookPageId = string.Empty,
                                        BaseRequest = new PenMediaRequest
                                        {
                                            Page = data.Page,
                                            PageId = uwoo_pageid,
                                            StudentId = student.UserId,
                                            PaperId = current_paperid.PaperId,
                                            Mac = data.Mac,
                                            UserRole = 0,
                                            TeacherId = current_paperid.TeacherId
                                        }
                                    };

                                    await _rpc_service_client.RecordServiceAsync(record_request);

                                    return;
                                }
                            case 7:
                                {
                                    var examItem = await GetExamItemAsync(paperId: current_paperid.PaperId, itemNo: region.ItemNo, classId: current_paperid.ClassId);
                                    if (examItem==null)
                                    {
                                        _log.LogError("答题卡播放:没有找到题目 requestid:{requestid} , mac:{mac} , paperid:{paperid} , itemno:{itemno} , itemtype:{itemtype}，option:{option}", request_id, data.Mac, current_paperid.PaperId, region.ItemNo.ToString(), region.Item.ItemType.ToString(), region.Item.Option);

                                        return;
                                    }

                                    var media_info = await GetMediaInfo(
                                        paperid: current_paperid.PaperId,
                                        itemid: examItem.Id,
                                        filetype: 2,
                                        roletype: 0,
                                        studentid: student.UserId
                                        );

                                    if (media_info == null || string.IsNullOrWhiteSpace(media_info.Url))
                                    {
                                        _log.LogError("答题卡播放:没有找到播放资源 requestid:{requestid} , mac:{mac} , paperid:{paperid} , itemno:{itemno} , itemtype:{itemtype}，itemid:{itemid}，studentid:{studentid}", request_id, data.Mac, current_paperid.PaperId, region.ItemNo.ToString(), region.Item.ItemType.ToString(), examItem.Id, student.UserId);

                                        return;
                                    }

                                    var play_request = new PlayRequest
                                    {
                                        Url = media_info.Url,
                                        BaseRequest = new PenMediaRequest
                                        {
                                            Page = data.Page,
                                            PageId = data.PageId,
                                            StudentId = student.UserId,
                                            PaperId = current_paperid.PaperId,
                                            Mac = data.Mac,
                                            UserRole = 0,
                                            TeacherId = current_paperid.TeacherId
                                        }
                                    };
                                    await _rpc_service_client.PlayServiceAsync(play_request);

                                    return;
                                }
                            case 8:
                                {
                                    var examItem = await GetExamItemAsync(paperId: current_paperid.PaperId, itemNo: region.ItemNo, classId: current_paperid.ClassId);
                                    if (examItem!=null)
                                    {
                                        var sql = @"Delete Exam_PaperUserAnswer where DoPaperId=@paperId and UserId=@studentId and ItemId=@itemId";

                                        await _dapper_service.ExecuteAsync(sql, new
                                        {
                                            paperId = current_paperid.PaperId,
                                            studentId = student.UserId,
                                            itemId = examItem.Id
                                        });
                                    }

                                    #region Process

                                    await _paper_log_redis_service.SetEraserStateAsync(data.Mac, current_paperid.PaperId, region.ItemNo.ToString());
                                    return;

                                    #endregion
                                }
                        }

                        return;

                        #endregion
                    }
                case 24 or 25:
                    {
                        #region 24 or 25  英语答题卡

                        //尝试获取当前卷码
                        var current_paperid = await _paper_log_redis_service.GetCurrentPaperIdAsync(student.ClassId);
                        if (current_paperid == null)
                        {
                            //TODO: manual write pagenum
                            _log.LogError("非一键赋码作答答题卡未获取到卷码: {error}", raw_data);
                            return;
                        }

                        var avg_x = Convert.ToSingle(dots.Average(x => x.X));
                        var avg_y = Convert.ToSingle(dots.Average(x => x.Y));
                        paperid = current_paperid.PaperId;
                        uwoo_pageid = data.Page;

                        //是否在答题卡有效区域作答
                        var is_effective = IsInEffectiveEnglishUnitCard(avg_x, avg_y, data.Page);
                        if (!is_effective)
                        {
                            _log.LogError("未在答题卡有效区域作答: {error}", raw_data);
                            return;
                        }

                        //定位题目区域
                        var region = EnglishUnitLocateItemRegion(avg_x, avg_y, data.Page);
                        if (region == null)
                        {
                            _log.LogError("未定位到题目有效区域: {error}", raw_data);
                            return;
                        }

                        if (region.Item.ItemType is 1 or 2)
                        {
                            //客观题直接获取结果
                            var english_objective_result_data = new EnglishUnitObjectiveAnswerResultData
                            {
                                PageId = uwoo_pageid,
                                Page = data.Page,
                                Mac = data.Mac,
                                PaperId = paperid,
                                UserId = data.UserId,
                                ItemNo = region.ItemNo,
                                Result = region.Item.Option,
                                Score = region.Score,
                                IsMultiScore = region.IsMultiScore
                            };
                            await _publisher.Publish(english_objective_result_data).ConfigureAwait(false);
                            _log.LogInformation
                            (
                                "英语单元答题卡客观题作答结果: paperid: {paperid} , pageid: {uwoo_pageid} , userid: {userid} , itemno: {itemno} , result: {result}", paperid,
                                uwoo_pageid.ToString(), data.UserId, english_objective_result_data.ItemNo.ToString(), english_objective_result_data.Result
                            );
                            return;
                        }

                        //填空题作答结果直接入库
                        var card_pen_log = new CardPenLog
                        {
                            Mid = _snowflake.NewId(),
                            Mac = data.Mac,
                            UserId = data.UserId,
                            Page = data.Page,
                            PageId = uwoo_pageid,
                            ItemNo = region.ItemNo,
                            SubItemNo = region.Item.Index,
                            PaperId = paperid,
                            Dots = _mapper.Map<List<DotBase>>(dots)
                        };
                        await _card_pen_log_service.AddAsync(card_pen_log, student.SchoolId);

                        _log.LogInformation
                        (
                            "英语单元答题卡主观题笔迹入库: paperid: {paperid} , pageid: {uwoo_pageid} , userid: {userid} , itemno: {itemno} , subitemno: {subitemno}", paperid,
                            uwoo_pageid.ToString(), data.UserId, region.ItemNo.ToString(), region.Item.Index.ToString()
                        );

                        return;

                        #endregion
                    }
                case >= 30 and <= 69:
                    {
                        #region 30 ~ 69  互动练习薄

                        var current_paperid = data.DataType == 1
                            ? await _paper_log_redis_service.GetCurrentPaperIdAsync(student.ClassId)
                            : await _paper_log_redis_service.GetCurrentOfflinePaperIdAsync(student.ClassId);

                        if (current_paperid == null)
                        {
                            _log.LogError("非一键赋码作答未获取到试卷信息: {data}", raw_data);
                            return;
                        }

                        if (await IsA4(current_paperid))
                        {
                            _log.LogError("请用A4铺码纸作答: {mac}", data.Mac);
                            return;
                        }

                        var current_paper_itemid = await _paper_log_redis_service.GetCurrentPaperItemIdAsync(current_paperid.ClassId, current_paperid.PaperId);
                        if (current_paper_itemid == null)
                        {
                            _log.LogError("非一键赋码作答未获取到试卷题目信息: {data}", raw_data);
                            return;
                        }

                        var avg_x = Convert.ToSingle(dots.Average(x => x.X));
                        var avg_y = Convert.ToSingle(dots.Average(x => x.Y));
                        var xdetail = LocateWorkbookRegion(avg_x, avg_y);
                        if (xdetail == null)
                        {
                            return;
                        }
                        var workbook_penlog = new WorkbookPenLog
                        {
                            Mid = _snowflake.NewId(),
                            LineNo = xdetail.Page,
                            Blank = xdetail.Index,
                            Mac = data.Mac,
                            UserId = data.UserId,
                            Page = data.Page,
                            PageId = data.Page,
                            PaperId = current_paper_itemid.PaperId,
                            ItemId = current_paper_itemid.ItemId,
                            IsFullLine = current_paper_itemid.IsFullLine,
                            Dots = _mapper.Map<List<DotBase>>(dots)
                        };

                        #region 实时笔迹

                        var realtime_workbook_dots = new List<WorkbookPenDot>();
                        foreach (var workbook_dot in workbook_penlog.Dots.Select(item => _mapper.Map<WorkbookPenDot>(item)))
                        {
                            workbook_dot.IsFullLine = current_paper_itemid.IsFullLine;
                            workbook_dot.LineNo = xdetail.Page;
                            workbook_dot.Blank = xdetail.Index;
                            workbook_dot.Page = data.Page;
                            realtime_workbook_dots.Add(workbook_dot);
                        }
                        var isAnswered = await _paper_log_redis_service.GetSingleItemAnswerStateAsync(data.Mac, current_paper_itemid.PaperId, current_paper_itemid.ClassId, current_paper_itemid.ItemId);
                        if (!isAnswered)
                        {
                            _log.LogInformation
                       (
                           "练习薄作答数据入队列: paperid: {paperid} , itemid: {itemid} , page: {page} , userid: {userid}", current_paper_itemid.PaperId, current_paper_itemid.ItemId,
                           data.Page.ToString(), data.UserId
                       );
                            await _paper_log_redis_service.SetSingleItemAnswerStateAsync(data.Mac, current_paper_itemid.PaperId, current_paper_itemid.ClassId, current_paper_itemid.ItemId);
                            var singleAnswerRequest = new InteractiveWorkbookSingleItemAnswer
                            {
                                ItemId = current_paper_itemid.ItemId,
                                PaperId = current_paper_itemid.PaperId,
                                ClassId = current_paper_itemid.ClassId,
                                UserId = data.UserId
                            };
                            await _publisher.Publish(singleAnswerRequest).ConfigureAwait(false);
                        }
                        //实时作答笔迹
                        var realtime_workbook_dot_data = new RealtimeWorkbookDotModel
                        {
                            Dots = realtime_workbook_dots,
                            StudentId = data.UserId,
                            ClassId = current_paper_itemid.ClassId,
                            ItemId = current_paper_itemid.ItemId,
                            PaperId = current_paper_itemid.PaperId,
                            Page = data.Page
                        };
                        await _publisher.Publish(realtime_workbook_dot_data).ConfigureAwait(false);

                        #endregion

                        #region 笔迹入库

                        await _workbook_pen_log_service.AddAsync(workbook_penlog, student.SchoolId).ConfigureAwait(false);

                        _log.LogInformation
                        (
                            "练习薄作答数据入库: paperid: {paperid} , itemid: {itemid} , page: {page} , userid: {userid}", paperid, current_paper_itemid.ItemId,
                            data.Page.ToString(), data.UserId
                        );

                        #endregion

                        return;

                        #endregion
                    }
                case >= 1400 and <= 1879:
                    {
                        // 通用B5 
                        break;
                    }
                case >= 2000 and <= 2500:   //通用A4 铺码纸
                case >= 5001 and <= 5200:   // 作文纸
                    {
                        #region 
                        // 根据当前学生所在班级获取是否存在赋码数据
                        var current_paperid = data.DataType == 1
                            ? await _paper_log_redis_service.GetCurrentPaperIdAsync(student.ClassId)
                            : await _paper_log_redis_service.GetCurrentOfflinePaperIdAsync(student.ClassId);

                        if (current_paperid == null)
                        {
                            _log.LogError("mac:{}，data.Page:{} 未找到当前赋码试卷信息", data.Mac, data.Page);
                            return;
                        }

                        // 先从缓存查询Mac与页面的映射关系
                        var cacheKey = $"PaperMacMapping|{current_paperid.PaperId}|{data.Page}|{data.Mac}";
                        var paperMacMapping = await _commonRedisService.GetAsync<MD_PaperMacMapping>(cacheKey);
                        if (paperMacMapping == null)
                        {
                            // 缓存中没有，查询数据库
                            const string md_paperMacMappingSql = """
                                SELECT TOP (1) *
                                FROM [dbo].[MD_PaperMacMapping] WITH (NOLOCK)
                                WHERE [PaperId] = @paperId
                                      AND [PaperCode] = @page
                                      AND [PMac] = @mac
                                      AND [Deleted] = 0;
                                """;

                            paperMacMapping = await _dapper_service.QueryFirstAsync<MD_PaperMacMapping>(
                                md_paperMacMappingSql, new
                                {
                                    paperId = current_paperid.PaperId,
                                    page = data.Page,
                                    mac = data.Mac
                                });

                            if (paperMacMapping == null)
                            {
                                // 数据库中也没有，需要创建新的映射关系
                                paperMacMapping = await CreatePaperMacMappingAsync(current_paperid.PaperId, data.Page, data.Mac, data.UserId);
                                if (paperMacMapping == null)
                                {
                                    _log.LogError("mac:{} 创建Mac与页面映射关系失败", data.Mac);
                                    return;
                                }
                            }

                            // 缓存映射关系，缓存2小时
                            await _commonRedisService.SetAsync(cacheKey, paperMacMapping, TimeSpan.FromMinutes(5));
                        }

                        // 根据映射关系查询具体的试卷页面信息，先查缓存再查数据库
                        var workpage = await _paper_info_redis_service.HGetPaperPageInfoAsync(
                            current_paperid.PaperId, paperMacMapping.PaperPageIndex);

                        if (workpage == null)
                        {
                            // 缓存中没有，查询数据库
                            const string workpage_sql = """
                                SELECT TOP (1) *
                                FROM [dbo].[WorkbookPage]
                                WHERE [Page] = @page
                                      AND [PaperId] = @paperId
                                """;

                            workpage = await _dapper_service.QueryFirstAsync<WorkbookPage>(
                                workpage_sql, new
                                {
                                    page = paperMacMapping.PaperPageIndex,
                                    paperId = current_paperid.PaperId
                                });

                            if (workpage != null)
                            {
                                // 缓存试卷页面信息
                                await _paper_info_redis_service.HSetPaperPageInfoAsync(
                                    current_paperid.PaperId, paperMacMapping.PaperPageIndex, workpage);
                            }
                        }

                        if (workpage == null)
                        {
                            _log.LogError("mac:{} 未找到对应的试卷页面信息: PaperId={}, Page={}",
                                data.Mac, current_paperid.PaperId, paperMacMapping.PaperPageIndex);
                            return;
                        }

                        paperid = workpage.PaperId;
                        uwoo_pageid = workpage.PageId;
                        data.PageId = workpage.PageId;
                        data.PageNo = workpage.Page;
                        page = workpage.PageId;
                        #endregion
                        break;
                    }
                case >= 3001 and <= 3480: //B4
                    //B4
                    break;
                case > 10000:
                    {
                        #region 1000  练习册

                        //查询是否为组合页面
                        var is_combination = await _paper_info_redis_service.HGetPaperInfoIsCombinationAsync(data.Page);
                        if (!is_combination)
                        {
                            //获取普通的试卷信息
                            paperid = await _paper_info_redis_service.HGetPaperIdAsync(data.Page);
                            uwoo_pageid = data.PageId;
                            if (string.IsNullOrWhiteSpace(paperid))
                            {
                                //缓存中没有试卷信息, 从数据库读取
                                const string workpage_sql = """
                                                        SELECT TOP (1) PaperId
                                                        FROM WorkbookPage
                                                        WHERE PaperType = 2
                                                              AND PageId = @pageid;
                                                        """;
                                var workpage = await _dapper_service.QueryFirstAsync<WorkbookPage>
                                (
                                    workpage_sql, new
                                    {
                                        pageid = data.PageId
                                    }
                                );
                                if (workpage != null)
                                {
                                    paperid = workpage.PaperId;
                                    uwoo_pageid = workpage.PageId;

                                    //缓存试卷信息
                                    await _paper_info_redis_service.HSetPaperIdAsync(data.PageId, paperid);
                                }
                            }

                            if (string.IsNullOrWhiteSpace(paperid) || uwoo_pageid < 0)
                            {
                                _log.LogError("未查询到试卷信息 , {msg}", raw_data);
                                return;
                            }
                        }
                        else
                        {
                            var combination_result = await GetCombinationPaperId(data.Dots, data.PageId);
                            if (string.IsNullOrWhiteSpace(combination_result.Item1) || combination_result.Item2 <= 0)
                            {
                                _log.LogError("未查询到组合页试卷信息 , {msg}", raw_data);
                                return;
                            }

                            paperid = combination_result.Item1;
                            uwoo_pageid = combination_result.Item2;
                        }

                        break;

                        #endregion
                    }
                case 19 or 20:   
                    {
                        #region 19 or 20:   8K纸
                        var current_paperid = data.DataType == 1
                           ? await _paper_log_redis_service.GetCurrentPaperIdAsync(student.ClassId)
                           : await _paper_log_redis_service.GetCurrentOfflinePaperIdAsync(student.ClassId);

                        if (current_paperid != null)
                        {
                            paperid = current_paperid.PaperId;

                            var paper = await GetPaperAsync(paperid);
                            if (!paper.HomeworkType.HasValue)
                            {
                                _log.LogInformation("8K mac:{} paperid: {} , 没有作业类型", data.Mac, paperid);
                                return;
                            }

                            if (paper.HomeworkType.Value != 2)
                            {
                                _log.LogInformation("8K mac:{} paperid: {} , 作业类型不是8K", data.Mac, paperid);
                                return;
                            }

                            var avg_x = dots.Average(x => x.X);
                            var avg_y = dots.Average(x => x.Y);
                            var transfer_x = avg_x;
                            if (avg_x > Middle8K_X_Position)
                            {
                                transfer_x = avg_x - Middle8K_X_Position;
                            }
                            var current_pageno = 1;

                            if (data.Page == 19)
                            {
                                var tmp_pageno = GetPageNo8K(avg_x, avg_y);
                                if (tmp_pageno > 0)
                                {
                                    current_pageno = tmp_pageno;
                                    _log.LogInformation("8K mac:{} 勾选页码：{}", data.Mac, tmp_pageno);
                                }

                                var current_workpageGroup = await _paper_log_redis_service.GetStudentCurrentDoPaperPageInfoFor8KAsync(data.UserId);
                                if (current_workpageGroup != null && current_workpageGroup.WorkbookPages.First().PaperId == current_paperid.PaperId)
                                {
                                    current_pageno = current_workpageGroup.PageNumber;
                                    if (tmp_pageno > 1)
                                    {
                                        current_pageno = tmp_pageno;
                                    }
                                }

                                var groupIndex = (current_pageno + 1) / 2;
                                var workpageGroup = (await GetWorkbookPageGroups(paperid)).FirstOrDefault(z => z.Index == groupIndex);
                                if (workpageGroup == null)
                                {
                                    _log.LogInformation("8K mac:{} paperid: {} , 未找到试卷页面信息", data.Mac, paperid);
                                    return;
                                }

                                if (avg_x > Middle8K_X_Position)
                                {
                                    if (workpageGroup.WorkbookPages.Count == 1)
                                    {
                                        _log.LogInformation("8K mac:{} paperid: {} , 未找到试卷页面信息2", data.Mac, paperid);
                                        return;
                                    }

                                    var workpage = workpageGroup.WorkbookPages.Last();
                                    uwoo_pageid = workpage.PageId;
                                    data.PageId = uwoo_pageid;
                                    data.PageNo = workpage.Page;
                                }
                                else
                                {
                                    var workpage = workpageGroup.WorkbookPages.First();
                                    uwoo_pageid = workpage.PageId;
                                    data.PageId = uwoo_pageid;
                                    data.PageNo = workpage.Page;
                                }

                                await _paper_log_redis_service.SetStudentCurrentDoPaperPageInfoFor8KAsync(data.UserId, workpageGroup);
                            }
                            else
                            {
                                var current_workpageGroup = await _paper_log_redis_service.GetStudentCurrentDoPaperPageInfoFor8KAsync(data.UserId);
                                if (current_workpageGroup != null)
                                {
                                    if (current_workpageGroup.WorkbookPages.First().PaperId != current_paperid.PaperId)
                                    {
                                        _log.LogInformation("8K mac:{} paperid: {} , 未找到试卷页面信息3", data.Mac, paperid);
                                        return;
                                    }

                                    current_pageno = current_workpageGroup.PageNumber;

                                    var workpageGroups = (await GetWorkbookPageGroups(paperid)).Where(z => z.PageNumber == current_pageno).ToList();
                                    if (workpageGroups.Count == 1)
                                    {
                                        _log.LogInformation("8K mac:{} paperid: {} , 未找到试卷页面信息4", data.Mac, paperid);
                                        return;
                                    }

                                    var workpageGroup = workpageGroups.Last();

                                    if (avg_x > Middle8K_X_Position)
                                    {
                                        var workpage = workpageGroup.WorkbookPages.Last();
                                        uwoo_pageid = workpage.PageId;
                                        data.PageId = uwoo_pageid;
                                        data.PageNo = workpage.Page;
                                    }
                                    else
                                    {
                                        var workpage = workpageGroup.WorkbookPages.First();
                                        uwoo_pageid = workpage.PageId;
                                        data.PageId = uwoo_pageid;
                                        data.PageNo = workpage.Page;
                                    }
                                }
                                else
                                {
                                    var workpageGroups = (await GetWorkbookPageGroups(paperid)).Where(z => z.PageNumber == current_pageno).ToList();
                                    if (workpageGroups.Count == 1)
                                    {
                                        _log.LogInformation("8K mac:{} paperid: {} , 未找到试卷页面信息4", data.Mac, paperid);
                                        return;
                                    }

                                    var workpageGroup = workpageGroups.Last();

                                    if (avg_x > Middle8K_X_Position)
                                    {
                                        var workpage = workpageGroup.WorkbookPages.Last();
                                        uwoo_pageid = workpage.PageId;
                                        data.PageId = uwoo_pageid;
                                        data.PageNo = workpage.Page;
                                    }
                                    else
                                    {
                                        var workpage = workpageGroup.WorkbookPages.First();
                                        uwoo_pageid = workpage.PageId;
                                        data.PageId = uwoo_pageid;
                                        data.PageNo = workpage.Page;
                                    }

                                    await _paper_log_redis_service.SetStudentCurrentDoPaperPageInfoFor8KAsync(data.UserId, workpageGroup);
                                }
                            }

                        }
                        else
                        {
                            var avg_x = dots.Average(x => x.X);
                            var avg_y = dots.Average(x => x.Y);

                            if (data.Page == 19)
                            {
                                if (GetPageRight8K(avg_x, avg_y))
                                {
                                    await _paper_log_redis_service.SAddUserPaperNumAsync(data.UserId, dots);
                                    await _paper_log_redis_service.ExpirePaperNumAsync(data.UserId, TimeSpan.FromHours(1));
                                    return;
                                }
                                else
                                {
                                    var current_pageno = 1;

                                    var pageno = GetPageNo8K(avg_x, avg_y);
                                    if (pageno == 0)
                                    {
                                        pageno = 1;
                                        current_pageno = pageno;
                                    }
                                    _log.LogInformation("8K Common mac:{} 识别页码: {} , 用户: {}", data.Mac, pageno.ToString(), data.UserId);

                                    var temp_pageno = await _paper_log_redis_service.GetRecognitionNumberAsync(data.UserId);
                                    var paper_num_log_list = await _paper_log_redis_service.SMemberUserPaperNumAsync(data.UserId);
                                    if (paper_num_log_list.Count > 0)
                                    {
                                        paper_num_log_list = paper_num_log_list.OrderBy(x => x.Oid).ToList();

                                        var penLogs = paper_num_log_list.Select(z => new DotDto
                                        {
                                            X = z.X,
                                            Y = z.Y,
                                            Type = z.Type,
                                            Oid = z.Oid,
                                            CreateTime = z.Time
                                        }).ToList();

                                        var codeMark = "[{\"X\":787,\"Y\":204},{\"X\":1011,\"Y\":204},{\"X\":1011,\"Y\":256},{\"X\":787,\"Y\":256}]";

                                        var response = _canvasService.DrawCaptureUploadObsByHandwriting(
                                            dots: penLogs,
                                            markPoints: new List<MarkPointsDto>()
                                            {
                                                        new MarkPointsDto {MarkId=Guid.NewGuid().ToString("N"),XYs=Newtonsoft.Json.JsonConvert.DeserializeObject<List<XY>>(codeMark)
                                                        }
                                            });

                                        if (response.IsSuccess)
                                        {
                                            _doubaoHuaweiOBSUrlRedisService.SetItems(response.ObjectNames);
                                            var imgurl = response.Urls.FirstOrDefault() ?? string.Empty;
                                            var msgContent = string.Empty;

                                            if (string.IsNullOrWhiteSpace(imgurl))
                                            {
                                                _log.LogInformation("8K Common mac:{} 没有对应的OBS地址", data.Mac);
                                                return;
                                            }

                                            _log.LogInformation("8K Common mac:{} imgurl:{}", data.Mac, imgurl);

                                            var txtContents = new List<DoubaoChatCompletionsRequest.TxtContent>();
                                            var txt = "只输出数字连起来的识别结果";

                                            txtContents.Add(new DoubaoChatCompletionsRequest.TxtContent
                                            {
                                                text = txt
                                            });

                                            var requestContents = new List<DoubaoChatCompletionsRequest.ContentBase>();
                                            requestContents.AddRange(txtContents);

                                            requestContents.Add(new DoubaoChatCompletionsRequest.ImageContent()
                                            {
                                                image_url = new DoubaoChatCompletionsRequest.Image_url
                                                {
                                                    url = imgurl
                                                }
                                            });

                                            var chatResponseWrap = await _doubaoChatCompletionsService.Ocr(requestContents);

                                            if (chatResponseWrap.IsSuccess)
                                            {
                                                msgContent = chatResponseWrap.Data.choices?.FirstOrDefault()?.message.content;
                                                msgContent = msgContent?.Trim();
                                                if (string.IsNullOrWhiteSpace(msgContent))
                                                {
                                                    _log.LogInformation("8K Common mac:{} 沒有识别结果返回", data.Mac);
                                                    return;
                                                }
                                            }
                                            else
                                            {
                                                msgContent = chatResponseWrap.Message;
                                                _log.LogInformation("8K Common mac:{} Third:" + msgContent, data.Mac);
                                                return;
                                            }

                                            var isSussess = int.TryParse(msgContent, out temp_pageno);

                                            if (!isSussess || temp_pageno <= 0)
                                            {
                                                _log.LogInformation("8K Common mac:{} 卷码识别出来，转换无效", data.Mac);
                                                return;
                                            }
                                        }

                                        _log.LogInformation
                                       (
                                           "8K Common mac:{mac} 识别卷码: {temp_pageno} , 用户: {userId}", data.Mac, temp_pageno,
                                           data.UserId
                                       );

                                        if (temp_pageno >= 1000 && temp_pageno <= 9999)
                                        {
                                            //临时保存当前识别的卷码数据
                                            await _paper_log_redis_service.SaveRecognitionNumberAsync
                                            (
                                                data.UserId,
                                                temp_pageno.ToString()
                                            );
                                        }
                                        else
                                        {
                                            _log.LogInformation("8K Common mac:{} 识别卷码:{},小于1000不保存", data.Mac, temp_pageno);
                                        }

                                        await _paper_log_redis_service.DelUserPaperNumAsync(data.UserId);
                                    }

                                    if (temp_pageno < 1000 && temp_pageno != -1)
                                    {
                                        _log.LogInformation("8K Common mac:{} 非一键赋码作答未获取到卷码: {error}", data.Mac, raw_data);
                                        return;
                                    }

                                    var current_workpageGroup = await _paper_log_redis_service.GetStudentCurrentDoPaperPageInfoFor8KAsync(data.UserId);
                                    if (current_workpageGroup != null)
                                    {
                                        current_pageno = current_workpageGroup.PageNumber;
                                        if (pageno > 1)
                                        {
                                            current_pageno = pageno;
                                        }
                                    }

                                    if (temp_pageno != -1)
                                    {
                                        paperid = await _paper_log_redis_service.HGetPaperNumAsync
                                              (
                                                  temp_pageno.ToString()
                                              );
                                    }

                                    if (pageno == 1)
                                    {
                                        if (current_workpageGroup == null || string.IsNullOrWhiteSpace(paperid))
                                        {
                                            if (current_workpageGroup != null && !string.IsNullOrWhiteSpace(paperid) && paperid != current_workpageGroup.WorkbookPages.First().PaperId)
                                            {
                                                _log.LogInformation("mac:{mac} studentid: {userid} ,正在尝试找卷码的试卷关系，由于卷码找到试卷和当前学生作答的试卷不一致,卷码：{temp_pageno} 卷码试卷：{paperid} 学生当前作答试卷{workpagePaperId}", data.Mac, data.UserId, temp_pageno, paperid, current_workpageGroup.WorkbookPages.First().PaperId);
                                            }

                                            var ocr_paper_no = temp_pageno;
                                            if (ocr_paper_no is > 1000 and <= 9999)
                                            {
                                                paperid = await _paper_log_redis_service.HGetPaperNumAsync
                                                (
                                                    ocr_paper_no.ToString()
                                                );

                                                if (string.IsNullOrWhiteSpace(paperid))
                                                {
                                                    const string paper_num_sql = """
                                                                             SELECT TOP (1)
                                                                                    *
                                                                             FROM [dbo].[MD_PaperNum]
                                                                             WHERE [Num] = @number;
                                                                             """;

                                                    var paper_num = await _dapper_service.QueryFirstAsync<PaperNum>
                                                    (
                                                        paper_num_sql, new
                                                        {
                                                            number = ocr_paper_no
                                                        }
                                                    );
                                                    if (paper_num != null)
                                                    {
                                                        paperid = paper_num.PaperId;

                                                        await _paper_log_redis_service.HSetPaperNumAsync
                                                        (
                                                            ocr_paper_no.ToString(), paper_num.PaperId
                                                        );
                                                    }
                                                }

                                                var groupIndex = (current_pageno + 1) / 2;
                                                var workpageGroup = (await GetWorkbookPageGroups(paperid)).FirstOrDefault(z => z.Index == groupIndex);
                                                if (workpageGroup == null)
                                                {
                                                    _log.LogInformation("8K Common mac:{} paperid: {} , 未找到试卷页面信息", data.Mac, paperid);
                                                    return;
                                                }

                                                if (avg_x > Middle8K_X_Position)
                                                {
                                                    if (workpageGroup.WorkbookPages.Count == 1)
                                                    {
                                                        _log.LogInformation("8K Common mac:{} paperid: {} , 未找到试卷页面信息2", data.Mac, paperid);
                                                        return;
                                                    }

                                                    var workpage = workpageGroup.WorkbookPages.Last();
                                                    uwoo_pageid = workpage.PageId;
                                                    data.PageId = uwoo_pageid;
                                                    data.PageNo = workpage.Page;
                                                }
                                                else
                                                {
                                                    var workpage = workpageGroup.WorkbookPages.First();
                                                    uwoo_pageid = workpage.PageId;
                                                    data.PageId = uwoo_pageid;
                                                    data.PageNo = workpage.Page;
                                                }

                                                await _paper_log_redis_service.SetStudentCurrentDoPaperPageInfoFor8KAsync(data.UserId, workpageGroup);

                                            }
                                        }
                                        else
                                        {
                                            if (current_workpageGroup != null)
                                            {
                                                if (paperid != current_workpageGroup.WorkbookPages.First().PaperId)
                                                {
                                                    _log.LogError("8K Common【2】 mac:{mac} studentid: {userid} , 卷码找到试卷和当前学生作答的试卷不一致,卷码：{temp_pageno} 卷码试卷：{paperid} 学生当前作答试卷{workpagePaperId} ", data.Mac, data.UserId, temp_pageno, paperid, current_workpageGroup.WorkbookPages.First().PaperId);
                                                    return;
                                                }

                                                _log.LogInformation("8K Common mac:{} 学生作答页面: {PaperId} , 用户: {userId}，班级：{classId}", data.Mac, current_workpageGroup.WorkbookPages.First().PaperId, data.UserId, student.ClassId);

                                                if (avg_x > Middle8K_X_Position)
                                                {
                                                    if (current_workpageGroup.WorkbookPages.Count == 1)
                                                    {
                                                        _log.LogInformation("8K Common mac:{} paperid: {} , 未找到试卷页面信息2", data.Mac, paperid);
                                                        return;
                                                    }

                                                    var workpage = current_workpageGroup.WorkbookPages.Last();
                                                    uwoo_pageid = workpage.PageId;
                                                    data.PageId = uwoo_pageid;
                                                    data.PageNo = workpage.Page;
                                                }
                                                else
                                                {
                                                    var workpage = current_workpageGroup.WorkbookPages.First();
                                                    uwoo_pageid = workpage.PageId;
                                                    data.PageId = uwoo_pageid;
                                                    data.PageNo = workpage.Page;
                                                }
                                            }
                                        }
                                    }
                                    else
                                    {
                                        if (current_workpageGroup == null)
                                        {
                                            _log.LogInformation("8K Common mac:{} pageno:{} studentid: {userid} , 未找到正在作答试卷页面信息2", data.Mac, temp_pageno, data.UserId);
                                            return;
                                        }

                                        if (paperid != current_workpageGroup.WorkbookPages.First().PaperId)
                                        {
                                            _log.LogError("8K Common【3】 mac:{mac} studentid: {userid} , 卷码找到试卷和当前学生作答的试卷不一致,卷码：{temp_pageno} 卷码试卷：{paperid} 学生当前作答试卷{workpagePaperId} ", data.Mac, data.UserId, temp_pageno, paperid, current_workpageGroup.WorkbookPages.First().PaperId);
                                            return;
                                        }

                                        var groupIndex = (current_pageno + 1) / 2;
                                        var workpageGroup = (await GetWorkbookPageGroups(paperid)).FirstOrDefault(z => z.Index == groupIndex);
                                        if (workpageGroup == null)
                                        {
                                            _log.LogInformation("8K Common mac:{} paperid: {} , 未找到试卷页面信息", data.Mac, paperid);
                                            return;
                                        }

                                        if (avg_x > Middle8K_X_Position)
                                        {
                                            if (workpageGroup.WorkbookPages.Count == 1)
                                            {
                                                _log.LogInformation("8K Common mac:{} paperid: {} , 未找到试卷页面信息2", data.Mac, paperid);
                                                return;
                                            }

                                            var workpage = workpageGroup.WorkbookPages.Last();
                                            uwoo_pageid = workpage.PageId;
                                            data.PageId = uwoo_pageid;
                                            data.PageNo = workpage.Page;
                                        }
                                        else
                                        {
                                            var workpage = workpageGroup.WorkbookPages.First();
                                            uwoo_pageid = workpage.PageId;
                                            data.PageId = uwoo_pageid;
                                            data.PageNo = workpage.Page;
                                        }

                                        await _paper_log_redis_service.SetStudentCurrentDoPaperPageInfoFor8KAsync(data.UserId, workpageGroup);
                                    }
                                }
                            }
                            else
                            {
                                var current_workpageGroup = await _paper_log_redis_service.GetStudentCurrentDoPaperPageInfoFor8KAsync(data.UserId);
                                if (current_workpageGroup == null)
                                {
                                    _log.LogInformation("8K Common【反面】 mac:{} studentid: {userid} , 未找到正在作答正面试卷页面信息", data.Mac, data.UserId);
                                    return;
                                }

                                var groupIndex = (current_workpageGroup.PageNumber + 1) / 2 + 1;
                                var workpageGroup = (await GetWorkbookPageGroups(paperid)).FirstOrDefault(z => z.Index == groupIndex);
                                if (workpageGroup == null)
                                {
                                    _log.LogInformation("8K Common mac:{} paperid: {} , 未找到试卷页面信息", data.Mac, paperid);
                                    return;
                                }

                                if (avg_x > Middle8K_X_Position)
                                {
                                    if (workpageGroup.WorkbookPages.Count == 1)
                                    {
                                        _log.LogInformation("8K Common mac:{} paperid: {} , 未找到试卷页面信息2", data.Mac, paperid);
                                        return;
                                    }

                                    var workpage = workpageGroup.WorkbookPages.Last();
                                    uwoo_pageid = workpage.PageId;
                                    data.PageId = uwoo_pageid;
                                    data.PageNo = workpage.Page;
                                }
                                else
                                {
                                    var workpage = workpageGroup.WorkbookPages.First();
                                    uwoo_pageid = workpage.PageId;
                                    data.PageId = uwoo_pageid;
                                    data.PageNo = workpage.Page;
                                }
                            }
                        }

                        if (uwoo_pageid <= 0 || data.PageId <= 0)
                        {
                            _log.LogInformation("8K mac:{} paperid: {} , 未找到试卷页面信息", data.Mac, paperid);
                            return;
                        }
                        break;
                        #endregion 
                    }
            }

            if (data.Page is 24 or 25)
            {
                return;
            }

            #endregion

            #region 生生互评

            if (data.Page is 5 or 6)
            {
                var is_each_correct = false;
                if (!string.IsNullOrWhiteSpace(student.ClassId))
                {
                    is_each_correct = await _paper_log_redis_service.HGetCorrectAsync(student.ClassId, paperid);
                }

                if (is_each_correct)
                {
                    var avg_x = dots.Average(x => x.X);
                    var avg_y = dots.Average(x => x.Y);
                    //尝试获取被勾选的学号
                    var by_correct_student_no = GetCheckedStudentNo(avg_y, avg_x);
                    var correct_paperid = paperid;
                    StudentInfo by_correct_student = null;
                    if (!string.IsNullOrWhiteSpace(by_correct_student_no))
                    {
                        //获取当前被批阅的学生信息
                        by_correct_student = await GetStudent(student.ClassId, by_correct_student_no);
                        if (by_correct_student != null)
                        {
                            //勾选学号操作只缓存当前被批阅的学生信息
                            await _teacher_correct_redis_service.SetCorrectStudentInfoAsync(student.UserId, by_correct_student);
                            await _teacher_correct_redis_service.SAddByCorrectStudentInfoAsync(by_correct_student.UserId, student);

                            //推送当前被批阅学生信息
                            var each_correct_model = new RealtimeEachCorrectModel
                            {
                                PaperId = correct_paperid,
                                ClassId = student.ClassId,
                                PageId = uwoo_pageid,
                                StudentId = student.UserId,
                                StudentName = student.NickName ?? student.RealName,
                                StudentNo = student.StudentNo,
                                ByStudentId = by_correct_student.UserId,
                                ByStudentNo = by_correct_student.StudentNo
                            };
                            await _publisher.Publish(each_correct_model).ConfigureAwait(false);
                            return;
                        }
                    }

                    if (string.IsNullOrWhiteSpace(by_correct_student_no))
                    {
                        by_correct_student = await _teacher_correct_redis_service.GetCorrectStudentInfoAsync(student.UserId);
                    }

                    if (by_correct_student == null)
                    {
                        //未勾选学号, 直接进行批阅
                        _log.LogError("当前未选中待批阅的学生: paperid: {paperid} , classid: {classid} , studentid: {studentid} , mac: {mac}", correct_paperid, student.ClassId, student.UserId, data.Mac);
                        return;
                    }

                    //尝试获取当前勾选的页码
                    var correct_page_no = GetPageNo(avg_x, avg_y);
                    if (correct_page_no > 0)
                    {
                        //正常勾选页码, 缓存当前勾选页码
                        await _paper_log_redis_service.SetStudentCurrentCorrectPageNoAsync(student.UserId, correct_paperid, correct_page_no);
                        return;
                    }

                    //尝试获取勾选的页码
                    correct_page_no = await _paper_log_redis_service.GetStudentCurrentCorrectPageNoAsync(student.UserId, correct_paperid);

                    if (correct_page_no <= 0)
                    {
                        //缓存中没有数据, 代表未勾选直接批阅, 取默认值
                        correct_page_no = 1;
                    }

                    if (data.Page == 6)
                    {
                        correct_page_no += 1;
                    }

                    //尝试获取试卷页面信息
                    var correct_workbook_page = await _paper_info_redis_service.HGetPaperPageInfoAsync(correct_paperid, correct_page_no) ?? await GetWorkbookPage(correct_paperid, correct_page_no);
                    if (correct_workbook_page == null)
                    {
                        _log.LogError("paperid: {paperid} , 未找到试卷页面信息", paperid);
                        return;
                    }

                    #region 发送实时笔迹

                    //笔迹回放顺序
                    var xlist = data.Dots.OrderBy(x => x.Oid).ToList();
                    //发送教师正在批改的学生实时笔迹数据
                    var dotdata = new RealtimeDotModel
                    {
                        UserType = 1,
                        PaperId = correct_workbook_page.PaperId,
                        UserId = by_correct_student.UserId,
                        Page = data.Page,
                        PageId = correct_workbook_page.PageId,
                        TeacherUserId = student.UserId,
                        Mode = 2,
                        Dots = xlist,
                        CurrentPageNo = correct_workbook_page.Page,
                        ClassId = student.ClassId
                    };
                    await _publisher.Publish(dotdata).ConfigureAwait(false);

                    #endregion

                    #region 笔迹入库

                    var teacher_penlog = new TeacherPenLog
                    {
                        Mid = _snowflake.NewId(),
                        Mac = data.Mac,
                        TeacherId = student.UserId,
                        UserId = by_correct_student.UserId,
                        Page = correct_workbook_page.PageId,
                        PageId = correct_workbook_page.PageId,
                        Dots = _mapper.Map<List<DotBase>>(dots)
                    };
                    await _teacher_pen_log_service.AddAsync(teacher_penlog, student.SchoolId).ConfigureAwait(false);
                    _log.LogInformation("互评笔迹入库: requestid: {requestid}, teacherid: {teacherid} , studentno: {studentno} , studentid: {studentid}, paperid: {paperid}, pageid: {pageid}", data.RequestId, student.UserId, by_correct_student.StudentNo, by_correct_student.UserId, correct_workbook_page.PaperId, correct_workbook_page.PageId.ToString());

                    #endregion

                    return;
                }
            }

            #endregion

            #region 实时传输笔迹

            var dostatus = 0;
            var avg_x_8K = 0d;
            var avg_y_8K = 0d;
            var tempPenLogs = new List<PenDot>();

            if (data.Page==19||data.Page==20)
            {
                avg_x_8K = dots.Average(x => x.X);
                avg_y_8K = dots.Average(x => x.Y);

                if (avg_x_8K>Middle8K_X_Position)
                {
                    foreach (var dot in dots)
                    {
                        tempPenLogs.Add(new PenDot
                        {
                            Oid=dot.Oid,
                            X=Convert.ToInt32(dot.X-Middle8K_X_Position),
                            Y=dot.Y,
                            Type=dot.Type,
                            Page=dot.Page,
                            Time=dot.Time,
                            BookNo=dot.BookNo,
                            Pressure=dot.Pressure
                        });
                    }
                }
                else
                {
                    tempPenLogs.AddRange(dots);
                }

                dostatus = await _teacher_correct_redis_service.HGetCorrectStudentStatusAsync(paperid, data.UserId);
                var realtime_dot_data = new RealtimeDotModel
                {
                    UserType = 0,
                    PaperId = paperid,
                    UserId = student.UserId,
                    ClassId = student.ClassId,
                    PageId = data.PageId,
                    Page = data.Page,
                    Mode = 0,
                    Dots = tempPenLogs
                };
                if (dostatus > 1)
                {
                    realtime_dot_data.Type = 1;
                }
                var cur_workpageGroups = await GetWorkbookPageGroups(paperid);
                if (cur_workpageGroups.Count==0)
                {
                    _log.LogInformation("8K 未查询到试卷页面信息 ,mac:{} classid: {classid} , paperid: {paperid}", data.Mac, student.ClassId, paperid);
                    return;
                }

                var cur_workpage = cur_workpageGroups
                         .SelectMany(z => z.WorkbookPages)
                         .FirstOrDefault(z => z.PageId == realtime_dot_data.PageId);

                if (cur_workpage==null)
                {
                    _log.LogInformation("8K 未查询到试卷页面信息 ,mac:{} classid: {classid} , paperid: {paperid}", data.Mac, student.ClassId, paperid);
                    return;
                }

                realtime_dot_data.CurrentPageNo = cur_workpage.Page;
                await _publisher.Publish(realtime_dot_data).ConfigureAwait(false);
            }
            else
            {
                dostatus = await _teacher_correct_redis_service.HGetCorrectStudentStatusAsync(paperid, data.UserId);
                var realtime_dot_data = new RealtimeDotModel
                {
                    UserType = 0,
                    PaperId = paperid,
                    UserId = student.UserId,
                    ClassId = student.ClassId,
                    PageId = data.Page > 1000 ? data.Page : data.PageId,
                    Page = data.Page,
                    Mode = 0,
                    Dots = dots
                };

                if(data.Page>=2001 && data.Page<= 2500)
                {
                    realtime_dot_data.PageId = page;
                    realtime_dot_data.Page = page;
                }

                realtime_dot_data.Dots.ForEach(z =>
                {
                    z.Color=color;
                });

                if (dostatus > 1)
                {
                    realtime_dot_data.Type = 1;
                }
                var cur_workpage_list = await GetWorkbookPage(paperid);
                if (cur_workpage_list is not { Count: > 0 })
                {
                    _log.LogError("未查询到试卷页面信息 , classid: {classid} , paperid: {paperid}", student.ClassId, paperid);
                    return;
                }

                if (dostatus > 1)
                {
                    realtime_dot_data.Type = 1;
                }

                var cur_workpage = cur_workpage_list.FirstOrDefault(x => x.PageId == realtime_dot_data.PageId);
                if (cur_workpage == null)
                {
                    _log.LogError("未查询到试卷页面信息 , classid: {classid} , paperid: {paperid}", student.ClassId, paperid);
                    return;
                }

                realtime_dot_data.CurrentPageNo = cur_workpage.Page;
                await _publisher.Publish(realtime_dot_data).ConfigureAwait(false);
            }

            #endregion

            #region 作答模式

            if (data.Page==19||data.Page==20)
            {
                if (dostatus < 2)
                {
                    if (dostatus == 0)
                    {
                        await _teacher_correct_redis_service.HSetCorrectStudentStatusAsync(paperid, data.UserId, 1);
                    }

                    var pen_log = new Pen8KLog
                    {
                        Mid = _snowflake.NewId(),
                        Mac = data.Mac,
                        UserId = data.UserId,
                        Page = data.Page,
                        PageId = uwoo_pageid,
                        Dots = _mapper.Map<List<DotBase>>(dots)
                    };
                    await _pen8KLogService.AddAsync(pen_log, student.SchoolId).ConfigureAwait(false);

                    _log.LogInformation
                    (
                        "8K 作答数据入库:mac:{}, paperid: {} , pageid: {} , userid: {}", data.Mac, paperid,
                        uwoo_pageid.ToString(), data.UserId
                    );

                    #region Job Seed

                    try
                    {
                        await _studentAnswerPaperRedisService.SetItemAsync(new Contracts.StudentAnswerPaperDto
                        {
                            PaperId= paperid,
                            ClassId= student.ClassId,
                            StudentId=student.UserId,
                            PageId= data.PageId,
                            CreateTime=DateTime.Now
                        });
                    }
                    catch (Exception ex)
                    {
                        _log.LogError("SetItemAsync Ex:{}", ex);
                    }

                    #endregion
                }

                if (dostatus > 1)
                {
                    //订正记录
                    await _teacher_correct_redis_service.HSetStudentCorrectPaperAsync(data.UserId, paperid);
                    var correct_penlog = new CorrectPen8KLog
                    {
                        Mid = _snowflake.NewId(),
                        Mac = data.Mac,
                        UserId = data.UserId,
                        Page = data.Page,
                        PageId = uwoo_pageid,
                        Dots = _mapper.Map<List<DotBase>>(dots)
                    };
                    await _correctPen8KLogService.AddAsync(correct_penlog, student.SchoolId).ConfigureAwait(false);
                    _log.LogInformation
                    (
                        "8K 订正数据入库:mac:{}, paperid: {} , pageid: {} , userid: {}", data.Mac, paperid,
                        uwoo_pageid.ToString(), data.UserId
                    );
                }

                //带完成提交和后续任务处理
                var mqdata = new Pen8KDotFinalData
                {
                    UserId = data.UserId,
                    Page = data.Page,
                    PageId = uwoo_pageid,
                    Dots = dots,
                    PaperId = paperid,
                    DoStatus = dostatus
                };
                await _publisher.Publish(mqdata).ConfigureAwait(false);

                if (dostatus > 1)
                {
                    //更新状态为已批阅
                    var answer_state_model = new StudentAnswerStateModel
                    {
                        UserId = data.UserId,
                        PaperId = paperid,
                        PageId = data.Page,
                        MDType = 3
                    };
                    await _publisher.Publish(answer_state_model).ConfigureAwait(false);
                }
            }
            else
            {
                //默认整卷模式
                var mode = 1;
                if ((data.Page == 5 || data.Page == 6) || (data.Page >= 2000 && data.Page <= 2500))
                {
                    mode = await _paper_log_redis_service.GetPaperAnswerModeAsync(student.ClassId, paperid);
                }

                if (mode == 2)
                {
                    #region 单题模式

                    #region 单题区域

                    var markinfos = await _paper_info_redis_service.SMemberMarkInfoAsync(paperid);
                    if (markinfos is not { Count: > 0 })
                    {
                        const string sql = """
                                       SELECT [WorkbookPageId],
                                              [Range],
                                              [ItemTypeId],
                                              [PaperId],
                                              [ItemId],
                                              [Page]
                                       FROM [dbo].[WorkbookMarkInfo]
                                       WHERE [PaperId] = @paperid
                                             AND [QuestionLevel] = 2;
                                       """;
                        var marklist = await _dapper_service.QueryListAsync<WorkbookMarkInfo>(sql, new
                        {
                            paperid
                        });
                        if (!marklist.Any())
                        {
                            _log.LogError("未查询到单题框选标注信息 , classid: {classid} , paperid: {paperid}", student.ClassId, paperid);
                            return;
                        }

                        markinfos = marklist.ToList();
                        await _paper_info_redis_service.SAddMarkInfoAsync(paperid, markinfos);
                    }

                    if (markinfos is not { Count: > 0 })
                    {
                        _log.LogError("未查询到单题框选标注信息 , classid: {classid} , paperid: {paperid}", student.ClassId, paperid);
                        return;
                    }

                    var mark_item = LocateItemInfo(dots, markinfos, data.PageNo);
                    if (mark_item == null)
                    {
                        _log.LogError("单题未命中题目区域 , classid: {classid} , paperid: {paperid}, dots: {dots}", student.ClassId, paperid, dots.ToJsonString());
                        return;
                    }

                    var range = mark_item.Range.ToEntity<List<XPoint>>();
                    if (range is not { Count: 4 })
                    {
                        _log.LogError("单题框选标注坐标数据不正确 , paperid: {paperid} , itemid: {itemid}", paperid, mark_item.ItemId);
                        return;
                    }

                    var top_left = range[0];
                    var bottom_right = range[2];
                    //获取在当前题目区域书写的笔迹
                    var hit_dots = data.Dots.Where(x => x.X >= top_left.X && x.X <= bottom_right.X && x.Y >= top_left.Y && x.Y <= bottom_right.Y).ToList();
                    if (hit_dots.Count > 0 && hit_dots.Count != data.Dots.Count)
                    {
                        var hit_dots_oids = hit_dots.Select(x => x.Oid).ToArray();
                        //未命中当前题目区域的点位
                        var miss_dots = data.Dots.Where(x => !hit_dots_oids.Contains(x.Oid));
                        _log.LogInformation("单题模式未命中当前题目区域笔迹记录: studentid: {studentid} , paperid: {paperid} , itemid: {itemid} , dots: {dots}", student.UserId, paperid, mark_item.ItemId, miss_dots.ToJsonString());
                    }

                    if (hit_dots.Count == 0)
                    {
                        _log.LogError("单题模式未在题目区域书写 , studentid: {studentid} , paperid: {paperid} , itemid: {itemid}", student.UserId, paperid, mark_item.ItemId);
                        return;
                    }

                    var last = hit_dots.LastOrDefault();
                    if (last == null)
                    {
                        return;
                    }

                    if (last.Type != 2)
                    {
                        hit_dots.Last().Type = 2;
                    }

                    #endregion

                    #region 笔迹入库
     
                    var single_pendot = new SingleItemPenLog
                    {
                        Mid = _snowflake.NewId(),
                        Mac = data.Mac,
                        UserId = data.UserId,
                        Page = page,
                        PageId = data.PageId,
                        ItemId = mark_item.ItemId,
                        PaperId = paperid,
                        Dots = _mapper.Map<List<DotBase>>(hit_dots)
                    };


                    single_pendot.Dots.ForEach(z =>
                    {
                        z.Color=color;
                    });

                    await _single_item_pen_log_service.AddAsync(single_pendot, student.SchoolId).ConfigureAwait(false);

                    _log.LogInformation
                    (
                        "单题作答数据入库: paperid: {paperid} , itemid: {itemid} , pageid: {uwoo_pageid} , userid: {userid}", paperid, mark_item.ItemId,
                        data.PageId.ToString(), data.UserId
                    );

                    #endregion

                    #region 完成提交

                    //查询单题完成提交框选区域
                    var finish_mark_infos = await _paper_info_redis_service.SMemberSingleFinishMarkInfoAsync(paperid);
                    if (finish_mark_infos is not { Count: > 0 })
                    {
                        const string sql = """
                                       SELECT DISTINCT
                                              [ItemId],
                                              [PaperId],
                                              [WorkbookPageId],
                                              [Page],
                                              [ItemTypeId],
                                              [Range]
                                       FROM [dbo].[WorkbookMarkInfo]
                                       WHERE [Deleted] = 0
                                             AND [PaperId] = @paperid
                                             AND [QuestionLevel] = 4;
                                       """;
                        var finish_mark_list = await _dapper_service.QueryListAsync<WorkbookMarkInfo>(sql, new
                        {
                            paperid
                        });
                        if (finish_mark_list == null || !finish_mark_list.Any())
                        {
                            _log.LogError("未查询到单题完成提交框选标注信息 , classid: {classid} , paperid: {paperid}", student.ClassId, paperid);
                            return;
                        }

                        var xfinish_mark_list = finish_mark_list.ToList();
                        await _paper_info_redis_service.SAddSingleFinishMarkInfoAsync(paperid, xfinish_mark_list).ConfigureAwait(false);
                        finish_mark_infos = xfinish_mark_list;
                    }

                    if (finish_mark_infos.Count == 0)
                    {
                        _log.LogError("未查询到单题完成提交框选标注信息 , classid: {classid} , paperid: {paperid}", student.ClassId, paperid);
                        return;
                    }

                    //查询当前题目完成提交区域
                    var single_finish_mark_info = finish_mark_infos.FirstOrDefault(x => x.ItemId == mark_item.ItemId);
                    if (single_finish_mark_info == null)
                    {
                        _log.LogError("未查询到单题完成提交框选标注信息 , classid: {classid} , paperid: {paperid} , itemid: {itemid}", student.ClassId, paperid, mark_item.ItemId);
                        return;
                    }

                    var finish_range = single_finish_mark_info.Range.ToEntity<List<XPoint>>();
                    if (finish_range is not { Count: 4 })
                    {
                        _log.LogError("单题完成提交框选标注坐标数据不正确 , paperid: {paperid} , itemid: {itemid}", paperid, mark_item.ItemId);
                        return;
                    }

                    var finish_top_left = finish_range[0];
                    var finish_bottom_right = finish_range[2];
                    var avg_finish_x = single_pendot.Dots.Average(x => x.X);
                    var avg_finish_y = single_pendot.Dots.Average(x => x.Y);
                    //是否勾选完成提交
                    var is_check_finish = avg_finish_x >= finish_top_left.X && avg_finish_x <= finish_bottom_right.X && avg_finish_y >= finish_top_left.Y && avg_finish_y <= finish_bottom_right.Y;
                    if (is_check_finish)
                    {
                        //当前题目已勾选提交
                        var single_finish_data = new SingleItemSubmitDto
                        {
                            PaperId = paperid,
                            ClassId = student.ClassId,
                            ItemId = mark_item.ItemId,
                            PageId = data.PageId,
                            UserId = student.UserId
                        };
                        await _publisher.Publish(single_finish_data).ConfigureAwait(false);
                        _log.LogInformation("单题提交: paperid: {paperid} , itemid: {itemid} , userid: {userid} , mac: {mac}", paperid, mark_item.ItemId, student.UserId, data.Mac);
                    }

                    #endregion

                    #endregion
                }
                else
                {
                    #region 整卷模式
                    if (dostatus < 2)
                    {
                        if (dostatus == 0)
                        {
                            await _teacher_correct_redis_service.HSetCorrectStudentStatusAsync(paperid, data.UserId, 1);
                        }

                        var pen_log = new PenLog
                        {
                            Mid = _snowflake.NewId(),
                            Mac = data.Mac,
                            UserId = data.UserId,
                            Page = page,
                            PageId = uwoo_pageid,
                            Dots = _mapper.Map<List<DotBase>>(dots)
                        };

                        pen_log.Dots.ForEach(z =>
                        {
                            z.Color=color;
                        });

                        await _pen_log_service.AddAsync(pen_log, student.SchoolId).ConfigureAwait(false);

                        _log.LogInformation
                        (
                            "作答数据入库:mac:{}, paperid: {paperid} , pageid: {uwoo_pageid} , userid: {userid}", data.Mac, paperid,
                            uwoo_pageid.ToString(), data.UserId
                        );

                        #region Job Seed

                        try
                        {
                            await _studentAnswerPaperRedisService.SetItemAsync(new Contracts.StudentAnswerPaperDto
                            {
                                PaperId= paperid,
                                ClassId= student.ClassId,
                                StudentId=student.UserId,
                                PageId= data.PageId,
                                CreateTime=DateTime.Now
                            });
                        }
                        catch (Exception ex)
                        {
                            _log.LogError("SetItemAsync Ex:{}", ex);
                        }

                        #endregion
                    }

                    if (dostatus > 1)
                    {
                        //订正记录
                        await _teacher_correct_redis_service.HSetStudentCorrectPaperAsync(data.UserId, paperid);
                        var correct_penlog = new CorrectPenLog
                        {
                            Mid = _snowflake.NewId(),
                            Mac = data.Mac,
                            UserId = data.UserId,
                            Page = page,
                            PageId = uwoo_pageid,
                            Dots = _mapper.Map<List<DotBase>>(dots)
                        };
                        await _correct_pen_log_service.AddAsync(correct_penlog, student.SchoolId).ConfigureAwait(false);
                        _log.LogInformation
                        (
                            "订正数据入库:mac:{}, paperid: {paperid} , pageid: {uwoo_pageid} , userid: {userid}", data.Mac, paperid,
                            uwoo_pageid.ToString(), data.UserId
                        );
                    }

                    //带完成提交和后续任务处理
                    var mqdata = new PenDotFinalData
                    {
                        UserId = data.UserId,
                        Page = page,
                        PageId = uwoo_pageid,
                        Dots = dots,
                        PaperId = paperid,
                        DoStatus = dostatus,
                    };
                    await _publisher.Publish(mqdata).ConfigureAwait(false);

                    if (dostatus > 1)
                    {
                        //更新状态为已批阅
                        var answer_state_model = new StudentAnswerStateModel
                        {
                            UserId = data.UserId,
                            PaperId = paperid,
                            PageId = page,
                            MDType = 3
                        };
                        await _publisher.Publish(answer_state_model).ConfigureAwait(false);
                    }

                    #endregion
                }
            }

            #endregion
        }
        catch (Exception e)
        {
            _log.LogCritical("[{method}_Error]: {msg} , trace: {trace}", nameof(StudentPenDot), e.Message, e.StackTrace);
        }
    }

    #region 定位题目区域

    /// <summary>
    /// /定位题目区域
    /// </summary>
    /// <param name="dots"></param>
    /// <param name="mark_infos"></param>
    /// <param name="pageno"></param>
    /// <returns></returns>
    private static WorkbookMarkInfo LocateItemInfo(List<PenDot> dots, List<WorkbookMarkInfo> mark_infos, int pageno)
    {
        var avg_x = dots.Average(x => x.X);
        var avg_y = dots.Average(x => x.Y);
        var xmark_infos = mark_infos.Where(x => x.Page == pageno);
        return (from item in xmark_infos
                where !string.IsNullOrEmpty(item.Range)
                let range = item.Range.ToEntity<List<MatrixPoint>>()
                where range is { Count: 4 }
                let pa = range[0]
                let pc = range[2]
                where avg_x >= pa.X && avg_x <= pc.X && avg_y >= pa.Y && avg_y <= pc.Y
                select item).FirstOrDefault();
    }

    #endregion

    #region 试卷组合页

    /// <summary>
    /// 获取试卷组合页
    /// </summary>
    /// <param name="dots">点位</param>
    /// <param name="pageid">页码id</param>
    /// <returns></returns>
    private async Task<(string, int)> GetCombinationPaperId(List<PenDot> dots, int pageid)
    {
        var paperid = "";
        var uwoo_pageid = 0;
        try
        {
            //查询组合页区域范围
            var combination_ranges = await _paper_info_redis_service.SMemberCombinationMarkRangeInfoListAsync(pageid);
            if (combination_ranges == null || combination_ranges.Count == 0)
            {
                //查询组合页信息
                const string combination_ranges_sql = """
                                                      SELECT [b].[Id] AS [MarkId],
                                                             [b].[WorkbookPageId] AS [WorkbookPageId],
                                                             [a].[PaperId],
                                                             [a].[Page],
                                                             [b].[Range]
                                                      FROM [dbo].[WorkbookPage] AS a
                                                          INNER JOIN [dbo].[WorkbookMarkInfo] AS b
                                                              ON [a].[Id] = [b].[WorkbookPageId]
                                                      WHERE [a].[IsCombination] = 1
                                                            AND [b].[Deleted] = 0
                                                            AND [b].[QuestionLevel] = 2
                                                            AND [b].[Range] IS NOT NULL
                                                            AND [b].[Range] <> '[]'
                                                            AND [a].[PageId] = @pageid;
                                                      """;
                var xcombination_ranges = await _dapper_service.QueryListAsync<CombinationMarkRangeInfo>
                (
                    combination_ranges_sql, new
                    {
                        pageid
                    }
                );
                if (xcombination_ranges == null || !xcombination_ranges.Any())
                {
                    _log.LogError("未查询到试卷组合页区域信息 , {msg}", dots.ToJsonString());
                    return (paperid, uwoo_pageid);
                }

                combination_ranges = xcombination_ranges.ToList();

                //缓存试卷组合页区域列表
                await _paper_info_redis_service.SAddCombinationMarkRangeInfoListAsync(pageid, combination_ranges);
            }

            paperid = GetCombinationPaperId(dots, combination_ranges);
            uwoo_pageid = pageid;
            return (paperid, uwoo_pageid);
        }
        catch (Exception e)
        {
            _log.LogError("获取试卷组合页区域信息出错 , {msg} , {error}", dots.ToJsonString(), e.Message);
            return (paperid, uwoo_pageid);
        }
    }

    /// <summary>
    /// 根据试卷组合页获取试卷id
    /// </summary>
    /// <param name="dots">点位</param>
    /// <param name="range_infos">区域</param>
    /// <returns></returns>
    private string GetCombinationPaperId(List<PenDot> dots, IEnumerable<CombinationMarkRangeInfo> range_infos)
    {
        var paperid = "";
        try
        {
            //获取点位平均值
            var avg_x = Convert.ToInt32(dots.Average(x => x.X));
            var avg_y = Convert.ToInt32(dots.Average(x => x.Y));
            foreach (var item in from item in range_infos
                                 let xrange = item.Range.ToEntity<List<XPoint>>()
                                 where xrange.Count is > 0 and 4
                                 let dot_a = xrange[0]
                                 let dot_c = xrange[2]
                                 let iscross = IsInMatrix(dot_a, dot_c, new XPoint(avg_x, avg_y))
                                 where iscross
                                 select item)
            {
                //找到任意答案框区域, 匹配当前答案对应的试卷信息
                paperid = item.PaperId;
                break;
            }

            return paperid;
        }
        catch (Exception)
        {
            _log.LogError("未获取试卷组合页对应试卷 , {msg}", dots.ToJsonString());
            return paperid;
        }
    }

    /// <summary>
    /// 判断点位是否在指定矩阵之内
    /// </summary>
    /// <param name="dot_a">点位区域A</param>
    /// <param name="dot_c">点位区域C</param>
    /// <param name="point">点位坐标</param>
    /// <returns></returns>
    private static bool IsInMatrix(XPoint dot_a, XPoint dot_c, XPoint point)
    {
        var is_inmatrix = point.X >= dot_a.X
                          && point.X <= dot_c.X
                          && point.Y >= dot_a.Y
                          && point.Y <= dot_c.Y;
        return is_inmatrix;
    }

    #endregion

    #region 识别数字

    /// <summary>
    /// 识别数字
    /// </summary>
    /// <param name="dots">点位数据</param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    private async Task<int> NumberRecognition(List<PenDot> dots)
    {
        return await _my_script_client.NumberRecognition(dots);
    }

    #endregion

    #region 试卷页面信息

    /// <summary>
    /// 获取或更新试卷缓存页面信息
    /// </summary>
    /// <param name="paperid">试卷id</param>
    /// <param name="pageno">试卷页码编号</param>
    /// <returns></returns>
    private async Task<WorkbookPage> GetWorkbookPage(string paperid, int pageno)
    {
        const string workpage_sql = """
                                    SELECT *
                                    FROM [dbo].[WorkbookPage]
                                    WHERE [PaperId] = @paperid;
                                    """;

        //从数据库中获取试卷多页数据
        var workpages = await _dapper_service.QueryListAsync<WorkbookPage>
        (
            workpage_sql, new
            {
                paperid
            }
        );
        if (workpages == null || !workpages.Any())
        {
            return null;
        }

        //缓存试卷多页数据
        foreach (var item in workpages)
        {
            await _paper_info_redis_service.HSetPaperPageInfoAsync
            (
                item.PaperId,
                item.Page,
                item
            );
        }

        //尝试获取试卷单页数据
        var workpage = workpages.FirstOrDefault(x => x.Page == pageno);
        return workpage;
    }

    /// <summary>
    /// 获取或更新试卷缓存页面信息
    /// </summary>
    /// <param name="paperid">试卷id</param>
    /// <returns></returns>
    private async Task<List<WorkbookPage>> GetWorkbookPage(string paperid)
    {
        var list = await _paper_info_redis_service.HGetAllPaperPageListAsync(paperid);
        if (list is { Count: > 0 })
        {
            return list;
        }
        list = [];
        const string workpage_sql = """
                                    SELECT *
                                    FROM [dbo].[WorkbookPage]
                                    WHERE [PaperId] = @paperid;
                                    """;

        //从数据库中获取试卷多页数据
        var workpages = await _dapper_service.QueryListAsync<WorkbookPage>
        (
            workpage_sql, new
            {
                paperid
            }
        );
        if (workpages == null || !workpages.Any())
        {
            return list;
        }
        //缓存试卷多页数据
        foreach (var item in workpages)
        {
            list.Add(item);
            await _paper_info_redis_service.HSetPaperPageInfoAsync
            (
                item.PaperId,
                item.Page,
                item
            );
        }

        return list;
    }

    /// <summary>
    /// 分组
    /// </summary>
    /// <param name="paperId"></param>
    /// <returns></returns>
    private async Task<List<WorkbookPageGroup>> GetWorkbookPageGroups(string paperId)
    {
        var finalList = new List<WorkbookPageGroup>();

        var workpages = await _paper_info_redis_service.HGetAllPaperPageListAsync(paperId);

        if (workpages == null || workpages.Count==0)
        {
            var sql = @"
SELECT *
FROM [dbo].[WorkbookPage]
WHERE [PaperId] = @paperid;                     
";
            workpages = (await _dapper_service.QueryListAsync<WorkbookPage>
            (
                sql, new
                {
                    paperId
                }
            )).ToList();
            if (workpages == null || workpages.Count==0)
            {
                return null;
            }

            foreach (var item in workpages)
            {
                await _paper_info_redis_service.HSetPaperPageInfoAsync
                (
                    item.PaperId,
                    item.Page,
                    item
                );
            }
        }

        if (workpages?.Count>0)
        {
            workpages=workpages.OrderBy(z => z.PageId).ToList();
            var index = 0;
            var paperNumber = 1;
            for (int i = 0; i < workpages.Count; i += 2)
            {
                ++index;
                var group = workpages.Skip(i).Take(2).ToList();

                if (index==1||index==2)
                {
                    paperNumber=1;
                }
                else if (index==3||index==4)
                {
                    paperNumber=5;
                }
                else if (index==5||index==6)
                {
                    paperNumber=9;
                }
                else if (index==7||index==8)
                {
                    paperNumber=13;
                }

                finalList.Add(new WorkbookPageGroup
                {
                    Index=index,
                    PageNumber=paperNumber,
                    WorkbookPages=group
                });
            }
        }

        return finalList;
    }

    #endregion

    #endregion

    #region TeacherInfo

    /// <summary>
    /// 教师笔笔迹处理
    /// </summary>
    /// <param name="data"></param>
    /// <param name="schoolid">学校id</param>
    /// <param name="paperid">当前批阅试卷信息</param>
    /// <param name="classid">班级id</param>
    private async Task TeacherPenDot(TeacherPenDotData data, string schoolid, string paperid, string classid)
    {
        if (data.Dots is { Count: <= 0 })
        {
            _log.LogInformation("mac:{mac}  教师没有批改笔迹", data.Mac);
            return;
        }

        if (data.Page is 5 or 6)
        {
            var is_media = await _paper_info_redis_service.HGetPaperIdIsMediaAsync(paperid);
            if (is_media)
            {
                var avg_x = data.Dots.Average(x => x.X);
                var avg_y = data.Dots.Average(x => x.Y);
                var coord = await GetCoordInfo(avg_x, avg_y, paperid, currentPageNo: data.CurrentPageNo, page: data.Page);
                if (coord != null)
                {
                    switch (coord.CoordType)
                    {
                        case 1 or 3 or 5:
                            {
                                #region 请求播放

                                var media_info = coord.CoordType switch
                                {
                                    1 => await GetMediaInfo(paperid, coord.ItemId, 1, 1),
                                    3 => await GetMediaInfo(paperid, coord.ItemId, 2, 1, data.TeacherId, data.UserId),
                                    5 => await GetMediaInfo(paperid, coord.ItemId, 3, 1, data.TeacherId, data.UserId),
                                    _ => null
                                };
                                if (media_info == null || string.IsNullOrWhiteSpace(media_info.Url))
                                {
                                    _log.LogInformation("mac:{mac}  教师没有找到media_info, paperId:{paperId} itemId:{itemId}", data.Mac, paperid, coord.ItemId);

                                    return;
                                }

                                var play_request = new PlayRequest
                                {
                                    Url = media_info.Url,
                                    BaseRequest = new PenMediaRequest
                                    {
                                        Page = data.Page,
                                        PageId = data.PageId,
                                        StudentId = data.UserId,
                                        PaperId = paperid,
                                        Mac = data.Mac,
                                        UserRole = 1,
                                        TeacherId = data.TeacherId
                                    }
                                };

                                _log.LogInformation("mac:{mac}  教师请求播放, play_request:{play_request}", data.Mac, play_request.ToJsonString());

                                await _rpc_service_client.PlayServiceAsync(play_request);
                                return;

                                #endregion
                            }
                        case 4:
                            {
                                #region 请求点评录音

                                var record_request = new RecordRequest
                                {
                                    ItemId = coord.ItemId,
                                    WorkbookPageId = coord.WorkBookPageId,
                                    BaseRequest = new PenMediaRequest
                                    {
                                        Page = data.Page,
                                        PageId = data.PageId,
                                        StudentId = data.UserId,
                                        PaperId = paperid,
                                        Mac = data.Mac,
                                        UserRole = 1,
                                        TeacherId = data.TeacherId
                                    }
                                };

                                _log.LogInformation("mac:{mac}  教师请求点评录音, play_request:{play_request}", data.Mac, record_request.ToJsonString());

                                await _rpc_service_client.RecordServiceAsync(record_request);

                                return;

                                #endregion
                            }
                        default:
                            return;
                    }
                }
            }
        }


        #region 发送实时笔迹

        //笔迹回放顺序
        var xlist = data.Dots.OrderBy(x => x.Oid).ToList();

        //发送教师正在批改的学生实时笔迹数据
        var dotdata = new RealtimeDotModel
        {
            UserType = 1,
            PaperId = paperid,
            UserId = data.UserId,
            Page = data.Page,
            PageId = data.PageId,
            TeacherUserId = data.TeacherId,
            Mode = 1,
            Dots = xlist,
            CurrentPageNo = data.CurrentPageNo,
            ClassId = classid
        };
        await _publisher.Publish(dotdata).ConfigureAwait(false);

        _log.LogInformation("mac:{mac}  教师批改发送时时笔迹, paperId:{paperId} userId:{userId} teacherId:{teacherId} classId:{classId}", data.Mac, dotdata.PaperId, dotdata.UserId, dotdata.TeacherUserId, dotdata.ClassId);

        #endregion

        #region 笔迹入库

        var teacher_penlog = new TeacherPenLog
        {
            Mid = _snowflake.NewId(),
            Mac = data.Mac,
            Page = data.Page,
            PageId = data.PageId,
            TeacherId = data.TeacherId,
            UserId = data.UserId,
            Dots = _mapper.Map<List<DotBase>>(xlist)
        };
        _log.LogInformation($"---教师批改笔迹落库----{ teacher_penlog.ToJsonString()}");
        await _teacher_pen_log_service.AddAsync(teacher_penlog, schoolid).ConfigureAwait(false);

        await _teacher_correct_redis_service.HSetCorrectStudentStatusAsync(paperid, data.UserId, 3);

        #endregion

        #region 教师笔批改实时识别队列

        var pen_times = xlist.Select(x => x.Time).Distinct().ToList();
        var correct_data = new TeacherCorrectData
        {
            PaperId = paperid,
            StudentId = data.UserId,
            PageId = data.PageId,
            StartTime = pen_times.Min(),
            EndTime = pen_times.Max()
        };

        await _publisher.Publish(correct_data).ConfigureAwait(false);

        #endregion
    }

    #endregion

    #region 卷码纸

    /// <summary>
    /// 获取勾选的学号
    /// </summary>
    /// <param name="y">Y</param>
    /// <param name="x">X</param>
    /// <remarks>铺码纸专用, 教师批改铺码纸, 勾选学号</remarks>
    /// <returns></returns>
    private static string GetCheckedStudentNo(double y, double x)
    {
        try
        {
            const int row = 3;
            const int col = 20;
            var array = new int[row, col];
            var num = 1;
            for (var i = 0; i < row; i++)
            {
                for (var j = 0; j < col; j++)
                {
                    array[i, j] = num;
                    num++;
                }
            }

            var tx = Convert.ToInt32(Math.Floor(Convert.ToDecimal(Convert.ToInt32(x - 24) / Convert.ToInt32(80))));
            var ty = Convert.ToInt32(Math.Floor(Convert.ToDecimal(Convert.ToInt32(y - 24) / Convert.ToInt32(63))));
            if (tx <= 0)
            {
                tx = 0;
            }

            if (ty <= 0)
            {
                ty = 0;
            }

            if (tx >= col || ty >= row)
            {
                return "";
            }

            var xnum = array[ty, tx];
            return xnum.ToString();
        }
        catch (Exception)
        {
            return "";
        }
    }

    private static string GetCheckedStudentNoFor8K(double y, double x)
    {
        try
        {
            const int row = 3;
            const int col = 20;
            var array = new int[row, col];
            var num = 1;
            for (var i = 0; i < row; i++)
            {
                for (var j = 0; j < col; j++)
                {
                    array[i, j] = num;
                    num++;
                }
            }

            var tx = Convert.ToInt32(Math.Floor(Convert.ToDecimal(Convert.ToInt32(x - 24) / Convert.ToInt32(73))));
            var ty = Convert.ToInt32(Math.Floor(Convert.ToDecimal(Convert.ToInt32(y - 20) / Convert.ToInt32(70))));
            if (tx <= 0)
            {
                tx = 0;
            }

            if (ty <= 0)
            {
                ty = 0;
            }

            if (tx >= col || ty >= row)
            {
                return "";
            }

            var xnum = array[ty, tx];
            return xnum.ToString();
        }
        catch (Exception)
        {
            return "";
        }
    }

    /// <summary>
    /// 获取页码
    /// </summary>
    /// <param name="x"></param>
    /// <param name="y"></param>
    /// <returns></returns>
    private static int GetPageNo(double x, double y)
    {
        return x switch
        {
            >= 1218 and <= 1275 when y is >= 227 and <= 299 => 0B0001,
            >= 1283 and <= 1339 when y is >= 227 and <= 299 => 0B0011,
            >= 1346 and <= 1402 when y is >= 227 and <= 299 => 0B0101,
            >= 1409 and <= 1464 when y is >= 227 and <= 299 => 0B0111,
            _ => 0
        };
    }

    private static int GetPageNo8K(double x, double y)
    {
        return x switch
        {
            >= 1126 and <= 1177 when y is >= 205 and <= 257 => 1,
            >= 1185 and <= 1236 when y is >= 205 and <= 257 => 5,
            >= 1244 and <= 1295 when y is >= 205 and <= 257 => 9,
            >= 1303 and <= 1354 when y is >= 205 and <= 257 => 13,
            _ => 0
        };
    }

    /// <summary>
    /// 获取选中的学号
    /// </summary>
    /// <param name="x">X</param>
    /// <param name="y">Y</param>
    /// <remarks>练习册标签学号纸专用</remarks>
    /// <returns></returns>
    private static string GetTagNo(float x, float y)
    {
        try
        {
            var matrix = PaperTagStudentNoMatrices.Locate(x, y, 10);
            return matrix == null ? "" : matrix.Option;
        }
        catch (Exception)
        {
            return "";
        }
    }

    /// <summary>
    /// 获取是否在卷码页位置写
    /// </summary>
    /// <param name="x">X</param>
    /// <param name="y">Y</param>
    /// <returns></returns>
    private static bool GetPageRight(double x, double y)
    {
        return x is >= 850 and <= 1111 && y is >= 227 and <= 285;
    }

    private static bool GetPageRight8K(double x, double y)
    {
        return x is >= 787 and <= 1012 && y is >= 204 and <= 257;
    }

    /// <summary>
    /// 分数框
    /// </summary>
    /// <param name="x">X</param>
    /// <param name="y">Y</param>
    /// <returns></returns>
    private static bool GetScorePosition(double x, double y)
    {
        return x is >= 1471 and <= 1551 && y is >= 150 and <= 285;
    }

    #endregion

    #region 用户信息

    /// <summary>
    /// 获取学生信息
    /// </summary>
    /// <param name="studentid"></param>
    /// <returns></returns>
    private async Task<StudentInfo> GetStudent(string studentid)
    {
        return await _userInfoRedisService.GetStudentInfoAsync(studentid);
    }

    /// <summary>
    /// 获取学生信息
    /// </summary>
    /// <param name="classid">班级id</param>
    /// <param name="studentno">学号</param>
    /// <returns></returns>
    private async Task<StudentInfo> GetStudent(string classid, string studentno)
    {
        return await _userInfoRedisService.GetStudentInfoAsync(classid, studentno);
    }

    /// <summary>
    /// 获取教师信息
    /// </summary>
    /// <param name="userid">教师用户id</param>
    /// <returns></returns>
    private async Task<TeacherInfo> GetTeacher(string userid)
    {
        var teacher = await _teacher_info_service.GetTeacherInfoAsync(userid);
        if (teacher != null)
        {
            return teacher;
        }

        const string sql = """
                           SELECT TOP (1)
                                  [Id] AS [UserId],
                                  [RealName],
                                  [UserName],
                                  [NickName],
                                  [SchoolId],
                                  [PhoneNum] AS [Mobile],
                                  [AreaId] AS [Area]
                           FROM [dbo].[Base_User]
                           WHERE [Id] = @userid
                                 AND [Deleted] = 0;
                           """;
        teacher = await _dapper_service.QueryFirstAsync<TeacherInfo>
        (
            sql, new
            {
                userid
            }
        );
        if (teacher == null)
        {
            return null;
        }

        teacher.Mid = _snowflake.NewId();
        await _teacher_info_service.AddAsync(teacher);
        return teacher;
    }

    #endregion

    #region 获取语音试卷坐标信息

    /// <summary>
    /// 获取语音试卷坐标信息
    /// </summary>
    /// <param name="x"></param>
    /// <param name="y"></param>
    /// <param name="paperid">试卷id</param>
    /// <returns></returns>
    private async Task<PaperCoordInfo> GetCoordInfo(double x, double y, string paperid, int currentPageNo, int page)
    {
        if (currentPageNo==0)
        {
            currentPageNo=1;
        }

        const string sql = """
                           SELECT ivc.WorkBookPageId,ivc.CoordType,ivc.ItemId,ivc.Range,wp.Page
                           FROM [dbo].[Exam_Item_Voice_Coord] ivc
                           JOIN dbo.WorkbookPage wp ON wp.id=ivc.WorkBookPageId
                           WHERE [Range] IS NOT NULL
                                 AND LEN([Range]) > 0
                              	  AND ivc.[PaperId] = @paperid;
                           """;
        var list = await _dapper_service.QueryListAsync<PaperCoordInfo>
        (
            sql, new
            {
                paperid
            }
        );
        if (list == null || !list.Any())
        {
            return null;
        }

        var workBookPageList = list.Select(z => new { z.WorkBookPageId, z.Page }).Distinct().Select(z => new WorkBookPageDto { WorkBookPageId=z.WorkBookPageId, Page=z.Page }).ToList();

        foreach (var item in workBookPageList)
        {
            item.Index= item.Page;
            item.PageId=5;

            if (item.Page % 2 == 0)
            {
                item.PageId=6;
            }
        }

        foreach (var item in list)
        {
            var findPage = workBookPageList.First(z => z.WorkBookPageId==item.WorkBookPageId);
            item.Index=findPage.Index;
            item.PageId=findPage.PageId;
        }

        var xlist = list
            .Where(z => z.Index>=currentPageNo&&z.PageId==page)
            .ToList();

        return (from item in xlist
                let ranges = item.Range.ToEntity<List<XPoint>>()
                where ranges is
                {
                    Count: 4
                }
                let pa = ranges[0]
                let pc = ranges[2]
                let isin = IsInMatrix(pa, pc, new XPoint(Convert.ToInt32(x), Convert.ToInt32(y)))
                where isin
                select item).FirstOrDefault();
    }

    #endregion

    #region 获取题目媒体信息

    /// <summary>
    /// 获取题目媒体信息
    /// </summary>
    /// <param name="paperid">试卷id</param>
    /// <param name="itemid">题目id</param>
    /// <param name="filetype">
    /// 1: 题干<br/>
    /// 2: 学生作答录音<br/>
    /// 3: 教师点评录音<br/>
    /// </param>
    /// <param name="roletype">视角: 0.学生 1.教师</param>
    /// <param name="teacherid">教师id</param>
    /// <param name="studentid">学生id</param>
    private async Task<PaperMediaInfo> GetMediaInfo(string paperid, string itemid, int filetype, int roletype, string teacherid = "", string studentid = "")
    {
        var sb = new StringBuilder
        (
            """
            SELECT TOP (1)
                   *
            FROM [dbo].[Exam_Item_Media]
            WHERE [PaperId] = @paperid
                  AND [ItemId] = @itemid
                  AND [IsDeleted] = 0
                  AND [FileType] = @filetype
            """
        );
        if (roletype == 0)
        {
            switch (filetype)
            {
                case 1:
                    sb.Append(" ORDER BY [AddTime] DESC;");
                    break;
                case 2:
                    sb.Append(" AND [StudentId] = @studentid ORDER BY [AddTime] DESC;");
                    break;
                case 3:
                    sb.Append(" AND [StudentId] = @studentid AND [TeacherId] = @teacherid ORDER BY [AddTime] DESC;");
                    break;
            }
        }
        else
        {
            switch (filetype)
            {
                case 1:
                    sb.Append(" ORDER BY [AddTime] DESC;");
                    break;
                case 2 or 3:
                    sb.Append(" AND [StudentId] = @studentid AND [TeacherId] = @teacherid ORDER BY [AddTime] DESC;");
                    break;
            }
        }

        var result = await _dapper_service.QueryFirstAsync<PaperMediaInfo>
        (
            sb.ToString(), new
            {
                paperid,
                itemid,
                filetype,
                teacherid,
                studentid
            }
        );
        return result;
    }

    #endregion

    #endregion

    #endregion

    #region Implementation of ProcessOfflineAsync

    /// <inheritdoc />
    public async Task ProcessOfflineAsync(OfflineCorrectPenDotData data)
    {
        var userid = await GetUserIdAsync(mac: data.Mac);

        var raw_data = data.ToJsonString();
        //点阵笔还未绑定用户
        if (string.IsNullOrWhiteSpace(userid))
        {
            _log.LogInformation("mac:{} 点阵笔未绑定-离线数据", data.Mac);
            return;
        }

        //当前批阅班级
        var correct_class_id = await _teacher_correct_redis_service.HGetCorrectClassIdAsync(userid);
        if (string.IsNullOrWhiteSpace(correct_class_id))
        {
            _log.LogInformation("mac:{} 未查询到当前正在批阅的班级", data.Mac);
            return;
        }

        var dots = data.StudentNoDots.Where(x => x.Value is { Count: > 0 }).ToArray();
        if (dots.Length == 0)
        {
            _log.LogInformation("mac:{},没有找到勾选学号笔迹", data.Mac);
            return;
        }

        foreach (var item in dots)
        {
            var ispass = data.CorrectDots.TryGetValue(item.Key, out var correct_dots);
            if (!ispass || correct_dots.Count == 0)
            {
                continue;
            }

            var avg_x = Convert.ToSingle(item.Value.OrderBy(x => x.Oid).Average(x => x.X));
            var avg_y = Convert.ToSingle(item.Value.OrderBy(x => x.Oid).Average(x => x.Y));
            var studentno = GetTagNo(avg_x, avg_y);
            if (string.IsNullOrWhiteSpace(studentno))
            {
                _log.LogInformation("mac:{} 未获取到学生信息: {index} , {classid}", data.Mac, item.Key.ToString(), correct_class_id);
                continue;
            }

            var student = await GetStudent(correct_class_id, studentno);
            if (student == null)
            {
                _log.LogInformation("mac:{} 学生信息不存在: {studentno} , {classid}", data.Mac, studentno, correct_class_id);
                continue;
            }

            var pages = correct_dots.Select(x => x.Page).Distinct().ToList();
            if (pages.Count <= 0)
            {
                continue;
            }

            foreach (var page in pages)
            {
                var zdots = correct_dots.Where(x => x.Page == page).ToList();
                if (zdots.Count <= 0)
                {
                    continue;
                }

                //查询是否为组合页面
                var is_combination = await _paper_info_redis_service.HGetPaperInfoIsCombinationAsync(page);
                string paperid;
                if (!is_combination)
                {
                    //获取普通的试卷信息
                    paperid = await _paper_info_redis_service.HGetPaperIdAsync(page);
                    if (string.IsNullOrWhiteSpace(paperid))
                    {
                        //缓存中没有试卷信息, 从数据库读取
                        const string workpage_sql = """
                                                    SELECT TOP (1) PaperId
                                                    FROM WorkbookPage
                                                    WHERE PaperType = 2
                                                          AND PageId = @pageid;
                                                    """;
                        var workpage = await _dapper_service.QueryFirstAsync<WorkbookPage>
                        (
                            workpage_sql, new
                            {
                                pageid = page
                            }
                        );
                        if (workpage != null)
                        {
                            paperid = workpage.PaperId;

                            //缓存试卷信息
                            await _paper_info_redis_service.HSetPaperIdAsync(page, paperid);
                        }
                    }

                    if (string.IsNullOrWhiteSpace(paperid))
                    {
                        continue;
                    }
                }
                else
                {
                    var combination_result = await GetCombinationPaperId(zdots, page);
                    if (string.IsNullOrWhiteSpace(combination_result.Item1) || combination_result.Item2 <= 0)
                    {
                        _log.LogInformation("mac:{} 未查询到组合页试卷信息", data.Mac);
                        return;
                    }

                    paperid = combination_result.Item1;
                }

                if (string.IsNullOrWhiteSpace(paperid))
                {
                    _log.LogInformation("mac:{} 未查询到组合页试卷", data.Mac);
                    continue;
                }

                var dot_list = _mapper.Map<List<DotBase>>(zdots);
                var teacher_penlog = new TeacherPenLog
                {
                    Mid = _snowflake.NewId(),
                    UserId = student.UserId,
                    TeacherId = userid,
                    Mac = data.Mac,
                    PageId = page,
                    Dots = dot_list
                };
                await _teacher_pen_log_service.AddAsync(teacher_penlog, student.SchoolId).ConfigureAwait(false);
                await _teacher_correct_redis_service.HSetCorrectStudentStatusAsync(paperid, student.UserId, 3);
                _log.LogInformation("离线批改笔迹入库: mac:{} requestid: {requestid}, teacherid: {teacherid} , studentno: {studentno} , studentid: {studentid}, paperid: {paperid}, pageid: {pageid}", data.Mac, data.RequestId, userid, studentno, student.UserId, paperid, page.ToString());

                var pen_times = dot_list.Select(x => x.AddTime).Distinct().ToList();
                var correct_data = new TeacherCorrectData
                {
                    PaperId = paperid,
                    StudentId = student.UserId,
                    PageId = page,
                    StartTime = pen_times.Min(),
                    EndTime = pen_times.Max()
                };

                await _publisher.Publish(correct_data).ConfigureAwait(false);
            }
        }
    }

    #endregion

    #region Implementation of RecordProcessAsync

    /// <inheritdoc />
    public async Task RecordProcessAsync(PenRecordData data)
    {

        const string xsql = """
                            SELECT TOP (1)
                                   *
                            FROM [dbo].[Exam_Item_Media]
                            WHERE [IsDeleted] = 0
                                  AND [PaperId] = @paperid
                                  AND [ItemId] = @itemid
                                  AND [StudentId] = @studentid
                                  AND [TeacherId] = @teacherid
                                  AND [FileType] = @filetype;
                            """;

        var xmedia_info = await _dapper_service.QueryFirstAsync<PaperMediaInfo>
        (
            xsql, new
            {
                paperid = data.PaperId,
                itemid = data.ItemId,
                addtime = DateTime.Now,
                studentid = data.StudentId,
                teacherid = data.TeacherId,
                filetype = data.UserRole == 0 ? 2 : 3
            }
        );

        var examItemMediaEto = new ExamItemMediaEto();

        if (xmedia_info == null)
        {
            const string sql = """
                               INSERT INTO [dbo].[Exam_Item_Media]
                               (
                                   [Id],
                                   [PaperId],
                                   [ItemId],
                                   [Url],
                                   [AddTime],
                                   [UpdateTime],
                                   [StudentId],
                                   [TeacherId],
                                   [FileType],
                                   [IsDeleted]
                               )
                               VALUES
                               (   @id,
                                   @paperid,
                                   @itemid,
                                   @url,
                                   @addtime,
                                   @updatetime,
                                   @studentid,
                                   @teacherid,
                                   @filetype,
                                   0
                                );
                               """;

            examItemMediaEto = new ExamItemMediaEto
            {
                Id=_snowflake.NewId().ToString(),
                PaperId=data.PaperId,
                ItemId=data.ItemId,
                Url=data.FileUrl,
                AddTime=DateTime.Now,
                UpdateTime=DateTime.Now,
                StudentId=data.StudentId,
                TeacherId=data.TeacherId,
                FileType= data.UserRole == 0 ? 2 : 3
            };

            var result = await _dapper_service.ExecuteAsync
            (
                sql, new
                {
                    id = examItemMediaEto.Id,
                    paperid = examItemMediaEto.PaperId,
                    itemid = examItemMediaEto.ItemId,
                    url = examItemMediaEto.Url,
                    addtime = examItemMediaEto.AddTime,
                    updatetime = examItemMediaEto.UpdateTime,
                    studentid = examItemMediaEto.StudentId,
                    teacherid = examItemMediaEto.TeacherId,
                    filetype = examItemMediaEto.FileType
                }
            );

            _log.LogInformation("录音文件入库: {result} , {data}", result.ToString(), data.ToJsonString());
        }
        else
        {
            const string update_sql = """
                                  UPDATE [dbo].[Exam_Item_Media]
                                  SET [Url] = @url,
                                      [UpdateTime] = GETDATE()
                                  WHERE [Id] = @id;
                                  """;
            var xresult = await _dapper_service.ExecuteAsync
            (
                update_sql, new
                {
                    id = xmedia_info.Id,
                    url = data.FileUrl
                }
            );
            _log.LogInformation("更新录音文件: {result} , {data}", xresult.ToString(), xmedia_info.ToJsonString());

            examItemMediaEto.Id=xmedia_info.Id;
            examItemMediaEto.PaperId=xmedia_info.PaperId;
            examItemMediaEto.ItemId=xmedia_info.ItemId;
            examItemMediaEto.Url= data.FileUrl;
            examItemMediaEto.AddTime=xmedia_info.AddTime;
            examItemMediaEto.UpdateTime=xmedia_info.UpdateTime;
            examItemMediaEto.StudentId=xmedia_info.StudentId;
            examItemMediaEto.TeacherId=xmedia_info.TeacherId;
            examItemMediaEto.FileType=xmedia_info.FileType;
        }

        await _publisher.Publish(examItemMediaEto).ConfigureAwait(false);
    }

    #endregion

    #region Mr. Zhu Chunfeng common private methods

    /// <summary>
    /// GET USERID - 通过Mac获取学生ID，支持请假学生的自动切换
    /// </summary>
    /// <param name="mac">点阵笔Mac地址</param>
    /// <param name="data">点阵笔数据，用于获取DataType</param>
    /// <returns>返回可用的学生ID</returns>
    private async Task<string> GetUserIdAsync(string mac, PenDotData data = null)
    {
        // 从缓存中获取绑定的学生ID（可能是多个，用英文逗号分隔）
        var userIds = await _pen_mapping_redis_service.HGetMappingUserAsync(mac);

        if (string.IsNullOrWhiteSpace(userIds))
        {
            return null;
        }

        // 如果只有一个学生ID，直接返回
        var studentIds = userIds.Split(',', StringSplitOptions.RemoveEmptyEntries);
        if (studentIds.Length == 1)
        {
            return studentIds[0].Trim();
        }

        // 如果有多个学生ID，需要检查请假情况
        if (studentIds.Length > 1)
        {
            return await GetAvailableStudentIdAsync(studentIds, data);
        }

        return studentIds[0].Trim();
    }

    /// <summary>
    /// 从多个学生ID中选择一个未请假的学生ID
    /// </summary>
    /// <param name="studentIds">学生ID数组</param>
    /// <param name="data">点阵笔数据，用于获取DataType</param>
    /// <returns>返回未请假的学生ID</returns>
    private async Task<string> GetAvailableStudentIdAsync(string[] studentIds, PenDotData data)
    {
        foreach (var studentId in studentIds)
        {
            var trimmedStudentId = studentId.Trim();

            // 获取学生信息
            var student = await GetStudent(trimmedStudentId);
            if (student == null)
            {
                _log.LogWarning("未找到学生信息: {studentId}", trimmedStudentId);
                continue;
            }

            // 检查学生是否请假
            var isOnLeave = await IsStudentOnLeaveAsync(trimmedStudentId, student.ClassId, data);
            if (!isOnLeave)
            {
                return trimmedStudentId;
            }
        }

        // 如果所有学生都请假，返回第一个学生ID
        _log.LogWarning("所有绑定的学生都在请假中，返回第一个学生ID: {studentIds}", string.Join(",", studentIds));
        return studentIds[0].Trim();
    }

    /// <summary>
    /// 检查学生是否在当前科目下请假
    /// </summary>
    /// <param name="studentId">学生ID</param>
    /// <param name="classId">班级ID</param>
    /// <param name="data">点阵笔数据，用于获取DataType</param>
    /// <returns>true表示请假中，false表示未请假</returns>
    private async Task<bool> IsStudentOnLeaveAsync(string studentId, string classId, PenDotData data)
    {
        try
        {
            // 获取当前赋码的试卷ID
            var currentPaperId = data?.DataType == 1
                ? await _paper_log_redis_service.GetCurrentPaperIdAsync(classId)
                : await _paper_log_redis_service.GetCurrentOfflinePaperIdAsync(classId);

            if (currentPaperId == null)
            {
                _log.LogInformation("未找到当前赋码试卷，跳过请假检查: studentId={studentId}, classId={classId}", studentId, classId);
                return false;
            }

            // 获取试卷信息以获取科目ID
            var paper = await GetPaperAsync(currentPaperId.PaperId);
            if (paper == null || string.IsNullOrWhiteSpace(paper.SubjectId))
            {
                _log.LogInformation("未找到试卷科目信息，跳过请假检查: paperId={paperId}", currentPaperId.PaperId);
                return false;
            }

            // 查询学生在当前科目下是否请假
            const string sql = """
                SELECT COUNT(1)
                FROM [dbo].[Exam_StudentLeave]
                WHERE [StudentId] = @studentId
                      AND [ClassId] = @classId
                      AND [SubjectId] = @subjectId
                      AND [Status] = 1
                      AND ([EndTime] IS NULL OR GETDATE() BETWEEN [StartTime] AND [EndTime])
                      AND [Deleted] = 0;
                """;

            var result = await _dapper_service.ExecuteScalarAsync(sql, new
            {
                studentId,
                classId,
                subjectId = paper.SubjectId
            });

            var leaveCount = Convert.ToInt32(result);

            var isOnLeave = leaveCount > 0;

            if (isOnLeave)
            {
                _log.LogInformation("学生请假中: studentId={studentId}, classId={classId}, subjectId={subjectId}",
                    studentId, classId, paper.SubjectId);
            }

            return isOnLeave;
        }
        catch (Exception ex)
        {
            _log.LogError(ex, "检查学生请假状态时发生异常: studentId={studentId}, classId={classId}", studentId, classId);
            return false; // 异常情况下认为未请假
        }
    }

    /// <summary>
    /// GET ExamItem
    /// </summary>
    /// <param name="paperId"></param>
    /// <param name="itemNo"></param>
    /// <returns></returns>
    private async Task<ExamItemDto> GetExamItemAsync(string paperId, int itemNo, string classId)
    {
        ExamItemDto data = null;

        var paper = await GetPaperAsync(paperId);

        if (paper.HomeworkType.HasValue&&paper.HomeworkType==9)
        {
            if (string.IsNullOrWhiteSpace(classId))
            {
                return data;
            }

            var current_paper_item = await _paper_log_redis_service.GetCurrentPaperItemIdAsync(classId, paperId);
            if (current_paper_item==null||string.IsNullOrWhiteSpace(current_paper_item.ItemId))
            {
                return data;
            }

            data=await GetExamItemAsync(current_paper_item.ItemId);
        }
        else
        {
            var key = $"{paperId}|{itemNo}|itemInfo";

            if (await _paper_info_redis_service.ExistsAsync(key))
            {
                data = _paper_info_redis_service.Get<ExamItemDto>(key);
            }
            else if (data == null)
            {
                var sql = @"
SELECT TOP 1
       i.Id,
       i.TypeId
FROM Exam_Item i
    JOIN Exam_PaperItemMapping pim
        ON i.Id = pim.ItemId
WHERE pim.PaperId = @paperId
      AND pim.OrderId = @itemNo;
";
                data= await _dapper_service.QueryFirstAsync<ExamItemDto>(sql, new
                {
                    paperId,
                    itemNo
                });

                if (data != null)
                {
                    _paper_info_redis_service.Set(key, data, new TimeSpan(1, 0, 0));
                }
                else
                {
                    _paper_info_redis_service.Set(key, "", new TimeSpan(0, 0, 60));
                }
            }
        }

        return data;
    }

    private async Task<ExamPaperDto> GetPaperAsync(string paperId)
    {
        if (string.IsNullOrWhiteSpace(paperId))
            return null;

        var key = $"PenServerPaper_{paperId}";
        var paper = await _commonRedisService.GetAsync<ExamPaperDto>(key);

        if (paper==null)
        {
            var sql = @"SELECT Id,HomeworkType,SubjectId FROM dbo.Exam_Paper where Id=@paperId";
            paper = await _dapper_service.QueryFirstAsync<ExamPaperDto>(sql, new
            {
                paperId
            });

            if (paper!=null)
            {
                await _commonRedisService.SetAsync(key, paper, new TimeSpan(0, 60, 0));
            }
        }

        return paper;
    }

    private async Task<ExamItemDto> GetExamItemAsync(string itemId)
    {
        var key = $"PenServerItemId_{itemId}";
        var data = await _commonRedisService.GetAsync<ExamItemDto>(key);

        if (data==null)
        {
            var sql = @"
SELECT Id,TypeId FROM dbo.Exam_Item WHERE Id=@ItemId
";
            data=await _dapper_service.QueryFirstAsync<ExamItemDto>(sql, new
            {
                itemId
            });

            if (data!=null)
            {
                await _commonRedisService.SetAsync(key, data, new TimeSpan(0, 60, 0));
            }
        }

        return data;
    }

    private async Task<bool> IsA4(CurrentPaperId input)
    {
        if (input==null)
            return false;

        var paper = await GetPaperAsync(input.PaperId);

        if (paper.HomeworkType!=null&&paper.HomeworkType.HasValue)
        {
            if (paper.HomeworkType.Value==1)
                return true;
        }

        return false;
    }

    /// <summary>
    /// 根据试卷Id,获取试卷是否有题目颜色设置
    /// </summary>
    /// <param name="paperId"></param>
    /// <returns></returns>
    private async Task<PaperItemColorWrap> GetPaperItemColorWrapAsync(string paperId)
    {
        var key = $"PenGetPaperItemColorWrap_{paperId}";
        var data = await _commonRedisService.GetAsync<PaperItemColorWrap>(key);

        if (data==null)
        {
            data=new PaperItemColorWrap();

            var sql = @"
SELECT * FROM PaperItemColorMapping WHERE PaperId=@paperId
";
            var paperItemColorMappings = (await _dapper_service.QueryListAsync<PaperItemColorMapping>(sql, new
            {
                paperId
            })).ToList();

            sql = @"
SELECT * FROM PaperItemDrawingBoardRange WHERE PaperId=@paperId
";
            var paperItemDrawingBoardRanges = (await _dapper_service.QueryListAsync<PaperItemDrawingBoardRange>(sql, new
            {
                paperId
            })).ToList();

            data.ItemColorRanges.AddRange(paperItemColorMappings.Select(z => new ItemColorRangeDto
            {
                ItemId=z.ItemId,
                Color=z.Color,
                Range=z.Range,
                IsColor=true
            }).ToList());

            data.ItemColorRanges.AddRange(paperItemDrawingBoardRanges.Select(z => new ItemColorRangeDto
            {
                ItemId=z.ItemId,
                Range=z.Range
            }).ToList());

            if (data.ItemColorRanges.Count>0)
            {
                data.IsExist=true;
            }

            await _commonRedisService.SetAsync(key, data, new TimeSpan(0, 5, 0));
        }

        return data;
    }

    /// <summary>
    /// 获取颜色图标（包括勾选还原图标）
    /// </summary>
    /// <param name="itemColorRanges"></param>
    /// <param name="x"></param>
    /// <param name="y"></param>
    /// <returns></returns>
    private static ItemColorRangeDto GetItemColorRange(List<ItemColorRangeDto> itemColorRanges, double x, double y)
    {
        return (from item in itemColorRanges
                let ranges = item.Range.ToEntity<List<XPoint>>()
                where ranges is
                {
                    Count: 4
                }
                let pa = ranges[0]
                let pc = ranges[2]
                let isin = IsInMatrix(pa, pc, new XPoint(Convert.ToInt32(x), Convert.ToInt32(y)))
                where isin
                select item).FirstOrDefault();
    }

    /// <summary>
    /// 获取用户对应试卷的笔迹颜色
    /// </summary>
    /// <param name="userId"></param>
    /// <param name="subjectId"></param>
    /// <param name="paperId"></param>
    /// <returns></returns>
    private async Task<string> GetPaperHandwritingColorCacheAsync(string userId, string subjectId, string paperId)
    {
        string color = null;
        var key = $"PenGetPaperHandwritingColor_{paperId}_{userId}_{subjectId}";
        var data = await _commonRedisService.GetAsync<PaperHandwritingColor>(key);

        if (data!=null)
        {
            color=data.Color;
        }

        return color;
    }

    /// <summary>
    /// 设置笔迹颜色
    /// </summary>
    /// <param name="userId"></param>
    /// <param name="subjectId"></param>
    /// <param name="paperId"></param>
    /// <param name="color"></param>
    /// <returns></returns>
    private async Task SetPaperHandwritingColorAsync(string userId, string subjectId, string paperId, string color)
    {
        var key = $"PenGetPaperHandwritingColor_{paperId}_{userId}_{subjectId}";

        await _commonRedisService.SetAsync(key, new PaperHandwritingColor { Color=color });
    }

    /// <summary>
    /// 恢复默认笔迹
    /// </summary>
    /// <param name="userId"></param>
    /// <param name="subjectId"></param>
    /// <param name="paperId"></param>
    /// <returns></returns>
    private async Task ReStoreSetPaperHandwritingColorAsync(string userId, string subjectId, string paperId)
    {
        var key = $"PenGetPaperHandwritingColor_{paperId}_{userId}_{subjectId}";

        await _commonRedisService.RemoveAsync(key);
    }

    /// <summary>
    /// 创建Mac与试卷页面的映射关系（防并发）
    /// </summary>
    /// <param name="paperId">试卷ID</param>
    /// <param name="paperCode">页码</param>
    /// <param name="mac">Mac地址</param>
    /// <returns>映射关系对象</returns>
    private async Task<MD_PaperMacMapping> CreatePaperMacMappingAsync(string paperId, int paperCode, string mac, string userId)
    {
        try
        {
            // 首先查询该学生在当前试卷的最新作答记录
            const string latestMappingSql = """
                SELECT TOP (1) *
                FROM [dbo].[MD_PaperMacMapping] WITH (NOLOCK)
                WHERE [PMac] = @mac
                      AND [PaperId] = @paperId
                      AND [Deleted] = 0
                ORDER BY [CreateTime] DESC;
                """;

            var latestMapping = await _dapper_service.QueryFirstAsync<MD_PaperMacMapping>(
                latestMappingSql, new { mac, paperId });

            int targetPaperPageIndex;

            if (latestMapping == null)
            {
                // 没有历史记录，说明是第一次作答，映射到试卷第1页
                targetPaperPageIndex = 1;
                _log.LogInformation("学生第一次作答: Mac={}, PaperId={}, 映射到试卷第1页", mac, paperId);
            }
            else if (latestMapping.PaperCode == paperCode)
            {
                // 还在同一物理页面书写，使用相同的试卷页码
                targetPaperPageIndex = latestMapping.PaperPageIndex;
                _log.LogInformation("继续在同一物理页面书写: Mac={}, PaperCode={}, 试卷第{}页",
                    mac, paperCode, targetPaperPageIndex);
            }
            else
            {
                // 翻页了，试卷页码+1
                targetPaperPageIndex = latestMapping.PaperPageIndex + 1;
                _log.LogInformation("学生翻页作答: Mac={}, 从PaperCode={}翻到PaperCode={}, 从试卷第{}页到第{}页",
                    mac, latestMapping.PaperCode, paperCode, latestMapping.PaperPageIndex, targetPaperPageIndex);
            }

            // 验证目标页码是否存在于试卷中
            const string workbookPageSql = """
                SELECT TOP (1) *
                FROM [dbo].[WorkbookPage]
                WHERE [PaperId] = @paperId
                      AND [Page] = @page;
                """;

            var targetPage = await _dapper_service.QueryFirstAsync<WorkbookPage>(
                workbookPageSql, new { paperId, page = targetPaperPageIndex });

            if (targetPage == null)
            {
                _log.LogError("目标试卷页面不存在: PaperId={}, Page={}", paperId, targetPaperPageIndex);
                return null;
            }

            // 使用数据库唯一约束防止并发插入重复数据
            var mappingId = _snowflake.NewStringId();
            var mapping = new MD_PaperMacMapping
            {
                Id = mappingId,
                PaperId = paperId,
                PaperCode = paperCode,
                PMac = mac,
                UserId = userId,
                PaperPageIndex = targetPaperPageIndex,
                CreateTime = DateTime.Now,
                Deleted = false
            };

            const string insertSql = """
                INSERT INTO [dbo].[MD_PaperMacMapping]
                ([Id], [PaperId], [PaperCode], [PMac], [PaperPageIndex], [CreateTime], [Deleted],[UserId])
                VALUES
                (@Id, @PaperId, @PaperCode, @PMac, @PaperPageIndex, @CreateTime, @Deleted,@UserId);
                """;

            var result = await _dapper_service.ExecuteAsync(insertSql, mapping);
            if (result > 0)
            {
                _log.LogInformation("成功创建Mac与页面映射关系: Mac={}, PaperId={}, PaperCode={}, PaperPageIndex={}",
                    mac, paperId, paperCode, targetPaperPageIndex);
                return mapping;
            }
            else
            {
                // 插入失败，可能是并发导致的重复，重新查询
                const string requerySql = """
                    SELECT TOP (1) *
                    FROM [dbo].[MD_PaperMacMapping] WITH (NOLOCK)
                    WHERE [PaperId] = @paperId
                          AND [PaperCode] = @paperCode
                          AND [PMac] = @mac
                          AND [Deleted] = 0;
                    """;

                var existingMapping = await _dapper_service.QueryFirstAsync<MD_PaperMacMapping>(
                    requerySql, new { paperId, paperCode, mac });

                if (existingMapping != null)
                {
                    _log.LogInformation("发现已存在的Mac与页面映射关系: Mac={}, PaperId={}, PaperCode={}",
                        mac, paperId, paperCode);
                    return existingMapping;
                }
            }

            return null;
        }
        catch (Exception ex)
        {
            _log.LogError(ex, "创建Mac与页面映射关系时发生异常: Mac={}, PaperId={}, PaperCode={}",
                mac, paperId, paperCode);
            return null;
        }
    }

    #endregion
}