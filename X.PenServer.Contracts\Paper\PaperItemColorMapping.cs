﻿namespace X.PenServer.Contracts.Paper
{
    /// <summary>
    /// 试卷题目颜色
    /// </summary>
    public class PaperItemColorMapping
    {
        /// <summary>
        /// Id
        /// </summary>
        public string Id { get; set; }

        public string PaperId { get; set; }

        public string ItemId { get; set; }

        public string Color { get; set; }

        public string ImgUrl { get; set; }

        /// <summary>
        /// 坐标
        /// </summary>
        public string Range { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public int Sort { get; set; }

        public DateTime UpdateTime { get; set; }
    }

    /// <summary>
    /// 题目对应
    /// </summary>
    public class PaperItemDrawingBoardRange
    {
        /// <summary>
        /// Id
        /// </summary>
        public string Id { get; set; }

        public string PaperId { get; set; }

        public string ItemId { get; set; }

        /// <summary>
        /// 坐标
        /// </summary>
        public string Range { get; set; }

        public DateTime UpdateTime { get; set; }
    }

    public class ItemColorRangeDto
    {
        public string ItemId { get; set; }

        public string Color { get; set; }

        public string Range { get; set; }

        public bool IsColor { get; set; }
    }

    public class PaperItemColorWrap
    {
        /// <summary>
        /// 是否存在
        /// </summary>
        public bool IsExist { get; set; }

        public List<ItemColorRangeDto> ItemColorRanges { get; set; } = new List<ItemColorRangeDto>();

    }
}
