﻿// -- Function: HostService.cs
// --- Project: <PERSON>.PenServer.Jobs
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2024/04/04 16:04:05

using Microsoft.Extensions.Hosting;
using Volo.Abp;

namespace X.PenServer.Jobs.Implement;

/// <summary>
/// Host服务
/// </summary>
public class HostService : IHostedService
{
    private readonly IAbpApplicationWithExternalServiceProvider _abp;
    private readonly IServiceProvider _provider;

    public HostService(IAbpApplicationWithExternalServiceProvider abp, IServiceProvider provider)
    {
        _abp = abp;
        _provider = provider;
    }

    #region Implementation of IHostedService

    /// <inheritdoc />
    public async Task StartAsync(CancellationToken token)
    {
        await _abp.InitializeAsync(_provider);
    }

    /// <inheritdoc />
    public async Task StopAsync(CancellationToken token)
    {
        await _abp.ShutdownAsync();
    }

    #endregion
}