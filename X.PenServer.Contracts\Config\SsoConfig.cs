﻿// -- Function: SsoConfig.cs
// --- Project: X.PenServer.Contracts
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2023/12/01 15:37

using System.Text.Json.Serialization;

namespace X.PenServer.Contracts.Config;

/// <inheritdoc />
public class SsoConfig : ISsoConfig
{
	/// <inheritdoc />
	[JsonInclude]
	[JsonPropertyName(nameof(Issuer))]
	public string Issuer { get; set; }

	/// <inheritdoc />
	[JsonInclude]
	[JsonPropertyName(nameof(Audience))]
	public string Audience { get; set; }

	/// <inheritdoc />
	[JsonInclude]
	[Json<PERSON>ropertyName(nameof(SigningKey))]
	public string SigningKey { get; set; }
}