﻿//   -- Function: UserInfo.cs
//   --- Project: Uwoo.PenSocket
//   ---- Remark: 
//   ---- Author: Lucifer
//   ------ Date: 2023/09/19 17:46

using System.Text.Json.Serialization;

namespace X.PenServer.Contracts.Realtime;

public class UserInfo
{
	/// <summary>
	///用户id
	/// </summary>
	[JsonPropertyName(nameof(UserId))]
	[JsonInclude]
	public string UserId { get; set; }

	/// <summary>
	/// 连接状态
	/// </summary>
	/// <remarks>0:断开 1:连接</remarks>
	[JsonPropertyName("state")]
	[JsonInclude]
	public int State { get; set; }

	/// <summary>
	/// 电量
	/// </summary>
	/// <remarks>范围: 0 ~ 10</remarks>
	[JsonPropertyName(nameof(Battery))]
	[JsonInclude]
	public int Battery { get; set; }

	/// <summary>
	/// 用户类型
	/// </summary>
	/// <remarks>0:学生 1:教师</remarks>
	[JsonPropertyName("userType")]
	[JsonInclude]
	public int UserType { get; set; }

	/// <summary>
	/// wifi热点
	/// </summary>
	[JsonPropertyName(nameof(Ssid))]
	[JsonInclude]
	public string Ssid { get; set; }

	/// <summary>
	/// 点阵笔编号
	/// </summary>
	[JsonPropertyName(nameof(PMac))]
	[JsonInclude]
	public string PMac { get; set; }
}