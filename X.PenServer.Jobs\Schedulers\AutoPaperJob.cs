﻿// -- Function: AutoPaperJob.cs
//  --- Project: X.PenServer.Jobs
//  ---- Remark: 
//  ---- Author: Lucifer
//  ------ Date: 2024/04/23 09:04

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Quartz;
using X.PenServer.Contracts.Paper;
using X.PenServer.Interfaces;
using X.PenServer.Interfaces.Redis;
using X.PenServer.Jobs.Implement;

namespace X.PenServer.Jobs.Schedulers;

public class AutoPaperJob : JobBase
{
    private readonly ILogger<AutoPaperJob> _log;
    private readonly IDapperService _dapper;
    private readonly IPaperInfoRedisService _paper_info_redis_service;
    private readonly ParallelOptions _parallel_options;

    /// <inheritdoc />
    public AutoPaperJob(ILogger<AutoPaperJob> log, IDapperService dapper, IPaperInfoRedisService paper_info_redis_service, ParallelOptions parallel_options , IConfiguration config)
    {
        _log = log;
        _dapper = dapper;
        _paper_info_redis_service = paper_info_redis_service;
        _parallel_options = parallel_options;

        var type = GetType();
        var time = DateTime.Today.AddHours(1.5);
        var is_immediate = config.GetValue<bool>("Immediate");
        if (is_immediate)
        {
            time = DateTime.Now;
        }
        JobDetail = JobBuilder.Create(type).WithIdentity(type.Name).Build();
        Trigger = TriggerBuilder.Create()
            .WithIdentity(type.Name)
            .StartAt(new DateTimeOffset(time))
            .WithSimpleSchedule
            (
                x =>
                {
                    x.WithInterval(TimeSpan.FromDays(1)).RepeatForever();
                }
            )
            .Build();
        ScheduleJob = async scheduler =>
        {
            var is_exist = await scheduler.CheckExists(JobDetail.Key);
            if (!is_exist)
            {
                await scheduler.ScheduleJob(JobDetail, Trigger).ConfigureAwait(false);
            }
        };
    }

    #region Overrides of QuartzBackgroundWorkerBase

    /// <inheritdoc />
    public override async Task Execute(IJobExecutionContext context)
    {
        try
        {
            var token = context.CancellationToken;
            const string sql = """
                               SELECT *
                               FROM [dbo].[WorkbookPage]
                               WHERE [IsCombination] = 0
                                     AND [PageId] > 1000;
                               """;
            var list = await _dapper.QueryListAsync<WorkbookPage>(sql);
            if (list == null || !list.Any())
            {
                return;
            }

            _parallel_options.CancellationToken = token;
            await Parallel.ForEachAsync(list, _parallel_options, async (x, _) =>
            {
                await _paper_info_redis_service.HSetPaperIdAsync(x.PageId, x.PaperId).ConfigureAwait(false);
                _log.LogInformation("[update page]: {paperid} , {pageid}", x.PageId.ToString(), x.PaperId);
            });

            var paper_list = list.GroupBy(x => x.PaperId).AsEnumerable();
            await Parallel.ForEachAsync(paper_list, _parallel_options, async (item, _) =>
            {
                await Parallel.ForEachAsync(item, _parallel_options, async (xitem, _) =>
                {
                    await _paper_info_redis_service.HSetPaperPageInfoAsync(item.Key, xitem.Page, xitem).ConfigureAwait(false);
                    _log.LogInformation("[update paper]: {paperid} , {pageid}", item.Key, xitem.Page.ToString());
                });
            });
            _log.LogInformation("update paper cache finished...");
        }
        catch (Exception e)
        {
            _log.LogCritical("error: {error}", e.StackTrace ?? e.Message);
        }
    }

    #endregion
}