﻿// -- Function: MyScriptRequestModel.cs
// --- Project: X.PenServer.Contracts
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2023/11/01 15:03


using System.Text.Json.Serialization;

namespace X.PenServer.Contracts.MyScipt;

public class MyScriptRequestModel
{
    /// <summary>
    /// The configuration for the recognition. See explanation for parameters in https://developer.myscript.com/docs/interactive-ink/latest/reference/web/configuration-rest/
    /// </summary>
    [JsonPropertyName("configuration")]
    [JsonInclude]
    public Configuration Configuration { get; set; }

    /// <summary>
    /// x resolution of the writing area in dpi
    /// </summary>
    [JsonPropertyName("xDPI")]
    [JsonInclude]
    public int Xdpi { get; set; }

    /// <summary>
    /// y resolution of the writing area in dpi
    /// </summary>
    [JsonPropertyName("yDPI")]
    [JsonInclude]
    public int Ydpi { get; set; }

    /// <summary>
    /// recognition type
    /// </summary>
    /// <value>
    /// <![CDATA[Text, Math, Diagram, Raw Content, Text Document]]>
    /// </value>
    [JsonPropertyName("contentType")]
    [JsonInclude]
    public string ContentType { get; set; }

    /// <summary>
    ///  A global CSS styling for your content. See https://developer.myscript.com/docs/interactive-ink/latestweb/myscriptjs/styling/
    /// </summary>
    [JsonPropertyName("theme")]
    [JsonInclude]
    public string Theme { get; set; }

    /// <summary>
    /// The write entries that corresponds to the input iink
    /// </summary>
    [JsonPropertyName("strokeGroups")]
    [JsonInclude]
    public List<StrokeGroup> StrokeGroups { get; set; } = new List<StrokeGroup>();

    /// <summary>
    /// height of the writing area
    /// </summary>
    [JsonPropertyName("height")]
    [JsonInclude]
    public int Height { get; set; }

    /// <summary>
    /// width of the writing area
    /// </summary>
    [JsonPropertyName("width")]
    [JsonInclude]
    public int Width { get; set; }

    [JsonPropertyName("conversionState")]
    [JsonInclude]
    public string ConversionState { get; set; }
}
    public class Guides
    {
        [JsonPropertyName("enable")]
        [JsonInclude]
        public bool Enable { get; set; }
    }

    public class SmartGuideFadeOut
    {
        [JsonPropertyName("enable")]
        [JsonInclude]
        public bool Enable { get; set; }

        [JsonPropertyName("duration")]
        [JsonInclude]
        public int Duration { get; set; }
    }

    public class Solver
    {
        /// <summary>
        /// If true, displays solver result upon conversion.
        /// </summary>
        /// <value>true</value>
        [JsonPropertyName("enable")]
        [JsonInclude]
        public bool Enable { get; set; }

        /// <summary>
        /// The number of decimals to display in solver computation result.
        /// </summary>
        /// <value>3</value>
        [JsonPropertyName("fractional-part-digits")]
        [JsonInclude]
        public int FractionalPartDigits { get; set; }

        /// <summary>
        /// The decimal separator symbol (usually . or ,).
        /// </summary>
        /// <value>.</value>
        [JsonPropertyName("decimal-separator")]
        [JsonInclude]
        public string DecimalSeparator { get; set; }

        /// <summary>
        /// Rounding method used display solver results: It must be either half up or truncate.
        /// </summary>
        /// <value>half up</value>
        [JsonPropertyName("rounding-mode")]
        [JsonInclude]
        public string RoundingMode { get; set; }

        /// <summary>
        /// Unit of the angle computation in the solver: It must be either deg (degrees) or rad (radians)
        /// </summary>
        /// <value>deg</value>
        [JsonPropertyName("angle-unit")]
        [JsonInclude]
        public string AngleUnit { get; set; }
    }

    public class Margin
    {
        /// <summary>
        /// Margin from the bottom of the part to the bottom of the text bounding box (used for reflow).
        /// </summary>
        /// <value>10</value>
        [JsonPropertyName("bottom")]
        [JsonInclude]
        public int Bottom { get; set; }

        /// <summary>
        /// Margin from the left of the part to the left of the text bounding box (used for reflow).
        /// </summary>
        /// <value>15</value>
        [JsonPropertyName("left")]
        [JsonInclude]
        public int Left { get; set; }

        /// <summary>
        /// Margin from the right of the part to the right of the text bounding box (used for reflow).
        /// </summary>
        /// <value>15</value>
        [JsonPropertyName("right")]
        [JsonInclude]
        public int Right { get; set; }

        /// <summary>
        /// Margin from the top of the part to the top of the text bounding box (used for reflow).
        /// </summary>
        /// <value>10</value>
        [JsonPropertyName("top")]
        [JsonInclude]
        public int Top { get; set; }
    }

    public class Eraser
    {
        /// <summary>
        /// erase precisely
        /// </summary>
        [JsonPropertyName("erase-precisely")]
        [JsonInclude]
        public bool ErasePrecisely { get; set; }
    }

    public class XMath
    {
        [JsonPropertyName("mimeTypes")]
        [JsonInclude]
        public List<string> MimeTypes { get; set; }

        [JsonPropertyName("solver")]
        [JsonInclude]
        public Solver Solver { get; set; }

        [JsonPropertyName("margin")]
        [JsonInclude]
        public Margin Margin { get; set; }

        [JsonPropertyName("eraser")]
        [JsonInclude]
        public Eraser Eraser { get; set; }

        /// <summary>
        /// Name of the custom grammar uploaded in the developer space to use. See the <a href="https://developer.myscript.com/docs/interactive-ink/latest/web/advanced/custom-recognition/">custom recognition documentation.</a>
        /// </summary>
        [JsonPropertyName("customGrammarId")]
        [JsonInclude]
        public string CustomGrammarId { get; set; }

        /// <summary>
        /// Custom math grammar text. See the <a href="https://developer.myscript.com/docs/interactive-ink/latest/web/advanced/build-custom-resources/#math">reference documentation</a> to know how to build your custom grammar.
        /// </summary>
        [JsonPropertyName("customGrammarContent")]
        [JsonInclude]
        public string CustomGrammarContent { get; set; }
    }

    public class Text
    {
        /// <summary>
        /// If true, JIIX export will include the detailed characters information.
        /// </summary>
        /// <value>false</value>
        [JsonPropertyName("chars")]
        [JsonInclude]
        public bool Chars { get; set; }

        /// <summary>
        /// If true, JIIX export will include the detailed words information.
        /// </summary>
        /// <value>true</value>
        [JsonPropertyName("words")]
        [JsonInclude]
        public bool Words { get; set; }
    }

    public class XText
    {
        [JsonPropertyName("guides")]
        [JsonInclude]
        public Guides Guides { get; set; }

        [JsonPropertyName("smartGuide")]
        [JsonInclude]
        public bool SmartGuide { get; set; }

        [JsonPropertyName("smartGuideFadeOut")]
        [JsonInclude]
        public SmartGuideFadeOut SmartGuideFadeOut { get; set; }

        [JsonPropertyName("mimeTypes")]
        [JsonInclude]
        public List<string> MimeTypes { get; set; }

        [JsonPropertyName("margin")]
        [JsonInclude]
        public Margin Margin { get; set; }

        [JsonPropertyName("eraser")]
        [JsonInclude]
        public Eraser Eraser { get; set; }

        [JsonPropertyName("configuration")]
        [JsonInclude]
        public TextConfiguration Configuration { get; set; }
    }

    public class TextConfiguration
    {
        public TextConfiguration()
        {
            CustomLexicon = new List<string>();
            CustomResources = new List<string>();
        }

        /// <summary>
        /// Name of the custom resources uploaded in the developer space to use. See the <a href="https://developer.myscript.com/docs/interactive-ink/latest/web/advanced/custom-recognition/">custom recognition</a> reference documentation
        /// </summary>
        [JsonPropertyName("customResources")]
        [JsonInclude]
        public List<string> CustomResources { get; set; }

        /// <summary>
        /// List of custom words to be added to the recognition capabilities. See more about the <a href="https://developer.myscript.com/docs/interactive-ink/latest/web/advanced/build-custom-resources/#lexicon">lexicon</a>.
        /// </summary>
        [JsonPropertyName("customLexicon")]
        [JsonInclude]
        public List<string> CustomLexicon { get; set; }

        /// <summary>
        /// Choice to add to the configured custom resources, words in the lexicon of the current language. Note that, if set to false, only one word will be recognized from the inks you send.
        /// </summary>
        /// <value>true</value>
        [JsonPropertyName("addLKText")]
        [JsonInclude]
        public bool AddLkText { get; set; }
    }

    public class Jiix
    {
        [JsonPropertyName("bounding-box")]
        [JsonInclude]
        public bool BoundingBox { get; set; }

        [JsonPropertyName("strokes")]
        [JsonInclude]
        public bool Strokes { get; set; }

        [JsonPropertyName("text")]
        [JsonInclude]
        public Text Text { get; set; }
    }

    public class Export
    {
        [JsonPropertyName("image-resolution")]
        [JsonInclude]
        public int ImageResolution { get; set; }

        [JsonPropertyName("jiix")]
        [JsonInclude]
        public Jiix Jiix { get; set; }
    }

    public class Configuration
    {
        [JsonPropertyName("text")]
        [JsonInclude]
        public XText Text { get; set; }

        [JsonPropertyName("math")]
        [JsonInclude]
        public XMath Math { get; set; }

        [JsonPropertyName("lang")]
        [JsonInclude]
        public string Lang { get; set; }

        [JsonPropertyName("export")]
        [JsonInclude]
        public Export Export { get; set; }
    }

    public class Stroke
    {
        [JsonPropertyName("x")]
        [JsonInclude]
        public List<int> X { get; set; }

        [JsonPropertyName("y")]
        [JsonInclude]
        public List<int> Y { get; set; }

        [JsonPropertyName("t")]
        [JsonInclude]
        public List<long> T { get; set; }

        [JsonPropertyName("pointerType")]
        [JsonInclude]
        public string PointerType { get; set; }
    }

    public class StrokeGroup
    {
        [JsonPropertyName("penStyle")]
        [JsonInclude]
        public string PenStyle { get; set; }

        [JsonPropertyName("strokes")]
        [JsonInclude]
        public List<Stroke> Strokes { get; set; } = new ();
    }