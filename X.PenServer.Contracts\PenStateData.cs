﻿//  -- Function: PenStateData.cs
//  --- Project: PenServer
//  ---- Remark: 
//  ---- Author: Lucifer
//  ------ Date: 2023/04/07 16:39

using System.Text.Json.Serialization;

namespace X.PenServer.Contracts;

/// <summary>
/// 连接状态
/// </summary>
[Serializable]
public class PenStateData
{
	/// <summary>
	/// 连接状态: 1.连接 0.断开
	/// </summary>
	[JsonPropertyName(nameof(State))]
	[JsonInclude]
	public int State { get; set; }
}