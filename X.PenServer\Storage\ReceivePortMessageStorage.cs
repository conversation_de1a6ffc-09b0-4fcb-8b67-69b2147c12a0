﻿using AFPen.Public;
using System.Collections.Concurrent;

namespace X.PenServer.Storage
{
    public class ReceivePortMessageStorage
    {
        static readonly ConcurrentQueue<ReceivedPortMessageDto> msgQueue = new();

        /// <summary>
        /// push msg
        /// </summary>
        /// <param name="data"></param>
        public static void PushMessage(ReceivedPortMessageDto data)
        {
            msgQueue.Enqueue(data);
        }

        /// <summary>
        /// get msg queue
        /// </summary>
        /// <returns></returns>
        public static ConcurrentQueue<ReceivedPortMessageDto> GetMessageQueue()
        {
            return msgQueue;
        }

        public class ReceivedPortMessageDto
        {
            public string MacIdStr { get; set; }

            public ulong MacIdLong { get; set; }

            public string Mac { get; set; }

            public AFPortMsgType Msgtype { get; set; }
        }
    }
}
