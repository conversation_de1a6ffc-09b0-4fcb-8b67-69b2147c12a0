﻿// -- Function: StudentPaperItemInfo.cs
// --- Project: X.PenServer.Contracts
// ---- Remark: 
// ---- Author: Lucifer
// ------ Date: 2024/3/21 10:50
namespace X.PenServer.Contracts.Realtime;

using System.Text.Json.Serialization;

/// <summary>
/// 学生当前正在作答试卷题目信息
/// </summary>
public class StudentPaperItemInfo
{
	/// <summary>
	/// 学生id
	/// </summary>
	[JsonPropertyName(nameof(StudentId))]
	[JsonInclude]
	public string StudentId { get; set; }

	/// <summary>
	/// 试卷id
	/// </summary>
	[JsonPropertyName(nameof(PaperId))]
	[JsonInclude]
	public string PaperId { get; set; }

	/// <summary>
	/// 题目id
	/// </summary>
	[JsonPropertyName(nameof(ItemId))]
	[JsonInclude]
	public string ItemId { get; set; }
}