﻿// -- Function: IRealtimePenService.cs
// --- Project: X.PenServer.Interfaces
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2023/10/27 13:03

using X.PenServer.Contracts.Realtime;
using X.PenServer.Interfaces.Lifecycle;

namespace X.PenServer.Interfaces;

/// <summary>
/// 实时点位服务
/// </summary>
public interface IRealtimePenService : ISingletonService
{
    /// <summary>
    /// 获取学生列表
    /// </summary>
    /// <param name="classid">班级id</param>
    /// <param name="teacherid">教师id</param>
    /// <returns></returns>
    Task<List<UserInfo>> GetUserList(string classid, string teacherid);
}