<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>disable</Nullable>
    <ServerGarbageCollection>true</ServerGarbageCollection>
    <ConcurrentGarbageCollection>true</ConcurrentGarbageCollection>
    <ErrorOnDuplicatePublishOutputFiles>false</ErrorOnDuplicatePublishOutputFiles>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <AppendRuntimeIdentifierToOutputPath>false</AppendRuntimeIdentifierToOutputPath>
    <!--auto version start-->
    <Deterministic>false</Deterministic>
    <GenerateAssemblyFileVersionAttribute>false</GenerateAssemblyFileVersionAttribute>
    <GenerateAssemblyInformationalVersionAttribute>false</GenerateAssemblyInformationalVersionAttribute>
    <AssemblyVersion>1.0.*</AssemblyVersion>
    <!--auto version end-->
  </PropertyGroup>
  <PropertyGroup>
    <Copyright>Copyright © Lucifer. All rights reserved.</Copyright>
    <PackageId>X.PenServer.Models</PackageId>
    <PackageProjectUrl>https://github.com/X-Lucifer</PackageProjectUrl>
    <RepositoryUrl>https://github.com/X-Lucifer</RepositoryUrl>
    <Company>X Lucifer</Company>
    <Authors>Lucifer</Authors>
    <AutoIncrementPackageRevision>true</AutoIncrementPackageRevision>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="AWSSDK.Core" Version="3.7.304.31" />
    <PackageReference Include="AWSSDK.SecurityToken" Version="3.7.300.121" />
    <PackageReference Include="DnsClient" Version="1.8.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.1" />
    <PackageReference Include="MongoDB.Driver" Version="2.28.0" />
    <PackageReference Include="MongoDB.Libmongocrypt" Version="1.11.0" />
    <PackageReference Include="SharpCompress" Version="0.37.2" />
    <PackageReference Include="Snappier" Version="1.1.6" />
    <PackageReference Include="System.Runtime.CompilerServices.Unsafe" Version="6.0.0" />
    <PackageReference Include="System.Security.AccessControl" Version="6.0.1" />
    <PackageReference Include="ZstdSharp.Port" Version="0.8.1" />
  </ItemGroup>

</Project>
