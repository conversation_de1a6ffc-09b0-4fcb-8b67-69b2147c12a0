﻿// -- Function: IDotQueueService.cs
// --- Project: X.PenServer.Interfaces
// ---- Remark: 
// ---- Author: Lucifer
// ------ Date: 2023/10/30 10:29

using X.PenServer.Contracts.Queue;
using X.PenServer.Interfaces.Lifecycle;

namespace X.PenServer.Interfaces;

/// <summary>
/// 点阵笔点位处理
/// </summary>
public interface IDotQueueService : ISingletonService
{
    /// <summary>
    /// 添加点位数据
    /// </summary>
    /// <param name="data"></param>
    /// <returns></returns>
    Task ProcessAsync(PenDotData data);

    /// <summary>
    /// 录制
    /// </summary>
    /// <param name="data"></param>
    /// <returns></returns>
    Task RecordProcessAsync(PenRecordData data);

    /// <summary>
    /// 处理离线批改
    /// </summary>
    /// <param name="data"></param>
    /// <returns></returns>
    Task ProcessOfflineAsync(OfflineCorrectPenDotData data);
}