﻿// -- Function: ItemRangeMatrix.cs
// --- Project: X.PenServer.Infrastructure
// ---- Remark: 
// ---- Author: Lucifer
// ------ Date: 2024/2/20 14:46
namespace X.PenServer.Infrastructure;

using System.Text.Json.Serialization;

/// <summary>
/// 题目区域
/// </summary>
public class MatrixItemRange : MatrixBase
{
	/// <summary>
	/// 题目编号
	/// </summary>
	/// <remarks>
	/// 默认情况为题目编号,当大区域类型为卷码, 则代表卷码号列编号, 练习薄时为行号
	/// </remarks>
	[JsonInclude]
	[JsonPropertyName(nameof(ItemNo))]
	public int ItemNo { get; set; }

	/// <summary>
	/// 题目矩阵列表
	/// </summary>
	[JsonInclude]
	[JsonPropertyName(nameof(Items))]
	public List<MatrixItem> Items { get; set; } = new();

	/// <summary>
	/// 题目分数
	/// </summary>
	[JsonInclude]
	[JsonPropertyName(nameof(Score))]
	public float Score { get; set; }
}