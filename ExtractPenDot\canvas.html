<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="author" content="朱春锋">
    <meta name="date" content="2024-10-17">
    <title>绘制工具</title>

    <style>
        * {
            margin: 0;
            padding: 0;
        }

        #test {
            border: 1px solid #ccc;
            background: #eee;
            margin: 20px 30px;
        }

        .btnStart,
        .btnStop,
        .btnPause,
        .btnResume {
            margin-left: 10px;
            margin-top: 10px;
            padding: 5px 10px;
        }

        .menus {
            position: fixed;
            top: 0;
            left: 30px;
        }

        .sec {
            width: 80px;
            margin-left: 10px;
        }

        .jsonContent {
            margin-top: 100px;
            margin-left: 30px;
            width: 80%;
            height: 300px;
        }

         footer.simple-footer {
            background-color: #f5f5f5;
            text-align: center;        
            padding: 10px 0; 
            color: #999;          
            font-size: 12px;           
            border-top: 1px solid #eee;
        }
    </style>

</head>

<body>

    <p class="menus">
        <button id="start" class="btnStart">开始</button>
        <button id="pause" class="btnPause">暂停</button>
        <button id="resume" class="btnResume">恢复</button>
        <button id="stop" class="btnStop">停止</button>
        &nbsp;绘制速度<input type="number" value="0" id="speed" class="sec" /> 秒
    </p>

    <textarea class="jsonContent" id="json"></textarea>

    <canvas id="test" width="4000" height="4000"></canvas>

    <footer class="simple-footer">
        <p>作者：朱春锋  日期：2024-10-17</p>
    </footer>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script>



        var start = $("#start");
        var pause = $("#pause");
        var resume = $("#resume");
        var stop = $("#stop");
        var speed = $("#speed");

        var obj = document.getElementById("test")
        var cxt = obj.getContext("2d");
        cxt.lineWidth = 1;
        cxt.strokeStyle = "red";
        cxt.lineCap = "round";
        btnStatusSet(pause, true)
        btnStatusSet(resume, true)
        btnStatusSet(stop, true)

        var lineIndex = 0, pointindex = 0, line2;
        var isMove = false;
        var intervalHandle = new PausableInterval(function () {

            line2 = xys[lineIndex];

            if (!line2) {
                this.stop()
            }

            if (lineIndex < xys.length - 1) {

                var remark = '';
                if (line2.Type === 2) {
                    remark = 'END'
                }

                console.log('[第' + (lineIndex + 1) + '条]', line2.X, line2.Y, line2.Type, remark)

                if (lineIndex == 0 || isMove) {
                    cxt.moveTo(line2.X, line2.Y);
                    isMove = false
                }

                lineIndex = lineIndex + 1;

                if (line2.Type === 2) {
                    isMove = true
                }
                else {
                    cxt.lineTo(line2.X, line2.Y);
                }

                cxt.stroke();
            }

        });

        var xys = undefined
        start.click(function (event) {

            xys = $("#json").val()

            if (!xys) {
                alert('请填写相应坐标')
                return;
            }

            xys = JSON.parse(xys)

            console.log('加载 ' + xys.length + ' 条数据')


            if (!timerId && !isPaused) {
                cxt.beginPath();
                lineIndex = 0;
                line2 = undefined;
            }

            interval = speed.val();

            intervalHandle.start()

            btnStatusSet(start, true)
            btnStatusSet(pause, false)
            btnStatusSet(resume, false)
            btnStatusSet(stop, false)
        });

        pause.click(function (event) {
            intervalHandle.pause();
            btnStatusSet(pause, true)
        });

        resume.click(function (event) {
            intervalHandle.resume();
            btnStatusSet(pause, false)
        });

        stop.click(function (event) {
            intervalHandle.stop();
            cxt.clearRect(0, 0, 4000, 4000);
            console.clear();
            btnStatusSet(start, false)
            btnStatusSet(pause, true)
            btnStatusSet(resume, true)
            btnStatusSet(stop, true)
        });

        var timerId;
        var isPaused = false;
        var interval = 0

        function PausableInterval(callback) {

            this.pause = function () {
                isPaused = true;
                this.stop();
            };

            this.resume = function () {
                isPaused = false;
                if (timerId === undefined) {
                    this.start();
                }
            };

            this.start = function () {
                if (!timerId) {
                    timerId = window.setInterval(callback, interval);
                }
            };

            this.stop = function () {
                if (timerId) {
                    window.clearInterval(timerId);
                    timerId = undefined;
                    isPaused = false;
                }
            };
        }

        function btnStatusSet(btn, isdisabled) {
            btn.prop('disabled', isdisabled);
        }

    </script>
</body>

</html>