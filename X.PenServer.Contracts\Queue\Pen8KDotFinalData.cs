﻿using System.Text.Json.Serialization;

namespace X.PenServer.Contracts.Queue
{
    /// <summary>
    /// <para><AUTHOR> <PERSON></para>
    /// <para>@Date   2025-05-08 14:45</para>
    /// <para>@Description </para>
    /// </summary>
    public class Pen8KDotFinalData
    {
        /// <summary>
        /// 点位坐标
        /// </summary>
        [JsonPropertyName(nameof(Dots))]
        [JsonInclude]
        public List<PenDot> Dots { get; set; }

        /// <summary>
        /// 实际采集到的页码id
        /// </summary>
        /// <remarks>明鼎实际返回的页码id</remarks>
        [JsonPropertyName(nameof(Page))]
        [JsonInclude]
        public int Page { get; set; }

        /// <summary>
        /// 业务页码id
        /// </summary>
        /// <remarks>除去卷码纸,其余情况都一致</remarks>
        [JsonPropertyName(nameof(PageId))]
        [JsonInclude]
        public int PageId { get; set; }

        /// <summary>
        /// 试卷id
        /// </summary>
        [JsonPropertyName(nameof(PaperId))]
        [JsonInclude]
        public string PaperId { get; set; }

        /// <summary>
        /// 用户id
        /// </summary>
        [JsonPropertyName(nameof(UserId))]
        [JsonInclude]
        public string UserId { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        [JsonPropertyName(nameof(DoStatus))]
        [JsonInclude]
        public int DoStatus { get; set; }
    }
}
