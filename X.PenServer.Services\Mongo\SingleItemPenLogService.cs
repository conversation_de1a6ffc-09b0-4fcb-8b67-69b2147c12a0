﻿// -- Function: SingleItemPenLogService.cs
// --- Project: X.PenServer.Services
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2024/04/04 15:04:05

namespace X.PenServer.Services.Mongo;

/// <inheritdoc cref="ISingleItemPenLogService" />
public class SingleItemPenLogService : MongoAutoService<SingleItemPenLog>, ISingleItemPenLogService
{
    /// <inheritdoc />
    public SingleItemPenLogService(IMongoConfig config) : base(config)
    {
    }

    /// <inheritdoc />
    protected override void CreateIndex(IMongoCollection<SingleItemPenLog> collection)
    {
        var userid_builder = Builders<SingleItemPenLog>.IndexKeys
            .Ascending(x => x.UserId)
            .Ascending(x => x.ItemId)
            .Ascending(x => x.PageId)
            .Ascending(x => x.Mac)
            .Ascending(x => x.PaperId)
            .Ascending(x => x.Page);
        collection.Indexes.CreateIndex(userid_builder, collection.CollectionNamespace.CollectionName + "_UserId_Key");

        var pageid_builder = Builders<SingleItemPenLog>.IndexKeys
            .Ascending(x => x.PageId)
            .Ascending(x => x.UserId)
            .Ascending(x => x.ItemId)
            .Ascending(x => x.Mac)
            .Ascending(x => x.PaperId);
        collection.Indexes.CreateIndex(pageid_builder, collection.CollectionNamespace.CollectionName + "_PageId_Key");

        var itemid_builder = Builders<SingleItemPenLog>.IndexKeys
            .Ascending(x => x.ItemId)
            .Ascending(x => x.UserId)
            .Ascending(x => x.PageId)
            .Ascending(x => x.Mac)
            .Ascending(x => x.PaperId);
        collection.Indexes.CreateIndex(itemid_builder, collection.CollectionNamespace.CollectionName + "_ItemId_Key");
    }
}