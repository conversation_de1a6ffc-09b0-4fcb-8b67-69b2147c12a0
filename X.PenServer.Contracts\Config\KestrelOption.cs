﻿// -- Function: KestrelOption.cs
// --- Project: X.PenServer.Contracts
// ---- Remark: 
// ---- Author: Lucifer
// ------ Date: 2024/3/26 11:4
namespace X.PenServer.Contracts.Config;

using System.Text.Json.Serialization;

/// <summary>
/// KestrelOption
/// </summary>
public class KestrelOption
{
	/// <summary>
	/// 监听端口
	/// </summary>
	[JsonInclude]
	[JsonPropertyName(nameof(Port))]
	public int Port { get; set; }

	/// <summary>
	/// 协议版本
	/// </summary>
	[JsonInclude]
	[JsonPropertyName(nameof(Protocol))]
	[JsonConverter(typeof(JsonStringEnumConverter))]
	public HttpProtocol Protocol { get; set; }
}