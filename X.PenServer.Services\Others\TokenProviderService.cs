﻿// -- Function: TokenProviderService.cs
// --- Project: X.PenServer.Services
// ---- Remark: 
// ---- Author: Lucifer
// ------ Date: 2024/3/6 9:46

using X.PenServer.Interfaces.Redis;

namespace X.PenServer.Services.Others;

using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using Microsoft.IdentityModel.Tokens;
using Interfaces;
using JwtRegisteredClaimNames = Microsoft.IdentityModel.JsonWebTokens.JwtRegisteredClaimNames;

/// <inheritdoc />
public class TokenProviderService : ITokenProviderService
{
	private readonly ISsoConfig _sso_config;
	private const int _expires = 60;
	private readonly ICommonRedisService _redis_service;

	public TokenProviderService(ISsoConfig sso_config, ICommonRedisService redis_service)
	{
		_sso_config = sso_config;
		_redis_service = redis_service;
	}

	#region Implementation of ITokenProviderService

	/// <inheritdoc />
	public async Task<string> GetTokenAsync()
	{
		var token = await _redis_service.GetStringAsync(RedisKeys.RPC_TOKEN);
		if (!string.IsNullOrWhiteSpace(token))
		{
			return token;
		}

		var bytes = Encoding.UTF8.GetBytes(_sso_config.SigningKey);
		var key = new SymmetricSecurityKey(bytes);
		var xoid = Guid.NewGuid().ToString("N");
		var claims = new[]
		{
			new Claim(JwtRegisteredClaimNames.Exp, $"{DateTimeOffset.Now.AddDays(_expires).ToUnixTimeSeconds()}"),
			new Claim("Name", _sso_config.Audience, ClaimValueTypes.String),
			new Claim("Oid", xoid, ClaimValueTypes.String),
			new Claim("Issuer", _sso_config.Issuer, ClaimValueTypes.String)
		};
		var credit = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);
		var jwttoken = new JwtSecurityToken
		(
			_sso_config.Issuer,
			_sso_config.Audience,
			claims,
			expires: DateTime.UtcNow.AddDays(_expires),
			signingCredentials: credit
		);
		token = new JwtSecurityTokenHandler().WriteToken(jwttoken);
		await _redis_service.SetAsync(RedisKeys.RPC_TOKEN, token, TimeSpan.FromDays(_expires));
		return token;
	}

	#endregion
}