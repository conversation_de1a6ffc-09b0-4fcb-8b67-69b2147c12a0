﻿// -- Function: TestTeacherCorrectRedis.cs
// --- Project: X.PenServer.UnitTest
// ---- Remark: 
// ---- Author: Lucifer
// ------ Date: 2024/06/04 15:06

using X.PenServer.Interfaces.Redis;
using Xunit.Abstractions;
using Xunit.Microsoft.DependencyInjection.Abstracts;

namespace X.PenServer.UnitTest;

/// <inheritdoc />
public class TestTeacherCorrectRedis : TestBed<TestFixture>
{
    private readonly ITeacherCorrectRedisService _redis_service;
    private readonly ITestOutputHelper _test_output_helper;

    /// <inheritdoc />
    public TestTeacherCorrectRedis(ITestOutputHelper test_output_helper, TestFixture test_fixture) : base(test_output_helper, test_fixture)
    {
        _test_output_helper = test_output_helper;
        _redis_service = test_fixture.GetService<ITeacherCorrectRedisService>(test_output_helper);
    }

    [Fact]
    public async Task MockAsync()
    {
        var teacherid = "1633321171384762368";
        var studentid = await _redis_service.HGetCorrectStudentAsync(teacherid);
        var correct_student = await _redis_service.GetCorrectStudentAsync(teacherid);
        var classid = await _redis_service.HGetCorrectClassIdAsync(teacherid);
        Assert.Equal(studentid, correct_student.StudentId);
        Assert.Equal(classid, correct_student.ClassId);
        var status = await _redis_service.HGetCorrectStudentStatusAsync(correct_student.PaperId, studentid);
        _test_output_helper.WriteLine($"classid: {classid} , paperid: {correct_student.PaperId} , studentid: {correct_student.StudentId} , status: {status}");
    }
}