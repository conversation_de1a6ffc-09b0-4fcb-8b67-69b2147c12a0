﻿// -- Function: MongoAsyncService.cs
// --- Project: X.PenServer.Services
// ---- Remark: 
// ---- Author: Lucifer
// ------ Date: 2023/10/27 10:16

namespace X.PenServer.Services.Mongo;

using System.Diagnostics.CodeAnalysis;

[SuppressMessage("ReSharper", "InconsistentNaming")]
[SuppressMessage("ReSharper", "MemberCanBePrivate.Global")]
[SuppressMessage("ReSharper", "VirtualMemberCallInConstructor")]
public abstract partial class MongoService<T>
{
    protected readonly IMongoCollection<T> _mongo;

    protected MongoService(IMongoConfig config)
    {
        var client = new MongoClient(config.ConnectionString);
        var database = client.GetDatabase(config.DatabaseName);
        _mongo = database.GetCollection<T>(typeof(T).Name);
        if (_mongo != null)
        {
            CreateIndex(_mongo);
        }
    }

    /// <summary>
    /// 创建索引
    /// </summary>
    /// <param name="collection"></param>
    protected abstract void CreateIndex(IMongoCollection<T> collection);

    /// <inheritdoc />
    public virtual async Task<T> GetAsync(long id)
    {
        return await _mongo.Find(x => x.Mid.Equals(id)).Limit(1).FirstOrDefaultAsync();
    }

    /// <inheritdoc />
    public virtual async Task<T> GetAsync(Expression<Func<T, bool>> predicate = null)
    {
        Expression<Func<T, bool>> xpredicate = exp => true;
        predicate ??= xpredicate;
        return await _mongo.Find(predicate).Limit(1).FirstOrDefaultAsync();
    }

    /// <inheritdoc />
    public virtual async Task<List<T>> GetAllAsync(Expression<Func<T, bool>> predicate = null, FindOptions<T> options = null)
    {
        Expression<Func<T, bool>> xpredicate = exp => true;
        predicate ??= xpredicate;
        var list = await _mongo.FindAsync(predicate, options);
        return await list.ToListAsync();
    }

    /// <inheritdoc />
    public virtual async Task<PageEnumerable<T>> GetPagedListAsync(Expression<Func<T, bool>> predicate = null, Expression<Func<T, object>> order = null, int page = 1, int pagesize = 10, bool isasc = false)
    {
        Expression<Func<T, bool>> xpredicate = exp => true;
        predicate ??= xpredicate;
        var total = await _mongo.CountDocumentsAsync(predicate);
        var list = _mongo.Find(predicate);
        list = order != null ? isasc ? list.SortBy(order) : list.SortByDescending(order) : list.SortBy(x => x.Mid);
        list = list.Limit(pagesize).Skip((page - 1) * pagesize);
        var xlist = await list.ToListAsync();
        return new PageEnumerable<T>
        {
            Result = xlist,
            Total = total,
            PageIndex = page,
            PageSize = pagesize
        };
    }

    /// <inheritdoc />
    public virtual async Task AddAsync(T entity)
    {
        await _mongo.InsertOneAsync(entity);
    }

    /// <inheritdoc />
    public virtual async Task AddManyAsync(IEnumerable<T> entities)
    {
        await _mongo.InsertManyAsync(entities);
    }

    /// <inheritdoc />
    public virtual async Task<bool> UpdateAsync(Expression<Func<T, bool>> predicate, T entity)
    {
        var result = await _mongo.ReplaceOneAsync(predicate, entity);
        return result.ModifiedCount > 0;
    }

    /// <inheritdoc />
    public virtual async Task<bool> UpdateAsync(long id, T entity)
    {
        var result = await _mongo.ReplaceOneAsync(x => x.Mid.Equals(id), entity);
        return result.ModifiedCount > 0;
    }

    /// <inheritdoc />
    public virtual async Task<bool> DeleteAsync(long id)
    {
        var result = await _mongo.DeleteOneAsync(x => x.Mid.Equals(id));
        return result.DeletedCount > 0;
    }

    /// <inheritdoc />
    public virtual async Task<bool> DeleteAsync(Expression<Func<T, bool>> predicate)
    {
        var result = await _mongo.DeleteOneAsync(predicate);
        return result.DeletedCount > 0;
    }

    /// <inheritdoc />
    public virtual async Task<bool> DeleteManyAsync(Expression<Func<T, bool>> predicate)
    {
        var result = await _mongo.DeleteManyAsync(predicate);
        return result.DeletedCount > 0;
    }
}