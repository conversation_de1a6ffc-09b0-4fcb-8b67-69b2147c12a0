﻿// -- Function: TestPenMappingRedis.cs
// --- Project: X.PenServer.UnitTest
// ---- Remark: 
// ---- Author: Lucifer
// ------ Date: 2024/06/04 15:06

using X.PenServer.Interfaces.Redis;
using Xunit.Abstractions;
using Xunit.Microsoft.DependencyInjection.Abstracts;

namespace X.PenServer.UnitTest;

/// <inheritdoc />
public class TestPenMappingRedis : TestBed<TestFixture>
{
    private readonly IPenMappingRedisService _redis_service;
    private readonly ITestOutputHelper _test_output_helper;

    /// <inheritdoc />
    public TestPenMappingRedis(ITestOutputHelper test_output_helper,TestFixture test_fixture) : base(test_output_helper, test_fixture)
    {
        _test_output_helper = test_output_helper;
        _redis_service = test_fixture.GetService<IPenMappingRedisService>(test_output_helper);
    }

    [Fact]
    public async Task MockAsync()
    {
        const string key = "00E04C0017C4";
        var userid = await _redis_service.HGetMappingUserAsync(key);
        var role = await _redis_service.HGetPenRoleAsync(key);
        _test_output_helper.WriteLine($"userid: {userid} , role: {role}");
        await _redis_service.HSetMappingUserAsync(key, userid);
    }
}