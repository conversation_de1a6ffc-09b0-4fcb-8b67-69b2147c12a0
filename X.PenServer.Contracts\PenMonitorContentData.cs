﻿// -- Function: PenMonitorContentData.cs
// --- Project: X.PenServer.Contracts
// ---- Remark: 
// ---- Author: Lucifer
// ------ Date: 2023/12/01 14:44
namespace X.PenServer.Contracts;

using System.Text.Json.Serialization;

public class PenMonitorContentData
{
	/// <summary>
	/// 编号
	/// </summary>
	[JsonInclude]
	[JsonPropertyName(nameof(Mac))]
	public string Mac { get; set; }

	/// <summary>
	/// 连接状态
	/// </summary>
	[JsonInclude]
	[JsonPropertyName(nameof(State))]
	public PenStateData State { get; set; }

	/// <summary>
	/// 笔
	/// </summary>
	[JsonInclude]
	[JsonPropertyName(nameof(Pen))]
	public PenPrensenter Pen { get; set; }
}