﻿// -- Function: MatrixDetail.cs
// --- Project: X.PenServer.Infrastructure
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2024/2/20 17:43

// ReSharper disable PropertyCanBeMadeInitOnly.Global
namespace X.PenServer.Infrastructure;

/// <summary>
/// 矩阵详细信息
/// </summary>
public class MatrixDetail
{
	/// <summary>
	/// 题号矩阵
	/// </summary>
	public MatrixItem Item { get; set; }

	/// <summary>
	/// 题号
	/// </summary>
	public int ItemNo { get; set; }

	/// <summary>
	/// 大区域类型
	/// </summary>
	/// <seealso cref="MatrixRegion"/>
	public int RegionType { get; set; }

	/// <summary>
	/// 大区域名称
	/// </summary>
	public string RegionName { get; set; }

	/// <summary>
	/// 题目总分值
	/// </summary>
	public float Score { get; set; }

	/// <summary>
	/// 是否多个分值
	/// </summary>
	public bool IsMultiScore { get; set; }
}