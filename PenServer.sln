﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.7.34202.233
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "X.PenServer", "X.PenServer\X.PenServer.csproj", "{933E42F9-BA6F-45E8-8C2C-9880A68C5CCD}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "X.PenServer.Models", "X.PenServer.Models\X.PenServer.Models.csproj", "{DD6BB319-8792-4872-B002-31EEA6027683}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "X.PenServer.Interfaces", "X.PenServer.Interfaces\X.PenServer.Interfaces.csproj", "{E556396F-27EF-4135-BBD8-591562D670C2}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "X.PenServer.PenQueue", "X.PenServer.PenQueue\X.PenServer.PenQueue.csproj", "{170B5063-B02E-486C-BF90-4A5AE68E25CF}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "X.PenServer.Services", "X.PenServer.Services\X.PenServer.Services.csproj", "{686E137A-E43F-4A07-AF41-65908D4C772E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "X.PenServer.Contracts", "X.PenServer.Contracts\X.PenServer.Contracts.csproj", "{F6A80A47-317D-440E-8CD1-E93C44E99EC8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "X.PenServer.Infrastructure", "X.PenServer.Infrastructure\X.PenServer.Infrastructure.csproj", "{F3C2D929-2F06-4BE3-AD13-78B3A918F360}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "X.PenServer.Sockets", "X.PenServer.Sockets\X.PenServer.Sockets.csproj", "{40BE0CE5-E786-49AB-A07C-1656E2E6828D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "X.PenServer.Profiles", "X.PenServer.Profiles\X.PenServer.Profiles.csproj", "{D3E2D52C-C8E1-448D-8819-A0D588CF3255}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "X.PenServer.Modules", "X.PenServer.Modules\X.PenServer.Modules.csproj", "{304746C7-C234-4A8C-8172-BAE8609D0F8D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "X.PenServer.Middlewares", "X.PenServer.Middlewares\X.PenServer.Middlewares.csproj", "{********-83BE-4902-BECA-AEA551EF1905}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "X.PenServer.Protos", "X.PenServer.Protos\X.PenServer.Protos.csproj", "{AB2937CC-477F-4184-96EC-3F4A069EA779}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "X.PenServer.Jobs", "X.PenServer.Jobs\X.PenServer.Jobs.csproj", "{6165222B-FEFD-480F-BF9F-C62B4D85917E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "X.PenServer.UnitTest", "X.PenServer.UnitTest\X.PenServer.UnitTest.csproj", "{D8BECEAD-A2ED-48F0-8CAE-53D7889AC62C}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "工具包", "工具包", "{61B42141-168C-4394-BAFE-BCBE81C58D2B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ExtractPenDot", "ExtractPenDot\ExtractPenDot.csproj", "{2C443910-4FF4-4318-8715-8EB98337F732}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{933E42F9-BA6F-45E8-8C2C-9880A68C5CCD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{933E42F9-BA6F-45E8-8C2C-9880A68C5CCD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{933E42F9-BA6F-45E8-8C2C-9880A68C5CCD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{933E42F9-BA6F-45E8-8C2C-9880A68C5CCD}.Release|Any CPU.Build.0 = Release|Any CPU
		{DD6BB319-8792-4872-B002-31EEA6027683}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DD6BB319-8792-4872-B002-31EEA6027683}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DD6BB319-8792-4872-B002-31EEA6027683}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DD6BB319-8792-4872-B002-31EEA6027683}.Release|Any CPU.Build.0 = Release|Any CPU
		{E556396F-27EF-4135-BBD8-591562D670C2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E556396F-27EF-4135-BBD8-591562D670C2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E556396F-27EF-4135-BBD8-591562D670C2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E556396F-27EF-4135-BBD8-591562D670C2}.Release|Any CPU.Build.0 = Release|Any CPU
		{170B5063-B02E-486C-BF90-4A5AE68E25CF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{170B5063-B02E-486C-BF90-4A5AE68E25CF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{170B5063-B02E-486C-BF90-4A5AE68E25CF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{170B5063-B02E-486C-BF90-4A5AE68E25CF}.Release|Any CPU.Build.0 = Release|Any CPU
		{686E137A-E43F-4A07-AF41-65908D4C772E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{686E137A-E43F-4A07-AF41-65908D4C772E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{686E137A-E43F-4A07-AF41-65908D4C772E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{686E137A-E43F-4A07-AF41-65908D4C772E}.Release|Any CPU.Build.0 = Release|Any CPU
		{F6A80A47-317D-440E-8CD1-E93C44E99EC8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F6A80A47-317D-440E-8CD1-E93C44E99EC8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F6A80A47-317D-440E-8CD1-E93C44E99EC8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F6A80A47-317D-440E-8CD1-E93C44E99EC8}.Release|Any CPU.Build.0 = Release|Any CPU
		{F3C2D929-2F06-4BE3-AD13-78B3A918F360}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F3C2D929-2F06-4BE3-AD13-78B3A918F360}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F3C2D929-2F06-4BE3-AD13-78B3A918F360}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F3C2D929-2F06-4BE3-AD13-78B3A918F360}.Release|Any CPU.Build.0 = Release|Any CPU
		{40BE0CE5-E786-49AB-A07C-1656E2E6828D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{40BE0CE5-E786-49AB-A07C-1656E2E6828D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{40BE0CE5-E786-49AB-A07C-1656E2E6828D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{40BE0CE5-E786-49AB-A07C-1656E2E6828D}.Release|Any CPU.Build.0 = Release|Any CPU
		{D3E2D52C-C8E1-448D-8819-A0D588CF3255}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D3E2D52C-C8E1-448D-8819-A0D588CF3255}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D3E2D52C-C8E1-448D-8819-A0D588CF3255}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D3E2D52C-C8E1-448D-8819-A0D588CF3255}.Release|Any CPU.Build.0 = Release|Any CPU
		{304746C7-C234-4A8C-8172-BAE8609D0F8D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{304746C7-C234-4A8C-8172-BAE8609D0F8D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{304746C7-C234-4A8C-8172-BAE8609D0F8D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{304746C7-C234-4A8C-8172-BAE8609D0F8D}.Release|Any CPU.Build.0 = Release|Any CPU
		{********-83BE-4902-BECA-AEA551EF1905}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{********-83BE-4902-BECA-AEA551EF1905}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{********-83BE-4902-BECA-AEA551EF1905}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{********-83BE-4902-BECA-AEA551EF1905}.Release|Any CPU.Build.0 = Release|Any CPU
		{AB2937CC-477F-4184-96EC-3F4A069EA779}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AB2937CC-477F-4184-96EC-3F4A069EA779}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AB2937CC-477F-4184-96EC-3F4A069EA779}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AB2937CC-477F-4184-96EC-3F4A069EA779}.Release|Any CPU.Build.0 = Release|Any CPU
		{6165222B-FEFD-480F-BF9F-C62B4D85917E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6165222B-FEFD-480F-BF9F-C62B4D85917E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6165222B-FEFD-480F-BF9F-C62B4D85917E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6165222B-FEFD-480F-BF9F-C62B4D85917E}.Release|Any CPU.Build.0 = Release|Any CPU
		{D8BECEAD-A2ED-48F0-8CAE-53D7889AC62C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D8BECEAD-A2ED-48F0-8CAE-53D7889AC62C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D8BECEAD-A2ED-48F0-8CAE-53D7889AC62C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D8BECEAD-A2ED-48F0-8CAE-53D7889AC62C}.Release|Any CPU.Build.0 = Release|Any CPU
		{2C443910-4FF4-4318-8715-8EB98337F732}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2C443910-4FF4-4318-8715-8EB98337F732}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2C443910-4FF4-4318-8715-8EB98337F732}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2C443910-4FF4-4318-8715-8EB98337F732}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{2C443910-4FF4-4318-8715-8EB98337F732} = {61B42141-168C-4394-BAFE-BCBE81C58D2B}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {9362051A-4668-4DD1-A8F6-664BD6860298}
	EndGlobalSection
EndGlobal
