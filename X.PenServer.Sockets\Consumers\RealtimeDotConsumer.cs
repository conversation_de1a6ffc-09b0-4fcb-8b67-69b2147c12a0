﻿//   -- Function: RealtimeDotConsumer.cs
//   --- Project: X.PenServer.Sockets
//   ---- Remark: 
//   ---- Author: Lucifer
//   ------ Date: 2023/09/19 13:25

using System.Collections.Immutable;
using System.Net.WebSockets;
using MassTransit;
using X.PenServer.Contracts.Realtime;
using X.PenServer.Contracts.Socket;
using static X.PenServer.Middlewares.MiddlewareExtensions;
using static X.PenServer.Middlewares.RealtimePenSocketMiddleware;

namespace X.PenServer.Sockets.Consumers;

using Infrastructure;

/// <summary>
/// 接收实时点位消费队列
/// </summary>
// ReSharper disable once ClassNeverInstantiated.Global
public class RealtimeDotConsumer : IConsumer<Batch<RealtimeDotModel>>
{
    private readonly ParallelOptions _parallel_options;

    public RealtimeDotConsumer(ParallelOptions parallel_options)
    {
        _parallel_options = parallel_options;
    }

    /// <inheritdoc />
    public async Task Consume(ConsumeContext<Batch<RealtimeDotModel>> context)
    {
        //接收实时点位
        foreach (var item in context.Message)
        {
            var pendata = item.Message;

            //兼容前端实时笔迹显示字段
            pendata.Page = pendata.PageId;

            //学生笔迹
            var wids = GetStudentClients(pendata.PaperId, pendata.UserId);
            if (wids is null || wids.IsEmpty)
            {
                continue;
            }

            var sockets = ImmutableHashSet.Create<WebSocket>();
            sockets = wids.Select(GetSocketClient).Where(socket => socket is
            {
                State: WebSocketState.Open
            }).Aggregate(sockets, (current, socket) => current.Add(socket));
            if (sockets.Count <= 0)
            {
                continue;
            }

            await Parallel.ForEachAsync
            (
                sockets, _parallel_options, async (x, xtoken) =>
                {
                    var xdata = new SendData
                    {
                        Type = "202",
                        Content = pendata.ToJsonString()
                    };

                    if (pendata.UserType == 0)
                    {
                        xdata.ClassId = pendata.ClassId;
                    }

                    await SendAsync(x, xdata.ToJsonString(), xtoken).ConfigureAwait(false);
                }
            ).ConfigureAwait(false);
        }

        await context.ConsumeCompleted.ConfigureAwait(false);
    }
}