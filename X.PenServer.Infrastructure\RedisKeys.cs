﻿// -- Function: RedisKeys.cs
// --- Project: X.PenServer.Infrastructure
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2023/10/30 17:26

namespace X.PenServer.Infrastructure;

/// <summary>
/// Redis键
/// </summary>
public static class RedisKeys
{
    /// <summary>
    /// 点阵笔用户映射关系
    /// </summary>
    public const string PEN_MAPPING = "Pen_Mapping";

    /// <summary>
    /// 学生信息
    /// </summary>
    public const string STUDENT = "StudentInfo";

    /// <summary>
    /// 教师信息
    /// </summary>
    public const string BASE_USER = "Base_User";

    /// <summary>
    /// 教师批改
    /// </summary>
    public const string TEACHER_CORRECT = "Teacher_Correct";

    /// <summary>
    /// 做卷相关缓存
    /// </summary>
    public const string PAPER_LOG = "Paper_Log";

    /// <summary>
    /// 试卷相关
    /// </summary>
    public const string PAPER_INFO = "Paper_Info";

    /// <summary>
    /// RPC Token
    /// </summary>
    public const string RPC_TOKEN = "Rpc_Token";

    /// <summary>
    /// 矩阵
    /// </summary>
    public const string MATRIX = "Matrix";

    /// <summary>
    /// 英语单元答题卡
    /// </summary>
    public const string ENGLISH_UNIT_CARD = "English_Unit_Card";

    /// <summary>
    /// 通用互动答题卡
    /// </summary>
    public const string UNIVERSAL_CARD = "Universal_Card";

    /// <summary>
    /// 练习册标签学号
    /// </summary>
    public const string PAPER_TAG_STUDENT_NO = "Paper_Tag_Student_No";

    /// <summary>
    /// 练习薄
    /// </summary>
    public const string WORKBOOK = "Workbook";

    /// <summary>
    /// 用户信息
    /// </summary>
    public const string UserInfo = "PenServer_UserInfo";

    /// <summary>
    /// 开始作答点阵笔
    /// </summary>
    public const string StudentStartAnswerPaper = "StartAnswerPaperDZB";

    /// <summary>
    /// Doubao Huawei OBS Url 
    /// </summary>
    public const string DoubaoHuaweiOBSUrl = "DoubaoHuaweiOBSUrl";
}