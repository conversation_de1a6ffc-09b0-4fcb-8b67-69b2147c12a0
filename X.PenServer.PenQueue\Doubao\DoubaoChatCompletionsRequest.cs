﻿namespace Uwoo.Util.Doubao
{
    public class DoubaoChatCompletionsRequest
    {
        public string model { get; set; }

        public List<ContentBaseWrap> messages { get; set; } = new List<ContentBaseWrap>();

        public class ContentBaseWrap
        {
            public string role { get; set; }

            public List<ContentBase> content { get; set; } = new List<ContentBase>();
        }

        public abstract class ContentBase
        {
            public abstract string type { get; }
        }

        public class TxtContent : ContentBase
        {
            public string text { get; set; }

            public override string type { get; } = "text";
        }

        public class Image_url
        {
            public string url { get; set; }
        }

        public class ImageContent : ContentBase
        {
            public Image_url image_url { get; set; } = new Image_url();

            public override string type { get; } = "image_url";
        }
    }

    public class DoubaoChatCompletionsResponse
    {
        public List<ChoicesItem> choices { get; set; }

        public int created { get; set; }

        public string id { get; set; }

        public string model { get; set; }

        [Newtonsoft.Json.JsonProperty("object")]
        public string z_object { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public Usage usage { get; set; }

        public class Usage
        {
            /// <summary>
            /// 
            /// </summary>
            public int completion_tokens { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public int prompt_tokens { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public int total_tokens { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public Prompt_tokens_details prompt_tokens_details { get; set; }
        }

        public class Prompt_tokens_details
        {
            /// <summary>
            /// 
            /// </summary>
            public int cached_tokens { get; set; }
        }

        public class Message
        {
            /// <summary>
            /// 
            /// </summary>
            public string content { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string role { get; set; }
        }

        public class ChoicesItem
        {
            /// <summary>
            /// 
            /// </summary>
            public string finish_reason { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public int index { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public string logprobs { get; set; }
            /// <summary>
            /// 
            /// </summary>
            public Message message { get; set; }
        }
    }

    public class DoubaoChatCompletionsResponseWrap<T> where T : class, new()
    {
        public bool IsSuccess { get; set; }

        public T Data { get; set; }

        public string Message { get; set; }
    }

    public class DoubaoYwContentDto
    {
        public string Content { get; set; }
    }
}
