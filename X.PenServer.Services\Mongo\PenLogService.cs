﻿// -- Function: PenLogService.cs
// --- Project: X.PenServer.Services
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2023/10/24 18:01

namespace X.PenServer.Services.Mongo;

/// <inheritdoc cref="IPenLogService" />
public class PenLogService : MongoAutoService<PenLog>, IPenLogService
{
	/// <inheritdoc />
	public PenLogService(IMongoConfig config) : base(config)
	{
	}

	/// <inheritdoc />
	protected override void CreateIndex(IMongoCollection<PenLog> collection)
	{
		var userid_builder = Builders<PenLog>.IndexKeys
			.Ascending(x => x.UserId)
			.Ascending(x => x.PageId)
			.Ascending(x => x.Page)
			.Ascending(x => x.AddTime)
			.Ascending(x => x.Mac);
		collection.Indexes.CreateIndex(userid_builder, collection.CollectionNamespace.CollectionName + "_UserId_Key");

		var mac_builder = Builders<PenLog>.IndexKeys
			.Ascending(x => x.Mac)
			.Ascending(x => x.UserId)
			.Ascending(x => x.PageId)
			.Ascending(x => x.Page)
			.Ascending(x => x.AddTime);

		collection.Indexes.CreateIndex(mac_builder, collection.CollectionNamespace.CollectionName + "_Mac_Key");
	}
}