{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Information", "Microsoft.AspNetCore.Hosting": "Information", "Microsoft.AspNetCore.Routing.EndpointMiddleware": "Information"}}, "AllowedHosts": "*", "Database": "Server=127.0.0.1;Database=YouwoEduPlatfrom;User ID=sa;Password=***********;TrustServerCertificate=True;MultipleActiveResultSets=True;", "RedisOption": {"Host": "develop.eduwon.cn", "Port": 6379, "Password": "***********", "DatabaseIndex": 5}, "PenConfig": {"PenServerPort": 6003, "IsAutoAll": false, "StandBy": 30, "IsFix": false, "Firmwares": [{"ModelCode": "DotPen", "BinCode": "42RC 2023-10-30 10:25:34 60WPS", "FileName": "20231030_OTA.bin"}, {"ModelCode": "VoicePen", "BinCode": "62RC 2024-04-03 18:08:21 61WPS-O-US", "FileName": "20240403_OTA.bin"}]}, "KestrelConfig": {"KestrelOptions": [{"Port": 5009, "Protocol": "Http2"}, {"Port": 5010, "Protocol": "Http1AndHttp2"}]}, "SsoConfig": {"Issuer": "x.lucifer", "Audience": "x", "SigningKey": "xssssssssssssssssss and the day i met you"}, "RabbitConfig": {"Host": "develop.eduwon.cn", "Port": 5672, "UserName": "guest", "Password": "<PERSON><PERSON><PERSON>@123!@#", "Heartbeat": 20, "VirtualHost": "/"}, "ObsOption": {"AccessKey": "HPUAOQMESUS0ILVEMYMY", "SecretKey": "fHCnZ9JELFifmU9WZaEka4GoH1UERsskxr586qA7", "Endpoint": "https://dotpenvoice.obs.cn-east-2.myhuaweicloud.com"}, "MongoConfig": {"ConnectionString": "*****************************************************************", "DatabaseName": "PenMongo"}}