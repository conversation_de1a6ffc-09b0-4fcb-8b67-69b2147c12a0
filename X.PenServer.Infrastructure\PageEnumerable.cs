﻿// -- Function: PageEnumerable.cs
// --- Project: X.PenServer.Infrastructure
// ---- Remark: 
// ---- Author: Lucifer
// ------ Date: 2023/10/24 16:44

// Re<PERSON><PERSON>per disable UnusedAutoPropertyAccessor.Global
namespace X.PenServer.Infrastructure;

/// <summary>
/// 分页数据
/// </summary>
/// <typeparam name="T">数据实体</typeparam>
public class PageEnumerable<T>
{
    /// <summary>
    /// 总条数
    /// </summary>
    public long Total { get; set; }

    /// <summary>
    /// 数据列表
    /// </summary>
    public IEnumerable<T> Result { get; set; }

    /// <summary>
    /// 当前页码,默认第一页
    /// </summary>
    public int PageIndex { get; set; } = 1;

    /// <summary>
    /// 每页条数,默认十条
    /// </summary>
    public int PageSize { get; set; } = 10;
}