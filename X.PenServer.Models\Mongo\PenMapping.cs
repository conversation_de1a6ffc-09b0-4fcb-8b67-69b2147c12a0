﻿// -- Function: PenMapping.cs
// --- Project: X.PenServer.Models
// ---- Remark: 
// ---- Author: Lucifer
// ------ Date: 2023/10/24 15:17

using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace X.PenServer.Models.Mongo;

using System.Text.Json.Serialization;

/// <summary>
/// 点阵笔绑定用户数据
/// </summary>
public class PenMapping : MongoBaseModel
{
    /// <summary>
    /// 用户id
    /// </summary>
    [BsonElement(nameof(UserId))]
    [BsonRepresentation(BsonType.String)]
    [JsonPropertyName(nameof(UserId))]
    [JsonInclude]
    public string UserId { get; set; }

    /// <summary>
    /// 点阵笔Mac地址
    /// </summary>
    [BsonElement(nameof(Mac))]
    [BsonRepresentation(BsonType.String)]
    [JsonPropertyName(nameof(Mac))]
    [JsonInclude]
    public string Mac { get; set; }

    /// <summary>
    /// 连接状态
    /// </summary>
    [BsonElement(nameof(State))]
    [BsonRepresentation(BsonType.Boolean)]
    [JsonPropertyName(nameof(State))]
    [JsonInclude]
    public bool State { get; set; }

    /// <summary>
    /// 用户类型: 0.学生 1.教师
    /// </summary>
    [BsonElement(nameof(UserType))]
    [BsonRepresentation(BsonType.Int32)]
    [JsonPropertyName(nameof(UserType))]
    [JsonInclude]
    public int UserType { get; set; }

    /// <summary>
    /// 电量: 0 ~ 10
    /// </summary>
    [BsonElement(nameof(Battery))]
    [BsonRepresentation(BsonType.Int32)]
    [JsonPropertyName(nameof(Battery))]
    [JsonInclude]
    public int Battery { get; set; }

    /// <summary>
    /// 配网热点名称
    /// </summary>
    [BsonElement(nameof(Ssid))]
    [BsonRepresentation(BsonType.String)]
    [JsonPropertyName(nameof(Ssid))]
    [JsonInclude]
    public string Ssid { get; set; }

    /// <summary>
    /// 固件版本号
    /// </summary>
    [BsonElement(nameof(Version))]
    [BsonRepresentation(BsonType.String)]
    [JsonPropertyName(nameof(Version))]
    [JsonInclude]
    public string Version { get; set; }
}