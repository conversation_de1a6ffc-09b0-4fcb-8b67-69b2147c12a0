<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:schemaLocation="NLog"
      autoReload="true"
      throwExceptions="false" keepVariablesOnReload="true" internalLogIncludeTimestamp="true">
    <extensions>
        <add assembly="NLog.Web.AspNetCore" />
    </extensions>
    <targets>
        <default-wrapper xsi:type="AsyncWrapper" overflowAction="Block" />
        <target name="asynclogfile" xsi:type="AsyncWrapper">
            <target xsi:type="File" name="logfile" fileName="nlog/${level}/nlog-${shortdate}.log"
                    lineEnding="CR"
                    encoding="utf-8"
                    maxArchiveFiles="30"
                    maxArchiveDays="30"
                    keepFileOpen="true"
                    concurrentWrites="true"
                    layout="${longdate}|${level}|${message} |${all-event-properties} ${exception:format=tostring}"/>
        </target>
        <target xsi:type="ColoredConsole" name="logconsole"
                encoding="utf-8"
                useDefaultRowHighlightingRules="false"
                layout="${longdate}|${level}: ${message} |${all-event-properties} ${exception:format=tostring}${newline}">
            <highlight-row condition="level == LogLevel.Trace" foregroundColor="DarkGray" />
            <highlight-row condition="level == LogLevel.Debug" foregroundColor="Gray" />
            <highlight-row condition="level == LogLevel.Info" foregroundColor="White" />
            <highlight-row condition="level == LogLevel.Warn" foregroundColor="Yellow" />
            <highlight-row condition="level == LogLevel.Error" foregroundColor="Red" />
            <highlight-row condition="level == LogLevel.Fatal" foregroundColor="Magenta" backgroundColor="White" />
        </target>
    </targets>
    <rules>
        <logger name="*" minlevel="Info" writeTo="asynclogfile,logconsole"/>
    </rules>
</nlog>
