﻿using X.PenServer.Interfaces.Lifecycle;

namespace Uwoo.Util.Doubao
{
    /// <summary>
    /// <para><AUTHOR>
    /// <para>@Date   2024-12-24 11:42</para>
    /// <para>@Description Canvas Service</para>
    /// </summary>
    public interface ICanvasService : ITransientService
    {
        /// <summary>
        /// Upload capture to OBS after draw by handwriting
        /// </summary>
        /// <param name="dots"></param>
        /// <param name="markPoints"></param>
        /// <returns></returns>
        DrawUploadObsResponse DrawCaptureUploadObsByHandwriting(List<DotDto> dots, List<MarkPointsDto> markPoints);

        /// <summary>
        /// Upload capture to OBS after drawing the img | handwriting
        /// </summary>
        /// <param name="imgUrl"></param>
        /// <param name="dots"></param>
        /// <param name="markPoints"></param>
        /// <returns></returns>
        Task<DrawUploadObsResponse> DrawCaptureUploadObsByHandwritingForSubjectiveAsync(string imgUrl, List<DotDto> dots, List<MarkPointsDto> markPoints);
    }
}
