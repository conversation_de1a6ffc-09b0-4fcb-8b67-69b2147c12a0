﻿// -- Function: MongoAutoAsyncService.cs
// --- Project: X.PenServer.Services
// ---- Remark: 
// ---- Author: Lucifer
// ------ Date: 2023/10/24 16:14

namespace X.PenServer.Services.Mongo;

public abstract partial class MongoAutoService<T>
{
    private readonly IMongoDatabase _database;

    protected MongoAutoService(IMongoConfig config)
    {
        var client = new MongoClient(config.ConnectionString);
        _database = client.GetDatabase(config.DatabaseName);
    }

    /// <summary>
    /// 创建索引
    /// </summary>
    /// <param name="collection"></param>
    protected abstract void CreateIndex(IMongoCollection<T> collection);

    /// <summary>
    /// 获取数据集合
    /// </summary>
    /// <param name="colname"></param>
    /// <returns></returns>
    protected virtual IMongoCollection<T> GetConnection(string colname = "")
    {
        var year = GetCurrentYear();
        var xcolname = $"{typeof(T).Name}_{year}";
        if (!string.IsNullOrWhiteSpace(colname))
        {
            xcolname = $"{typeof(T).Name}_{year}_{colname}";
        }

        var collection = _database.GetCollection<T>(xcolname);
        CreateIndex(collection);
        return collection;
    }

    /// <summary>
    /// 获取当前学期年份
    /// </summary>
    /// <remarks>升学年自动切换学期年份,自动拆分学期数据</remarks>
    /// <returns></returns>
    private static int GetCurrentYear()
    {
        var date = DateTime.Today;
        return (date.Month < 9 || (date.Month==8 && date.Day>=28)) ? date.Year - 1 : date.Year;
    }

    #region Implementation of IMongoAutoAsyncService<T>

    /// <inheritdoc />
    public virtual async Task<T> GetAsync(long id, string colname = "")
    {
        var mongo = GetConnection(colname);
        return await mongo.Find(x => x.Mid.Equals(id)).Limit(1).FirstOrDefaultAsync();
    }

    /// <inheritdoc />
    public virtual async Task<T> GetAsync(Expression<Func<T, bool>> predicate = null, string colname = "")
    {
        Expression<Func<T, bool>> xpredicate = exp => true;
        predicate ??= xpredicate;
        var mongo = GetConnection(colname);
        return await mongo.Find(predicate).Limit(1).FirstOrDefaultAsync();
    }

    /// <inheritdoc />
    public virtual async Task<List<T>> GetAllAsync(Expression<Func<T, bool>> predicate = null, string colname = "", FindOptions<T> options = null)
    {
        Expression<Func<T, bool>> xpredicate = exp => true;
        predicate ??= xpredicate;
        var mongo = GetConnection(colname);
        var list = await mongo.FindAsync(predicate, options);
        return await list.ToListAsync();
    }

    /// <inheritdoc />
    public virtual async Task<PageEnumerable<T>> GetPagedListAsync(Expression<Func<T, bool>> predicate = null,
        string colname = "", Expression<Func<T, object>> order = null, int page = 1,
        int pagesize = 10, bool isasc = false)
    {
        var mongo = GetConnection(colname);
        Expression<Func<T, bool>> xpredicate = exp => true;
        predicate ??= xpredicate;
        var total = await mongo.CountDocumentsAsync(predicate);
        var list = mongo.Find(predicate);
        list = order != null ? isasc ? list.SortBy(order) : list.SortByDescending(order) : list.SortBy(x => x.Mid);
        list = list.Limit(pagesize).Skip((page - 1) * pagesize);
        var xlist = await list.ToListAsync();
        return new PageEnumerable<T>
        {
            Result = xlist,
            Total = total,
            PageIndex = page,
            PageSize = pagesize
        };
    }

    /// <inheritdoc />
    public virtual async Task AddAsync(T entity, string colname = "")
    {
        var mongo = GetConnection(colname);
        await mongo.InsertOneAsync(entity);
    }

    /// <inheritdoc />
    public virtual async Task AddManyAsync(IEnumerable<T> entities, string colname = "")
    {
        var mongo = GetConnection(colname);
        await mongo.InsertManyAsync(entities);
    }

    /// <inheritdoc />
    public virtual async Task<bool> UpdateAsync(Expression<Func<T, bool>> predicate, T entity, string colname = "")
    {
        var mongo = GetConnection(colname);
        var result = await mongo.ReplaceOneAsync(predicate, entity);
        return result.ModifiedCount > 0;
    }

    /// <inheritdoc />
    public virtual async Task<bool> UpdateAsync(long id, T entity, string colname = "")
    {
        var mongo = GetConnection(colname);
        var result = await mongo.ReplaceOneAsync(x => x.Mid.Equals(id), entity);
        return result.ModifiedCount > 0;
    }

    /// <inheritdoc />
    public virtual async Task<bool> DeleteAsync(long id, string colname = "")
    {
        var mongo = GetConnection(colname);
        var result = await mongo.DeleteOneAsync(x => x.Mid.Equals(id));
        return result.DeletedCount > 0;
    }

    /// <inheritdoc />
    public virtual async Task<bool> DeleteAsync(Expression<Func<T, bool>> predicate, string colname = "")
    {
        var mongo = GetConnection(colname);
        var result = await mongo.DeleteOneAsync(predicate);
        return result.DeletedCount > 0;
    }

    /// <inheritdoc />
    public virtual async Task<bool> DeleteManyAsync(Expression<Func<T, bool>> predicate, string colname = "")
    {
        var mongo = GetConnection(colname);
        var result = await mongo.DeleteManyAsync(predicate);
        return result.DeletedCount > 0;
    }

    #endregion
}