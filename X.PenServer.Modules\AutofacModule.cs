﻿using System.Reflection;
using Autofac;
using Microsoft.AspNetCore.Mvc.ApplicationParts;
using Microsoft.AspNetCore.Mvc.Controllers;
using X.PenServer.Interfaces.Lifecycle;
using Module = Autofac.Module;

namespace X.PenServer.Modules;

/// <inheritdoc />
public class AutofacModule : Module
{
    /// <inheritdoc />
    protected override void Load(ContainerBuilder builder)
    {
        var singleton = typeof(ISingletonService);
        var scoped = typeof(IScopedService);
        var transient = typeof(ITransientService);
        //注册当前公用服务接口
        var file = Path.Combine(AppContext.BaseDirectory, "X.PenServer.Services.dll");
        if (File.Exists(file))
        {
            var impl_service = Assembly.LoadFile(file);
            builder.RegisterAssemblyTypes(impl_service).Where(x => singleton.IsAssignableFrom(x) && x != singleton)
                .AsImplementedInterfaces()
                .SingleInstance()
                .PropertiesAutowired();

            builder.RegisterAssemblyTypes(impl_service).Where(x => scoped.IsAssignableFrom(x) && x != scoped)
                .AsImplementedInterfaces()
                .InstancePerLifetimeScope()
                .PropertiesAutowired();

            builder.RegisterAssemblyTypes(impl_service).Where(x => transient.IsAssignableFrom(x) && x != transient)
                .AsImplementedInterfaces()
                .InstancePerDependency()
                .PropertiesAutowired();
        }

        //注册当前上层应用接口
        var service = Assembly.GetEntryAssembly();
        if (service == null)
        {
            return;
        }

        builder.RegisterAssemblyTypes(service).Where(x => singleton.IsAssignableFrom(x) && x != singleton)
            .AsImplementedInterfaces()
            .SingleInstance()
            .PropertiesAutowired();

        builder.RegisterAssemblyTypes(service).Where(x => scoped.IsAssignableFrom(x) && x != scoped)
            .AsImplementedInterfaces()
            .InstancePerLifetimeScope()
            .PropertiesAutowired();

        builder.RegisterAssemblyTypes(service).Where(x => transient.IsAssignableFrom(x) && x != transient)
            .AsImplementedInterfaces()
            .InstancePerDependency()
            .PropertiesAutowired();

        //控制器注入
        var manager = new ApplicationPartManager
        {
            ApplicationParts =
            {
                new AssemblyPart(service)
            },
            FeatureProviders =
            {
                new ControllerFeatureProvider()
            }
        };
        var feature = new ControllerFeature();
        manager.PopulateFeature(feature);
        builder.RegisterTypes(feature.Controllers.Select(x => x.AsType()).ToArray())
            .AsImplementedInterfaces()
            .InstancePerLifetimeScope()
            .PropertiesAutowired();
    }
}