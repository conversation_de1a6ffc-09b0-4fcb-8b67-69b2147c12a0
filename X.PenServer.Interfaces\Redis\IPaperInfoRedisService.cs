﻿// -- Function: IPaperInfoRedisService.cs
// --- Project: X.PenServer.Interfaces
// ---- Remark: 
// ---- Author: Lucifer
// ------ Date: 2023/11/01 13:52

using X.Elegant.Redis.Infrastructure;
using X.PenServer.Contracts.Paper;
using X.PenServer.Contracts.Redis;
using X.PenServer.Interfaces.Lifecycle;

namespace X.PenServer.Interfaces.Redis;

/// <summary>
/// 试卷信息
/// </summary>
public interface IPaperInfoRedisService : IRedisService, ISingletonService
{
    /// <summary>
    /// 获取试卷子页面数据
    /// </summary>
    /// <param name="paperid">试卷id</param>
    /// <param name="pageno">页码</param>
    /// <returns></returns>
    Task<WorkbookPage> HGetPaperPageInfoAsync(string paperid, int pageno);

    /// <summary>
    /// 缓存试卷子页面数据
    /// </summary>
    /// <param name="paperid">试卷id</param>
    /// <param name="page">页码</param>
    /// <param name="workpage">试卷子页面</param>
    /// <returns></returns>
    Task HSetPaperPageInfoAsync(string paperid, int page, WorkbookPage workpage);

    /// <summary>
    /// 根据页码id获取试卷信息
    /// </summary>
    /// <param name="pageid">页码id</param>
    /// <returns></returns>
    Task<string> HGetPaperIdAsync(int pageid);

    /// <summary>
    /// 设置试卷缓存页信息
    /// </summary>
    /// <param name="pageid">页码id</param>
    /// <returns></returns>
    Task HSetPaperInfoCombinationAsync(int pageid);

    /// <summary>
    /// 获取试卷是否为组合页
    /// </summary>
    /// <param name="pageid">页码id</param>
    /// <returns></returns>
    Task<bool> HGetPaperInfoIsCombinationAsync(int pageid);

    /// <summary>
    /// 获取试卷组合页区域列表
    /// </summary>
    /// <param name="pageid">页码id</param>
    /// <returns></returns>
    Task<List<CombinationMarkRangeInfo>> SMemberCombinationMarkRangeInfoListAsync(int pageid);

    /// <summary>
    /// 设置试卷组合页区域列表
    /// </summary>
    /// <param name="pageid">页码id</param>
    /// <param name="range_infos">标记区域列表</param>
    /// <returns></returns>
    Task SAddCombinationMarkRangeInfoListAsync(int pageid,List<CombinationMarkRangeInfo> range_infos);

    /// <summary>
    /// 设置页码对应试卷信息
    /// </summary>
    /// <param name="pageid">页码id</param>
    /// <param name="paperid">试卷id</param>
    /// <returns></returns>
    Task HSetPaperIdAsync(int pageid, string paperid);

    /// <summary>
    /// 获取试卷页面列表
    /// </summary>
    /// <param name="paperid">试卷id</param>
    /// <returns></returns>
    Task<List<WorkbookPage>> HGetAllPaperPageListAsync(string paperid);

    /// <summary>
    /// 当前试卷是否包含语音题
    /// </summary>
    /// <param name="paperid">试卷id</param>
    /// <returns></returns>
    Task<bool> HGetPaperIdIsMediaAsync(string paperid);

    /// <summary>
    /// 获取题干标注坐标列表
    /// </summary>
    /// <param name="paperid">试卷id</param>
    /// <returns></returns>
    Task<List<WorkbookMarkInfo>> SMemberMarkInfoAsync(string paperid);

    /// <summary>
    /// 添加试卷题干框选标注信息
    /// </summary>
    /// <param name="paperid">试卷id</param>
    /// <param name="markinfos">题目标注信息</param>
    /// <returns></returns>
    Task SAddMarkInfoAsync(string paperid, List<WorkbookMarkInfo> markinfos);

    /// <summary>
    /// 获取单题模式试卷完成提交区域
    /// </summary>
    /// <param name="paperid">试卷id</param>
    /// <returns></returns>
    Task<List<WorkbookMarkInfo>> SMemberSingleFinishMarkInfoAsync(string paperid);

    /// <summary>
    /// 添加单题模式试卷完成提交区域
    /// </summary>
    /// <param name="paperid">试卷id</param>
    /// <param name="infos">完成提交区域</param>
    /// <returns></returns>
    Task SAddSingleFinishMarkInfoAsync(string paperid, List<WorkbookMarkInfo> infos);
}