{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Information"}}, "AllowedHosts": "*", "Database": "Server=***********;Database=YouwoEduPlatfrom;User ID=sa;Password=*************;TrustServerCertificate=True;MultipleActiveResultSets=True;", "RedisOption": {"Host": "************", "Port": 6379, "Password": "uwoo!@#uwoo!@#1", "DatabaseIndex": 5}, "Kestrel": {"Endpoints": {"Http": {"Url": "http://*:6002", "Protocols": "Http1AndHttp2AndHttp3"}}}, "RabbitConfig": {"Host": "*************", "Port": 5672, "UserName": "uwoo", "Password": "uwoo@123", "Heartbeat": 20, "VirtualHost": "/"}, "MongoConfig": {"ConnectionString": "mongodb://Lucifer:Uwoo%40123!%40#@*************:27017/?directConnection=true", "DatabaseName": "PenMongo"}}