﻿// -- Function: MongoConfig.cs
// --- Project: X.PenServer.Infrastructure
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2023/10/24 16:51

using System.Text.Json.Serialization;

namespace X.PenServer.Contracts.Config;

/// <inheritdoc />
public class MongoConfig : IMongoConfig
{
    /// <inheritdoc />
    [JsonInclude]
    [JsonPropertyName(nameof(ConnectionString))]
    public string ConnectionString { get; set; }

    /// <inheritdoc />
    [JsonInclude]
    [JsonPropertyName(nameof(DatabaseName))]
    public string DatabaseName { get; set; }
}