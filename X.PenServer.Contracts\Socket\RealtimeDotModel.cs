﻿// Author: Lucifer
//   Date: 2023/09/18 15:44
//   Name: RealtimeDotModel.cs
// Remark:

using System.Text.Json.Serialization;
using X.PenServer.Contracts.Queue;

namespace X.PenServer.Contracts.Socket;

public class RealtimeDotModel
{
	/// <summary>
	/// 试卷id
	/// </summary>
	[JsonPropertyName(nameof(PaperId))]
	[JsonInclude]
	public string PaperId { get; set; }

	/// <summary>
	/// 实际采集到的页码id
	/// </summary>
	/// <remarks>明鼎实际返回的页码id</remarks>
	[JsonPropertyName(nameof(Page))]
	[JsonInclude]
	public int Page { get; set; }

	/// <summary>
	/// 业务页码id
	/// </summary>
	/// <remarks>除去卷码纸,其余情况都一致</remarks>
	[JsonPropertyName(nameof(PageId))]
	[JsonInclude]
	public int PageId { get; set; }

	/// <summary>
	/// 用户id
	/// </summary>
	/// <remarks>
	///	1.当UserType = 0, 代表学生id<br />
	/// 2.当UserType = 1, 代表被批阅学生id
	/// </remarks>
	[JsonPropertyName(nameof(UserId))]
	[JsonInclude]
	public string UserId { get; set; }

	/// <summary>
	/// 用户类型
	/// </summary>
	/// <remarks>0:当前用户为学生 1.当前用户为被批阅的学生</remarks>
	[JsonPropertyName("userType")]
	[JsonInclude]
	public int UserType { get; set; }

	/// <summary>
	/// 教师用户id
	/// </summary>
	[JsonPropertyName(nameof(TeacherUserId))]
	[JsonInclude]
	public string TeacherUserId { get; set; }

	/// <summary>
	/// 模式:<br />
	/// 0.默认<br />
	/// 1.教师批阅学生(常规批阅模式)<br />
	/// 2.学生批阅学生(学生互评模式)
	/// </summary>
	[JsonPropertyName(nameof(Mode))]
	[JsonInclude]
	public int Mode { get; set; } = 0;

	/// <summary>
	/// 学生班级id
	/// </summary>
	[JsonPropertyName(nameof(ClassId))]
	[JsonInclude]
	public string ClassId { get; set; }

	/// <summary>
	/// 点位
	/// </summary>
	[JsonPropertyName(nameof(Dots))]
	[JsonInclude]
	public List<PenDot> Dots { get; set; }

	/// <summary>
	/// 当前页面为第几页
	/// </summary>
	[JsonPropertyName(nameof(CurrentPageNo))]
	[JsonInclude]
	public int CurrentPageNo { get; set; }

	/// <summary>
	/// 笔迹类型，0，作答；1 订正
	/// </summary>
	[JsonPropertyName(nameof(Type))]
	[JsonInclude]
	public int Type { get; set; }

}