﻿//   -- Function: RealtimeBatteryConsumer.cs
//   --- Project: X.PenServer.Sockets
//   ---- Remark: 
//   ---- Author: Lucifer
//   ------ Date: 2023/09/19 16:14

using System.Collections.Immutable;
using System.Net.WebSockets;
using MassTransit;
using X.PenServer.Contracts.Realtime;
using X.PenServer.Contracts.Socket;
using static X.PenServer.Middlewares.MiddlewareExtensions;
using static X.PenServer.Middlewares.RealtimePenSocketMiddleware;

namespace X.PenServer.Sockets.Consumers;

using Infrastructure;

/// <summary>
/// 实时电量消费队列
/// </summary>

// ReSharper disable once ClassNeverInstantiated.Global
public class RealtimeBatteryConsumer : IConsumer<Batch<RealtimeBatteryModel>>
{
    private readonly ParallelOptions _parallel_options;

    public RealtimeBatteryConsumer(ParallelOptions parallel_options)
    {
        _parallel_options = parallel_options;
    }

    /// <inheritdoc />
    public async Task Consume(ConsumeContext<Batch<RealtimeBatteryModel>> context)
    {
        foreach (var item in context.Message)
        {
            var data = item.Message;
            var wids = GetClassClients(data.ClassId);
            if (wids is null || wids.IsEmpty)
            {
                continue;
            }

            var xdata = new SendData
            {
                Type = "204",
                ClassId = data.ClassId,
                Content = data.ToJsonString()
            };
            var jdata = xdata.ToJsonString();
            var sockets = ImmutableHashSet.Create<WebSocket>();
            sockets = wids.Select(GetSocketClient).Where(socket => socket is
            {
                State: WebSocketState.Open
            }).Aggregate(sockets, (current, socket) => current.Add(socket));
            if (sockets.Count <= 0)
            {
                continue;
            }

            await Parallel.ForEachAsync
            (
                sockets, _parallel_options, async (x, xtoken) => { await SendAsync(x, jdata, xtoken).ConfigureAwait(false); }
            );
        }

        await context.ConsumeCompleted.ConfigureAwait(false);
    }
}