﻿// -- Function: TeacherInfoService.cs
// --- Project: X.PenServer.Services
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2023/10/31 15:28

namespace X.PenServer.Services.Mongo;

/// <inheritdoc cref="ITeacherInfoService" />
public class TeacherInfoService : MongoService<TeacherInfo>, ITeacherInfoService
{
    /// <inheritdoc />
    public TeacherInfoService(IMongoConfig config) : base(config)
    {
    }

    /// <inheritdoc />
    protected override void CreateIndex(IMongoCollection<TeacherInfo> collection)
    {
        var builder = Builders<TeacherInfo>.IndexKeys
            .Ascending(x => x.UserId)
            .Ascending(x => x.UserName)
            .Ascending(x => x.RealName)
            .Ascending(x => x.SchoolId)
            .Ascending(x => x.Mobile);
        collection.Indexes.CreateIndex(builder, collection.CollectionNamespace.CollectionName);
    }

    /// <inheritdoc />
    public async Task<TeacherInfo> GetTeacherInfoAsync(string teacherid)
    {
        return await GetAsync(x => x.UserId.Equals(teacherid));
    }
}