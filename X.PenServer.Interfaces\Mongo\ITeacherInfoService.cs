﻿// -- Function: ITeacherInfoService.cs
// --- Project: X.PenServer.Interfaces
// ---- Remark: 
// ---- Author: Lucifer
// ------ Date: 2023/10/31 15:27

using X.PenServer.Models.Mongo;

namespace X.PenServer.Interfaces.Mongo;

using Lifecycle;

/// <summary>
/// 教师信息
/// </summary>
public interface ITeacherInfoService : IMongoService<TeacherInfo>, ISingletonService
{
    /// <summary>
    /// 获取教师信息
    /// </summary>
    /// <param name="teacherid">教师id</param>
    /// <returns></returns>
    Task<TeacherInfo> GetTeacherInfoAsync(string teacherid);
}