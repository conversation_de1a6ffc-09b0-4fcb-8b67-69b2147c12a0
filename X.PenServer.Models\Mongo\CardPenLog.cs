﻿// -- Function: CardPaperLog.cs
// --- Project: X.PenServer.Models
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2024/1/31 16:16
namespace X.PenServer.Models.Mongo;

using System.Text.Json.Serialization;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

/// <summary>
/// 答题卡笔迹
/// </summary>
public class CardPenLog : PenLog
{
	/// <summary>
	/// 试卷id
	/// </summary>
	[BsonElement(nameof(PaperId))]
	[BsonRepresentation(BsonType.String)]
	[JsonPropertyName(nameof(PaperId))]
	[JsonInclude]
	public string PaperId { get; set; }

	/// <summary>
	/// 题目编号
	/// </summary>
	[BsonElement(nameof(ItemNo))]
	[BsonRepresentation(BsonType.Int32)]
	[JsonPropertyName(nameof(ItemNo))]
	[JsonInclude]
	public int ItemNo { get; set; }

	/// <summary>
	/// 子题目编号
	/// </summary>
	[BsonElement(nameof(SubItemNo))]
	[BsonRepresentation(BsonType.Int32)]
	[JsonPropertyName(nameof(SubItemNo))]
	[JsonInclude]
	public int SubItemNo { get; set; }
}