﻿// -- Function: PredicateBuilder.cs
// --- Project: X.PenServer.Infrastructure
// ---- Remark: 
// ---- Author: Lucifer
// ------ Date: 2024/3/20 11:17
namespace X.PenServer.Infrastructure;

using System.Linq.Expressions;
using static System.Linq.Expressions.Expression;
using static ParameterExpressionVisitor;

/// <summary>
/// 条件扩展
/// </summary>
public static class PredicateBuilder
{
	public static Expression<Func<T, bool>> True<T>() { return x => true; }

	public static Expression<Func<T, bool>> False<T>() { return x => false; }

	private static Expression<T> Compose<T>(this Expression<T> first, Expression<T> second, Func<Expression, Expression, Expression> merge)
	{
		var map = first.Parameters.Select
		(
			(x, i) => new
			{
				x, s = second.Parameters[i]
			}
		).ToDictionary(x => x.s, z => z.x);
		var second_body = ReplaceParameters(map, second.Body);
		return Lambda<T>(merge(first.Body, second_body), first.Parameters);
	}

	public static Expression<Func<T, bool>> And<T>(this Expression<Func<T, bool>> first, Expression<Func<T, bool>> second)
	{
		return first.Compose(second, Expression.And);
	}

	public static Expression<Func<T, bool>> AndAlso<T>(this Expression<Func<T, bool>> first, Expression<Func<T, bool>> second)
	{
		return first.Compose(second, Expression.AndAlso);
	}

	public static Expression<Func<T, bool>> Or<T>(this Expression<Func<T, bool>> first, Expression<Func<T, bool>> second)
	{
		return first.Compose(second, Expression.Or);
	}

	public static Expression<Func<T, bool>> OrElse<T>(this Expression<Func<T, bool>> first, Expression<Func<T, bool>> second)
	{
		return first.Compose(second, Expression.OrElse);
	}
}