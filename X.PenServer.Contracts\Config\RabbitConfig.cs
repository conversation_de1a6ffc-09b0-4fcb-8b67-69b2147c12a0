﻿//  -- Function: RabbitConfig.cs
//  --- Project: PenServer
//  ---- Remark: 
//  ---- Author: Lucifer
//  ------ Date: 2023/05/19 14:56

using System.Text.Json.Serialization;

namespace X.PenServer.Contracts.Config;

/// <inheritdoc />
public class RabbitConfig : IRabbitConfig
{
    /// <inheritdoc />
    [JsonInclude]
    [JsonPropertyName(nameof(Host))]
    public string Host { get; set; }

    /// <inheritdoc />
    [JsonInclude]
    [JsonPropertyName(nameof(Port))]
    public ushort Port { get; set; }

    /// <inheritdoc />
    [JsonInclude]
    [JsonPropertyName(nameof(UserName))]
    public string UserName { get; set; }

    /// <inheritdoc />
    [JsonInclude]
    [JsonPropertyName(nameof(Password))]
    public string Password { get; set; }

    /// <inheritdoc />
    [JsonInclude]
    [JsonPropertyName(nameof(Heartbeat))]
    public int Heartbeat { get; set; } = 20;

    /// <inheritdoc />
    [JsonInclude]
    [JsonPropertyName(nameof(VirtualHost))]
    public string VirtualHost { get; set; } = "/";
}