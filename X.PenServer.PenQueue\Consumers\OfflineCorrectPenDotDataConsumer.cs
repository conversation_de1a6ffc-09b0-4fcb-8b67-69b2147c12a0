﻿// -- Function: OfflineCorrectPenDotDataConsumer.cs
// --- Project: X.PenServer.PenQueue
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2024/4/9 17:9

namespace X.PenServer.PenQueue.Consumers;
using Microsoft.Extensions.Logging;
using Infrastructure;
using MassTransit;
using Contracts.Queue;
using Interfaces;

/// <summary>
/// 离线批改
/// </summary>
// ReSharper disable once ClassNeverInstantiated.Global
public class OfflineCorrectPenDotDataConsumer : IConsumer<OfflineCorrectPenDotData>
{
	private readonly ILogger<OfflineCorrectPenDotDataConsumer> _log;
	private readonly IDotQueueService _service;

	public OfflineCorrectPenDotDataConsumer(ILogger<OfflineCorrectPenDotDataConsumer> log, IDotQueueService service)
	{
		_log = log;
		_service = service;
	}

	/// <inheritdoc />
	public async Task Consume(ConsumeContext<OfflineCorrectPenDotData> context)
	{
		var data = context.Message;
		_log.LogInformation
		(
			"[{Name}_Service]: {Data}", nameof(OfflineCorrectPenDotDataConsumer),
			data.ToJsonString()
		);
		await _service.ProcessOfflineAsync(data);
		await context.ConsumeCompleted;
	}
}