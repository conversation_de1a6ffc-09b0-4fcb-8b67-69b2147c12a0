﻿// -- Function: PaperMediaInfo.cs
// --- Project: X.PenServer.Contracts
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2024/3/11 9:11
namespace X.PenServer.Contracts.Paper;

/// <summary>
/// 题目媒体信息
/// </summary>
public class PaperMediaInfo
{
	/// <summary>
	/// Id
	/// </summary>
	public string Id { get; set; }

	/// <summary>
	/// 试卷id
	/// </summary>
	public string PaperId { get; set; }

	/// <summary>
	/// 题目id
	/// </summary>
	public string ItemId { get; set; }

	/// <summary>
	/// 文件url
	/// </summary>
	public string Url { get; set; }

	/// <summary>
	/// 添加时间
	/// </summary>
	public DateTime AddTime { get; set; }

	/// <summary>
	/// 更新时间
	/// </summary>
	public DateTime UpdateTime { get; set; }

	/// <summary>
	/// 学生id
	/// </summary>
	public string StudentId { get; set; }

	/// <summary>
	/// 教师id
	/// </summary>
	public string TeacherId { get; set; }

	/// <summary>
	/// 文件类型
	/// </summary>
	/// <remarks>
	/// 1: 题干<br/>
	/// 2: 学生作答录音<br/>
	/// 3: 教师点评录音<br/>
	/// </remarks>
	public int FileType { get; set; }

	/// <summary>
	/// 是否删除
	/// </summary>
	public bool IsDeleted { get; set; }
}