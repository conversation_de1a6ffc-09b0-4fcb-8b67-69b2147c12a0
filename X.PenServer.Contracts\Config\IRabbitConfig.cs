﻿// -- Function: IRabbitConfig.cs
// --- Project: X.PenServer.Interfaces
// ---- Remark: 
// ---- Author: Lucifer
// ------ Date: 2023/10/24 15:59

namespace X.PenServer.Contracts.Config;

/// <summary>
/// RabbitMQ配置
/// </summary>
public interface IRabbitConfig
{
    /// <summary>
    /// 主机地址
    /// </summary>
    public string Host { get; set; }

    /// <summary>
    /// 端口
    /// </summary>
    public ushort Port { get; set; }

    /// <summary>
    /// 用户名
    /// </summary>
    public string UserName { get; set; }

    /// <summary>
    /// 密码
    /// </summary>
    public string Password { get; set; }

    /// <summary>
    /// 心跳时间 , 以秒为单位
    /// </summary>
    public int Heartbeat { get; set; }

    /// <summary>
    /// 虚拟路径
    /// </summary>
    public string VirtualHost { get; set; }
}