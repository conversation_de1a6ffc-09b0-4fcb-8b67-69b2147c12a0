﻿using Microsoft.Extensions.Logging;
using StackExchange.Redis;
using X.Elegant.Redis;
using X.Elegant.Redis.Infrastructure;
using X.PenServer.Contracts;
using X.PenServer.Interfaces.Redis;

namespace X.PenServer.Services.Redis
{
    public class StudentAnswerPaperRedisService : RedisService, IStudentAnswerPaperRedisService
    {
        readonly ILogger<StudentAnswerPaperRedisService> _log;

        public StudentAnswerPaperRedisService(
            IRedisServiceFactory factory
            , ILogger<StudentAnswerPaperRedisService> log) : base(factory)
        {
            _log=log;
        }

        public async Task SetItemAsync(StudentAnswerPaperDto item)
        {
            if (string.IsNullOrWhiteSpace(item.StudentId)
                ||string.IsNullOrWhiteSpace(item.PaperId)
                ||item.PageId==0
                )
                return;

            var hashkey = RedisKeys.StudentStartAnswerPaper;
            var key = $"{item.PageId}|{item.StudentId}";

            if (!HExists(hash_key: hashkey, key: key))
            {
                await HSetAsync(
                   hash_key: hashkey,
                   key: key,
                   value: item.ToJsonString(),
                   flags: CommandFlags.FireAndForget
                );
            }
        }
    }
}
