﻿// -- Function: DapperContextService.cs
// --- Project: X.PenServer.Services
// ---- Remark: 
// ---- Author: Lucifer
// ------ Date: 2023/11/16 13:41
namespace X.PenServer.Services.Dapper;

using System.Data;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using Interfaces;

/// <inheritdoc />
public class DapperFactoryService : IDapperFactoryService
{
	private readonly IConfiguration _config;

	public DapperFactoryService(IConfiguration config)
	{
		_config = config;
	}

	/// <inheritdoc />
	public IDbConnection CreateConnection()
	{
		var sqlcfg = _config.GetSection("Database").Value;
		if (string.IsNullOrWhiteSpace(sqlcfg))
		{
			throw new Exception("sql connection strings is empty");
		}

		return new SqlConnection(sqlcfg);
	}
}