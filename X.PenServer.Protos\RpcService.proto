syntax = "proto3";
package X.PenServer.Protos;
import "google/protobuf/wrappers.proto";
import "google/protobuf/empty.proto";

/*点阵笔RPC服务*/
service RpcService{
  /*播放服务*/
  rpc PlayService(PlayRequest) returns (google.protobuf.Empty);
  /*录制服务*/
  rpc RecordService(RecordRequest) returns (google.protobuf.Empty);
}

/*媒体公用参数*/
message PenMediaRequest{
  /*Mac地址*/
  google.protobuf.StringValue Mac = 1;

  /*实际采集的页码*/
  google.protobuf.Int32Value Page = 2;

  /*明鼎对应业务页码id*/
  google.protobuf.Int32Value PageId = 3;

  /*当前用户类型: 0.学生 1.教师*/
  google.protobuf.Int32Value UserRole = 4;

  /*教师用户id*/
  google.protobuf.StringValue TeacherId = 5;

  /*学生用户id*/
  google.protobuf.StringValue StudentId = 6;

  /*试卷id*/
  google.protobuf.StringValue PaperId = 7;
}

/*播放请求消息*/
message PlayRequest{
  /*媒体公用请求*/
  PenMediaRequest BaseRequest = 1;
  /*文件url*/
  google.protobuf.StringValue Url = 2;
}

/*录制请求消息*/
message RecordRequest{
  /*媒体公用请求*/
  PenMediaRequest BaseRequest = 1;

  /*题目id*/
  google.protobuf.StringValue ItemId = 2;

  /*试卷页面id*/
  google.protobuf.StringValue WorkbookPageId = 3;
}