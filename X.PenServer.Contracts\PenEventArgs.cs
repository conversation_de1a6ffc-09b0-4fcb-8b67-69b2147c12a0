﻿//  -- Function: PenEventArgs.cs
//  --- Project: PenServer
//  ---- Remark:
//  ---- Author: Lucifer
//  ------ Date: 2023/04/11 11:11

using System.Text.Json.Serialization;

namespace X.PenServer.Contracts;

/// <summary>
/// 笔事件
/// </summary>
public class PenEventArgs : EventArgs
{
    /// <summary>
    /// 连接id
    /// </summary>
    [JsonPropertyName(nameof(Wid))]
    [JsonInclude]
    public string Wid { get; set; }

    /// <summary>
    /// MAC地址
    /// </summary>
    [JsonPropertyName(nameof(Mac))]
    [JsonInclude]
    public string Mac { get; set; }

    /// <summary>
    /// 已接收的数据
    /// </summary>
    [JsonPropertyName(nameof(ReceiveData))]
    [JsonInclude]
    public string ReceiveData { get; set; }

    /// <inheritdoc />
    public PenEventArgs(string wid, string mac, string data)
    {
        Wid = wid;
        Mac = mac;
        ReceiveData = data;
    }
}