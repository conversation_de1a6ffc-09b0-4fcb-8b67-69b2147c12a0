﻿// -- Function: PaperLogRedisService.cs
// --- Project: X.PenServer.Services
// ---- Remark: 
// ---- Author: Lucifer
// ------ Date: 2023/10/31 17:41

using StackExchange.Redis;
using X.Elegant.Redis;
using X.Elegant.Redis.Infrastructure;
using X.PenServer.Contracts.Paper;
using X.PenServer.Contracts.Queue;
using X.PenServer.Contracts.Redis;
using X.PenServer.Interfaces.Redis;

namespace X.PenServer.Services.Redis;

/// <inheritdoc cref="IPaperLogRedisService" />
public class PaperLogRedisService : RedisService, IPaperLogRedisService
{
    /// <inheritdoc />
    public PaperLogRedisService(IRedisServiceFactory factory) : base(factory)
    {
    }

    #region Overrides of RedisService

    /// <inheritdoc />
    protected override string Prefix => RedisKeys.PAPER_LOG + "|";

    #endregion

    #region Implementation of IPaperInfoRedisService

    /// <inheritdoc />
    public async Task<CurrentPaperId> GetCurrentPaperIdAsync(string classid)
    {
        var key = $"CurrentPaper|{classid}";
        return await GetAsync<CurrentPaperId>(key);
    }

    /// <inheritdoc />
    public async Task<CurrentPaperId> GetCurrentOfflinePaperIdAsync(string classid)
    {
        var key = $"CurrentPaper_Permanent|{classid}";
        return await GetAsync<CurrentPaperId>(key);
    }

    /// <inheritdoc />
    public async Task<WorkbookPage> GetStudentCurrentDoPaperPageInfoAsync(string userid)
    {
        var key = $"StudentCurrentWorkPage|{userid}";
        return await GetAsync<WorkbookPage>(key);
    }

    public async Task<WorkbookPageGroup> GetStudentCurrentDoPaperPageInfoFor8KAsync(string userid)
    {
        var key = $"StudentCurrentWorkPageFor8K|{userid}";
        return await GetAsync<WorkbookPageGroup>(key);
    }

    /// <inheritdoc />
    public async Task SetStudentCurrentDoPaperPageInfoAsync(string userid, WorkbookPage workpage)
    {
        var key = $"StudentCurrentWorkPage|{userid}";
        await SetAsync(key, workpage, TimeSpan.FromHours(2), flags: CommandFlags.FireAndForget);
    }

    public async Task SetStudentCurrentDoPaperPageInfoFor8KAsync(string userid, WorkbookPageGroup workpageGroup)
    {
        var key = $"StudentCurrentWorkPageFor8K|{userid}";
        await SetAsync(key, workpageGroup, TimeSpan.FromHours(2), flags: CommandFlags.FireAndForget);
    }

    /// <inheritdoc />
    public async Task SAddUserPaperNumAsync(string userid, List<PenDot> dots)
    {
        var key = $"PaperTempNum|{userid}";
        await SAddAsync(key, dots.AsEnumerable(), flags: CommandFlags.FireAndForget);
    }

    /// <inheritdoc />
    public async Task<List<PenDot>> SMemberUserPaperNumAsync(string userid)
    {
        var key = $"PaperTempNum|{userid}";
        var result = await SMembersAsync<PenDot>(key);
        return result.ToList();
    }

    /// <inheritdoc />
    public async Task SaveRecognitionNumberAsync(string userid, string paperno)
    {
        var key = $"RecognitionNumber|{userid}";
        await SetAsync(key, paperno, TimeSpan.FromHours(2), flags: CommandFlags.FireAndForget);
    }

    /// <inheritdoc />
    public async Task<int> GetRecognitionNumberAsync(string userid)
    {
        var key = $"RecognitionNumber|{userid}";
        var result = await GetStringAsync(key);
        if (string.IsNullOrWhiteSpace(result))
        {
            return -1;
        }

        var ispass = int.TryParse(result, out var num);
        if (ispass)
        {
            return num;
        }

        return -1;
    }

    /// <inheritdoc />
    public async Task DelUserPaperNumAsync(string userid)
    {
        var key = $"PaperTempNum|{userid}";
        await RemoveAsync(key, flags: CommandFlags.FireAndForget);
    }

    /// <inheritdoc />
    public async Task<string> HGetPaperNumAsync(string paperno)
    {
        const string key = "EduwonPaperNum";
        return await HGetStringAsync(key, paperno);
    }

    /// <inheritdoc />
    public async Task HSetPaperNumAsync(string paperno, string paperid)
    {
        const string key = "EduwonPaperNum";
        await HSetAsync(key, paperno, paperid, flags: CommandFlags.FireAndForget);
    }

    /// <inheritdoc />
    public async Task<bool> HGetCorrectAsync(string classid, string paperid)
    {
        const string key = "IsEachComment";
        var field = $"{classid}|{paperid}";
        return await HExistsAsync(key, field);
    }

    /// <inheritdoc />
    public async Task<CurrentPaperItem> GetCurrentPaperItemIdAsync(string classid, string paperid)
    {
        var key = $"CurrentPaper|{classid}|{paperid}";
        return await GetAsync<CurrentPaperItem>(key);
    }

    /// <inheritdoc />
    public async Task ExpirePaperNumAsync(string userid, TimeSpan time_span)
    {
        var key = $"PaperTempNum|{userid}";
        await ExpireAsync(key, time_span, flags: CommandFlags.FireAndForget);
    }

    /// <inheritdoc />
    public async Task<int> GetPaperAnswerModeAsync(string classid, string paperid)
    {
        var key = $"PaperAnswerMode|{classid}|{paperid}";
        var result = await GetAsync<int>(key);
        return result == 2 ? 2 : 1;
    }

    /// <inheritdoc />
    public async Task SetEraserStateAsync(string mac, string paperid, string itemno)
    {
        var key = $"EraserState|{mac}|{paperid}|{itemno}";
        await SetAsync(key, 1, TimeSpan.FromMinutes(10), flags: CommandFlags.FireAndForget);
    }

	/// <inheritdoc />
	public async Task SetSingleItemAnswerStateAsync(string mac,string paperid,string classId,string itemNo)
    {
        var hashkey = $"SingleItemAnswer|{classId}|{paperid}";
		await HSetAsync(hashkey, $"{mac}|{itemNo}", $"{itemNo}");
        await ExpireAsync(hashkey, TimeSpan.FromHours(2));
	}

	/// <inheritdoc />
	public async Task<bool> GetSingleItemAnswerStateAsync(string mac, string paperid, string classId, string itemno)
	{
		var hashkey = $"SingleItemAnswer|{classId}|{paperid}";
        return await HExistsAsync(hashkey, $"{mac}|{itemno}");
	}

	/// <inheritdoc />
	public async Task<bool> GetEraserStateAsync(string mac, string paperid, string itemno)
    {
        var key = $"EraserState|{mac}|{paperid}|{itemno}";
        return await ExistsAsync(key);
    }

    /// <inheritdoc />
    public async Task DeleteEraserStateAsync(string mac, string paperid, string itemno)
    {
        var key = $"EraserState|{mac}|{paperid}|{itemno}";
        await RemoveAsync(key, flags: CommandFlags.FireAndForget);
    }

    /// <inheritdoc />
    public async Task SetStudentCurrentCorrectPageNoAsync(string studentid, string paperid, int pageno)
    {
        var key = $"StudentCurrentCorrectPageNo|{studentid}|{paperid}";
        await SetAsync(key, pageno, TimeSpan.FromHours(2), flags: CommandFlags.FireAndForget);
    }

    /// <inheritdoc />
    public async Task<int> GetStudentCurrentCorrectPageNoAsync(string studentid, string paperid)
    {
        var key = $"StudentCurrentCorrectPageNo|{studentid}|{paperid}";
        return await GetAsync<int>(key);
    }

    #endregion
}