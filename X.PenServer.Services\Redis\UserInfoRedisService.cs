﻿using Microsoft.Extensions.Logging;
using X.Elegant.Redis;
using X.Elegant.Redis.Infrastructure;
using X.PenServer.Interfaces;
using X.PenServer.Interfaces.Redis;
using X.PenServer.Models.Mongo;

namespace X.PenServer.Services.Redis
{
    public class UserInfoRedisService : RedisService, IUserInfoRedisService
    {
        protected override string Prefix => RedisKeys.UserInfo + "|";

        readonly IDapperService _dapper_service;
        readonly ILogger<UserInfoRedisService> _log;

        public UserInfoRedisService(
            IRedisServiceFactory factory
            , IDapperService dapper_service
            , ILogger<UserInfoRedisService> log) : base(factory)
        {
            _dapper_service=dapper_service;

            _log=log;
        }

        public async Task<StudentInfo> GetStudentInfoAsync(string userId)
        {
            StudentInfo user = null;

            if (string.IsNullOrWhiteSpace(userId))
                goto Over;

            var key = $"StudentId|{userId}";
            user = await GetAsync<StudentInfo>(key);

            if (user == null)
            {
                const string sql = """
                           SELECT TOP (1)
                                  [Id] AS [UserId],
                                  [RealName],
                                  [UserName],
                                  [NickName],
                                  [SchoolId],
                                  [PhoneNum] AS [Mobile],
                                  [ClassId],
                                  [StudentNo]
                           FROM [dbo].[Exam_Student]
                           WHERE [Id] = @userid
                                 AND [Deleted] = 0;
                           """;
                user = await _dapper_service.QueryFirstAsync<StudentInfo>
                (
                    sql, new
                    {
                        userid = userId
                    }
                );

                if (user == null)
                {
                    _log.LogError("db未查询到学生信息： {}", userId);
                }
                else
                {
                    await SetAsync(key: key, value: user, span: new TimeSpan(1, 0, 0));
                }
            }

        Over:
            return user;
        }

        public async Task<StudentInfo> GetStudentInfoAsync(string classId, string studentNo)
        {
            StudentInfo user = null;

            if (string.IsNullOrWhiteSpace(classId)||string.IsNullOrWhiteSpace(studentNo))
                goto Over;

            var key = $"StudentId|{classId}|{studentNo}";
            user = await GetAsync<StudentInfo>(key);

            if (user == null)
            {
                const string sql = """
                           SELECT TOP (1)
                                  [Id] AS [UserId],
                                  [RealName],
                                  [UserName],
                                  [NickName],
                                  [SchoolId],
                                  [PhoneNum] AS [Mobile],
                                  [ClassId],
                                  [StudentNo]
                           FROM [dbo].[Exam_Student]
                           WHERE [StudentNo] = @studentno
                                 AND [ClassId] = @classid
                                 AND [Deleted] = 0;
                           """;
                user = await _dapper_service.QueryFirstAsync<StudentInfo>
                (
                    sql, new
                    {
                        classId,
                        studentNo
                    }
                );

                if (user == null)
                {
                    _log.LogError("db未查询到学生信息： {}-{}", classId, studentNo);
                }
                else
                {
                    await SetAsync(key: key, value: user, span: new TimeSpan(1, 0, 0));
                }
            }

        Over:
            return user;
        }
    }
}
