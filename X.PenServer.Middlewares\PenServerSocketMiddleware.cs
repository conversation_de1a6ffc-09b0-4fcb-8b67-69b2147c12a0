﻿// -- Function: PenServerSocketMiddleware.cs
// --- Project: X.PenServer.Middlewares
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2023/10/25 14:00

using Masa.BuildingBlocks.Data;

namespace X.PenServer.Middlewares;
using System.Collections.Concurrent;
using System.Net.WebSockets;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Contracts;
using Infrastructure;
using static MiddlewareExtensions;

// ReSharper disable once ClassNeverInstantiated.Global
/// <inheritdoc />
public class PenServerSocketMiddleware : IMiddleware
{
    public delegate Task NotifyEventHandler(object sender, PenEventArgs e);

    /// <summary>
    /// 笔更新事件通知
    /// </summary>
    public static event NotifyEventHandler OnNotify;

    /// <summary>
    /// 所有WebSocket客户端
    /// </summary>
    public static ConcurrentDictionary<string, WebSocket> AllSocketClients { get; } = new();

    /// <summary>
    /// 当前点阵笔所有的客户端
    /// </summary>
    public static ConcurrentDictionary<string, HashSet<string>> AllPenClients { get; } = new();

    private readonly ILogger<PenServerSocketMiddleware> _log;
    private readonly ISequentialGuidGenerator _guid_generator;

    public PenServerSocketMiddleware(ILogger<PenServerSocketMiddleware> log, ISequentialGuidGenerator guid_generator)
    {
        _log = log;
        _guid_generator = guid_generator;
    }

    #region Implementation of IMiddleware

    /// <inheritdoc />
    public async Task InvokeAsync(HttpContext context, RequestDelegate next)
    {
        if (!context.WebSockets.IsWebSocketRequest)
        {
            await next(context);
            return;
        }

        if (!context.Request.Path.Equals("/pensocket", StringComparison.OrdinalIgnoreCase))
        {
            await next(context);
            return;
        }

        var ismac = context.Request.Query.TryGetValue("mac", out var mac);
        if (!ismac || string.IsNullOrWhiteSpace(mac))
        {
            await next(context);
            return;
        }

        var xmac = mac.ToString().Trim().Replace(":", "").ToUpper();
        if (string.IsNullOrWhiteSpace(xmac) || xmac.Length != 12)
        {
            await next(context);
            return;
        }

        var socket = await context.WebSockets.AcceptWebSocketAsync
        (
            new WebSocketAcceptContext
            {
                KeepAliveInterval = TimeSpan.FromSeconds(60),
                DangerousEnableCompression = false
            }
        );
        var token = context.RequestAborted;
        var wid = _guid_generator.NewStringId();
        if (!AllSocketClients.ContainsKey(wid))
        {
            AllSocketClients.TryAdd(wid, socket);
        }
        else
        {
            AllSocketClients[wid] = socket;
        }

        if (!AllPenClients.TryGetValue(xmac, out var client))
        {
            AllPenClients.TryAdd
            (
                xmac, [wid]
            );
        }
        else
        {
            client.Add(wid);
        }

        try
        {
            while (!token.IsCancellationRequested)
            {
                if (socket.State != WebSocketState.Open && socket.State != WebSocketState.Connecting)
                {
                    break;
                }

                var response = await ReceiveAsync(socket, token);
                if (string.IsNullOrWhiteSpace(response))
                {
                    continue;
                }

                var xdata = response.ToEntity<ReceiveData>();
                if (xdata != null)
                {
                    switch (xdata.TypeId)
                    {
                        case 1:
                            var zdata = new PenSocketData
                            {
                                TypeId = PenDataType.HeartBeat,
                                Mac = xmac,
                                ClientId = wid,
                                Content = "pong"
                            };
                            await SendAsync(socket, zdata.ToJsonString(), token);
                            break;
                        case 2 when OnNotify != null:
                        {
                            var edata = new PenEventArgs(wid, mac, response);
                            await OnNotify(socket, edata);
                            break;
                        }
                    }
                }
            }

            AllSocketClients.TryRemove(wid);
            AllPenClients[xmac].Remove(wid);
        }
        catch (Exception e)
        {
            _log.LogCritical(e, "error: {error}", e.Message);
        }
        finally
        {
            if (socket.State != WebSocketState.Closed)
            {
                socket.Abort();
                socket.Dispose();
            }
        }
    }

    #endregion
}