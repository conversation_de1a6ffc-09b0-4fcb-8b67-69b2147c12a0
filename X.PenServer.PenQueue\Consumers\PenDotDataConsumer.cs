﻿// -- Function: PenDotDataConsumer.cs
// --- Project: X.PenServer.PenQueue
// ---- Remark: 
// ---- Author: Lucifer
// ------ Date: 2023/10/30 9:56

using MassTransit;
using Microsoft.Extensions.Logging;
using X.PenServer.Contracts.Queue;
using X.PenServer.Interfaces;

namespace X.PenServer.PenQueue.Consumers;

using Infrastructure;

/// <summary>
/// 点阵笔产生的所有点位消费队列
/// </summary>
/// <remarks>所有后续点阵笔相关逻辑都基于此消费队列中的点位数据</remarks>
// ReSharper disable once ClassNeverInstantiated.Global
public class PenDotDataConsumer : IConsumer<Batch<PenDotData>>
{
    private readonly ILogger<PenDotDataConsumer> _log;
    private readonly IDotQueueService _service;

    public PenDotDataConsumer(ILogger<PenDotDataConsumer> log, IDotQueueService service)
    {
        _log = log;
        _service = service;
    }

    /// <inheritdoc />
    public async Task Consume(ConsumeContext<Batch<PenDotData>> context)
    {
        foreach (var item in context.Message)
        {
            var pendata = item.Message;
            _log.LogInformation
          (
              "[{Name}_Service]: mac:{} pageid:{} page:{} pageNo:{} DataType:{}", nameof(PenDotDataConsumer)
              ,pendata.Mac,pendata.PageId,pendata.Page,pendata.PageNo,pendata.DataType
          );
            await _service.ProcessAsync(pendata);
        }
        await context.ConsumeCompleted;
    }
}