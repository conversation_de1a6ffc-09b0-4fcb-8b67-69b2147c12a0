﻿//  -- Function: PenBusiness.cs
//  --- Project: PenServer
//  ---- Remark: 
//  ---- Author: Lucifer
//  ------ Date: 2023/02/22 17:29

using X.PenServer.Interfaces.Redis;

namespace X.PenServer.Implement;
using Masa.BuildingBlocks.Data;
using MassTransit;
using Microsoft.Extensions.Logging;
using Contracts;
using Contracts.Queue;
using Contracts.Socket;
using Interfaces;
using Huawei.Cloud.OBS.Interfaces;
using Infrastructure;
using System.Security.Cryptography;

/// <inheritdoc />
public class PenBusiness : IPenBusiness
{
    private readonly ILogger<PenBusiness> _logger;
    private readonly ISequentialGuidGenerator _generator;
    private readonly IDapperService _dapper;
    private readonly IPublishEndpoint _bus;
    private readonly IObsService _obs_service;
    private readonly ICommonRedisService _redis_service;
    private readonly ISnowflakeGenerator _snowflake;

    public PenBusiness(ILogger<PenBusiness> logger, ISequentialGuidGenerator generator, IDapperService dapper, IPublishEndpoint bus, IObsService obs_service, ICommonRedisService redis_service, ISnowflakeGenerator snowflake)
    {
        _logger = logger;
        _generator = generator;
        _dapper = dapper;
        _bus = bus;
        _obs_service = obs_service;
        _redis_service = redis_service;
        _snowflake=snowflake;
    }

    #region Implementation of IPenBusiness

    /// <inheritdoc />
    public async Task AddPenLog(PenDotData data)
    {
        var rid = _generator.NewStringId();
        data.RequestId = rid;
        _logger.LogDebug("[{name}]: requestid: {requestid}, data: {data}", nameof(AddPenLog), rid, data.ToJsonString());
        await _bus.Publish(data).ConfigureAwait(false);
    }

    /// <inheritdoc />
    public async Task AddPenLogOffline(PenDotData data)
    {
        var rid = _generator.NewStringId();
        data.RequestId = rid;
        _logger.LogDebug("[{name}]: requestid: {requestid}, data: {data}", nameof(AddPenLogOffline), rid, data.ToJsonString());
        await _bus.Publish(data).ConfigureAwait(false);
    }

    /// <inheritdoc />
    public async Task<bool> UpdateConnectState(string mac, string type)
    {
        var rid = _generator.NewStringId();
        _logger.LogDebug("[{name}]: requestid: {rid}, mac: {mac} , type: {type}", nameof(UpdateConnectState), rid, mac, type);
        try
        {
            const string update_sql = """
                                      UPDATE [dbo].[MD_UserPenMapping]
                                      SET [state] = @state
                                      WHERE [PMac] = @pmac;
                                      """;
            var rs = await _dapper.ExecuteAsync
             (
                 update_sql, new
                 {
                     pmac = mac,
                     state = type
                 }
             );

            _logger.LogInformation("[UpdateConnectState]:mac:{} type:{} rs:{}", mac, type, rs);

            var xdata = await GetPenMapping(mac);
            if (xdata == null || string.IsNullOrWhiteSpace(xdata.ClassId))
            {
                return true;
            }

            var data = new RealtimePenStateModel
            {
                State = type == "1" ? 1 : 0,
                ClassId = xdata.ClassId,
                UserId = xdata.UserId,
                UserType = xdata.UserType
            };
            await _bus.Publish(data).ConfigureAwait(false);
            return true;
        }
        catch (Exception e)
        {
            //_logger.LogCritical(e, "{error}", e.Message);
            _logger.LogCritical("mac:{} error:{error}", mac, e.Message);
            return false;
        }
    }

    /// <inheritdoc />
    public async Task<bool> UpdateBatteryState(string mac, string battery)
    {
        var rid = _generator.NewStringId();
        _logger.LogDebug("[{name}]: requestid: {rid}, mac: {mac} , battery: {battery}", nameof(UpdateBatteryState), rid, mac, battery);
        try
        {
            const string update_sql = """
                                      UPDATE [dbo].[MD_UserPenMapping]
                                      SET [Battery] = @battery
                                      WHERE [PMac] = @pmac;
                                      """;
            await _dapper.ExecuteAsync
            (
                update_sql, new
                {
                    pmac = mac,
                    battery
                }
            );

            var xdata = await GetPenMapping(mac);
            if (xdata == null)
            {
                return true;
            }

            var data = new RealtimeBatteryModel
            {
                Battery = Convert.ToInt32(battery),
                ClassId = xdata.ClassId,
                UserId = xdata.UserId,
                UserType = xdata.UserType
            };
            await _bus.Publish(data).ConfigureAwait(false);
            return true;
        }
        catch (Exception e)
        {
            _logger.LogCritical(e, "{error}", e.Message);
            return false;
        }
    }

    /// <inheritdoc />
    public async Task<bool> UpdateVersion(string mac, string version)
    {
        try
        {
            const string sql = """
                               UPDATE [dbo].[MD_UserPenMapping]
                               SET [Version] = @version
                               WHERE [PMac] = @mac;
                               """;
            var result = await _dapper.ExecuteAsync
            (
                sql, new
                {
                    mac,
                    version
                }
            ).ConfigureAwait(false);
            return result > 0;
        }
        catch (Exception e)
        {
            _logger.LogCritical(e, "{error}", e.Message);
            return false;
        }
    }

    /// <inheritdoc />
    public async Task<bool> UpdateSsid(string mac, string ssid)
    {
        try
        {
            const string sql = """
                               UPDATE [dbo].[MD_UserPenMapping]
                               SET [Ssid] = @ssid
                               WHERE [PMac] = @mac;
                               """;
            var result = await _dapper.ExecuteAsync
            (
                sql, new
                {
                    mac,
                    ssid
                }
            ).ConfigureAwait(false);
            return result > 0;
        }
        catch (Exception e)
        {
            _logger.LogCritical(e, "{error}", e.Message);
            return false;
        }
    }

    /// <summary>
    /// 获取点阵笔绑定信息
    /// </summary>
    /// <param name="mac">编号</param>
    /// <returns></returns>
    private async Task<PenMapping> GetPenMapping(string mac)
    {
        try
        {
            const string sql = """
                               SELECT TOP (1)
                                      [a].[PMac] AS [Mac],
                                      CASE
                                          WHEN [a].[UserType] = 0 THEN
                                              [b].[ClassId]
                                          ELSE
                                              [c].[ClassId]
                                      END AS [ClassId],
                                      [a].[UserId],
                                      [a].[UserType],
                                      [a].[Battery],
                                      CASE
                                          WHEN LEN([a].[Ssid]) <= 0 THEN
                                              '-'
                                          ELSE
                                              [a].[Ssid]
                                      END AS [Ssid],
                                      [a].[CreateTime]
                               FROM [dbo].[MD_UserPenMapping] AS [a] WITH (NOLOCK)
                                   LEFT JOIN [dbo].[Exam_Student] AS [b] WITH (NOLOCK)
                                       ON [b].[Id] = [a].[UserId]
                                   LEFT JOIN [dbo].[Exam_TeacherSubject] AS [c] WITH (NOLOCK)
                                       ON [c].[UserId] = [a].[UserId]
                                          AND [c].[PMac] = [a].[PMac]
                               WHERE [a].[PMac] = @pmac
                                     AND [a].[Deleted] = 0
                                     AND
                                     (
                                         [b].[Deleted] = 0
                                         OR [c].[Id] IS NOT NULL
                                     )
                               ORDER BY [a].[CreateTime] DESC;
                               """;
            var result = await _dapper.QueryFirstAsync<PenMapping>(sql, new
            {
                pmac = mac
            }).ConfigureAwait(false);
            return result;
        }
        catch (Exception e)
        {
            _logger.LogCritical(e, "{error}", e.Message);
            return null;
        }
    }

    public async Task SyncUserLoginLogAsync(int userType, string userId)
    {
        try
        {
            var key = $"PenServerSyncUserLoginLog_{userId}";
            var rs = _redis_service.GetString(key);
            if (!string.IsNullOrWhiteSpace(rs))
            {
                return;
            }

            var sql = string.Empty;

            if (userType==0)
            {
                sql = """
                        INSERT INTO dbo.UserLoginInfoLog
                        (
                            Id,
                            UserId,
                            Platform,
                            CreateTime
                        )
                        VALUES
                        (@id, @userId, 'PenServer', @createTime);
                     """;

                var result = await _dapper.ExecuteAsync(sql, new
                {
                    id = _snowflake.NewStringId(),
                    userId = userId,
                    createTime = DateTime.Now
                });
            }
            else
            {
                sql = """
                     DECLARE @schoolId NVARCHAR(50) = N'';
                     DECLARE @areaId INT = 0;

                     SELECT @schoolId = SchoolId,
                            @areaId = AreaId
                     FROM dbo.Base_User
                     WHERE Id = @userId;

                     INSERT INTO dbo.Base_UserLoginInfo
                     (
                         Id,
                         UserId,
                         LoginTime,
                         SchoolId,
                         AreaId
                     )
                     VALUES
                     (   @id,
                         @userId,
                         @loginTime,
                         @schoolId,
                         @areaId
                         );
                     """;

                var result = await _dapper.ExecuteAsync(sql, new
                {
                    id = _snowflake.NewStringId(),
                    userId = userId,
                    loginTime = DateTime.Now
                });
            }

            DateTime now = DateTime.Now;
            DateTime endOfDay = new DateTime(
                now.Year, now.Month, now.Day, 23, 59, 59);
            TimeSpan difference = endOfDay - now;

            _redis_service.Set<string>(key, userId, difference);
        }
        catch (Exception e)
        {
            _logger.LogCritical(e, "{error}", e.Message);
        }
    }

    /// <inheritdoc />
    public async Task<List<PenFirmwareVersion>> GetVersionList()
    {
        var list = new List<PenFirmwareVersion>();
        try
        {
            var xlist = await _redis_service.GetAsync<List<PenFirmwareVersion>>("FirmwareVersions");
            if (xlist is { Count: > 0 })
            {
                return xlist;
            }

            const string sql = """
                               SELECT *
                               FROM [dbo].[PenFirmwareVersion];
                               """;
            var result = await _dapper.QueryListAsync<PenFirmwareVersion>(sql);
            list = result.ToList();
            await _redis_service.SetAsync("FirmwareVersions", list).ConfigureAwait(false);
            return list;
        }
        catch (Exception e)
        {
            _logger.LogCritical(e, "{error}", e.Message);
            return list;
        }
    }

    /// <inheritdoc />
    public async Task<PenFirmwareVersion> GetVersion(string rawver)
    {
        var xver = rawver.Trim();
        var list = await GetVersionList();
        var result = list.FirstOrDefault(x => x.RawVersion.Equals(xver, StringComparison.OrdinalIgnoreCase));
        return result;
    }

    /// <inheritdoc />
    public async Task PenRecord(PenRecordData data)
    {
        await _bus.Publish(data).ConfigureAwait(false);
    }

    /// <inheritdoc />
    public async Task<string> ObsUpload(byte[] filedata, string filename, string contenttype = "", string bucketname = "dotpenvoice")
    {
        try
        {
            if (filedata is not
                {
                    Length: > 0
                })
            {
                return null;
            }

            await using var bytes = new MemoryStream(filedata);
            var result = await _obs_service.PutObjectAsync(bucketname, filename, bytes, contenttype).ConfigureAwait(false);
            return result;
        }
        catch (Exception e)
        {
            _logger.LogCritical(e, "{error}", e.Message);
            return null;
        }
    }

    /// <inheritdoc />
    public async Task<string> ObsUpload(string filepath, string filename, string contenttype = "", string bucketname = "dotpenvoice")
    {
        try
        {
            if (string.IsNullOrWhiteSpace(filepath) || string.IsNullOrWhiteSpace(filename))
            {
                return null;
            }

            var result = await _obs_service.PutObjectAsync(bucketname, filename, filepath, contenttype).ConfigureAwait(false);
            return result;
        }
        catch (Exception e)
        {
            _logger.LogCritical(e, "{error}", e.Message);
            return null;
        }
    }

    /// <inheritdoc />
    public async Task PushTeacherOfflineDotsAsync(OfflineCorrectPenDotData pendata)
    {
        try
        {
            await _bus.Publish(pendata).ConfigureAwait(false);
        }
        catch (Exception e)
        {
            _logger.LogCritical(e, "{error}", e.Message);
        }
    }

    #endregion
}