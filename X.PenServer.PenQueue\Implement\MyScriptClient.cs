﻿// -- Function: MyScriptClient.cs
// --- Project: X.PenServer.PenQueue
// ---- Remark: 
// ---- Author: Lucifer
// ------ Date: 2023/11/10 18:01

namespace X.PenServer.PenQueue.Implement;

using System.Security.Cryptography;
using System.Text;
using Microsoft.Extensions.Logging;
using Contracts.Config;
using Contracts.MyScipt;
using Contracts.Queue;
using Infrastructure;
using Interfaces;

/// <inheritdoc />
public class MyScriptClient : IMyScriptClient
{
	private readonly ILogger<MyScriptClient> _log;
	private readonly IHttpClientFactory _client_factory;
	private readonly IMyScriptConfig _myscript_config;

	public MyScriptClient(ILogger<MyScriptClient> log, IHttpClientFactory client_factory, IMyScriptConfig myscript_config)
	{
		_log = log;
		_client_factory = client_factory;
		_myscript_config = myscript_config;
	}

	#region Implementation of IMyScriptClient

	/// <inheritdoc />
	public async Task<int> NumberRecognition(List<PenDot> dots)
	{
		try
		{
			if (dots is not { Count: > 0 })
			{
				return -1;
			}

			var txtcfg = new TextConfiguration
			{
				AddLkText = true,
				CustomResources = ["arabic_number_sk"]
			};

			//数据列表, 从1开始,遇2结束.
			var reqmodel = new MyScriptRequestModel
			{
				Ydpi = 200,
				Xdpi = 200,
				Height = 2338,
				Width = 1654,
				ContentType = MyScriptContentType.Text.GetEnumValue(),
				ConversionState = "DIGITAL_EDIT",
				Configuration = new Configuration
				{
					Lang = MyScriptLanguageType.English.GetEnumValue(),
					Text = new XText
					{
						Margin = new Margin
						{
							Top = 0,
							Left = 0,
							Right = 0
						},
						Eraser = new Eraser
						{
							ErasePrecisely = true
						},
						MimeTypes = ["text/plain"],
						Configuration = txtcfg
					}
				}
			};
			var diclist = new List<List<PenDot>>();
			var xylist = new List<PenDot>();
			foreach (var xitem in dots)
			{
				xylist.Add(xitem);
				if (xitem.Type != 2)
				{
					continue;
				}

				diclist.Add(xylist);
				xylist = [];
			}

			var strokegroup = new StrokeGroup
			{
				Strokes = []
			};
			foreach (var zitem in diclist)
			{
				var stroke = new Stroke
				{
					X = [],
					Y = [],
					T = []
				};
				foreach (var kitem in zitem)
				{
					stroke.X.Add(kitem.X);
					stroke.Y.Add(kitem.Y);
					stroke.T.Add(kitem.Time.ToMilliStamp());
				}

				strokegroup.Strokes.Add(stroke);
			}

			reqmodel.StrokeGroups.Add(strokegroup);
			var content = reqmodel.ToJsonString();
			var result = await GetResponse(content).ConfigureAwait(false);
			result = result
				.Replace(".", "0")
				.Replace(" ", "")
				.Replace("-", "2");
			var ispass = int.TryParse(result, out var xresult);
			return ispass ? xresult : -1;
		}
		catch (Exception e)
		{
			_log.LogCritical("{error}", e.Message);
			return -1;
		}
	}

	/// <summary>
	/// 计算HMAC512
	/// </summary>
	/// <param name="appkey">appkey</param>
	/// <param name="hmac">hmac</param>
	/// <param name="content">content</param>
	/// <returns></returns>
	private static string ComputeHMac(string appkey, string hmac, string content)
	{
		var xkey = $"{appkey}{hmac}";
		var key_bytes = Encoding.UTF8.GetBytes(xkey);
		var content_bytes = Encoding.UTF8.GetBytes(content);
		using var encrypt = new HMACSHA512(key_bytes);
		var result = encrypt.ComputeHash(content_bytes);
		return BitConverter.ToString(result).Replace("-", "").ToLower();
	}

	/// <summary>
	/// 获取结果
	/// </summary>
	/// <param name="content">请求内容</param>
	/// <returns></returns>
	private async Task<string> GetResponse(string content)
	{
		try
		{
			var xhmac = ComputeHMac(_myscript_config.AppKey, _myscript_config.HMac, content);
			using var client = _client_factory.CreateClient();
			using var request_msg = new HttpRequestMessage(HttpMethod.Post, _myscript_config.ApiUrl);
			request_msg.Headers.Add("applicationkey", _myscript_config.AppKey);
			request_msg.Headers.Add("hmac", xhmac);
			request_msg.Headers.Add("accept", "text/plain,application/x-latex,application/vnd.myscript.jiix");
			request_msg.Content = new StringContent(content, Encoding.UTF8, "application/json");
			using var response = await client.SendAsync(request_msg).ConfigureAwait(false);
			var result = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
			_log.LogInformation("识别数据: request: {RequestMsg} , response: {ResponseMsg}", content, result);
			return result;
		}
		catch (Exception e)
		{
			_log.LogCritical("识别异常:  request: {RequestMsg} , error: {ResponseMsg}", content, e.Message);
			return "";
		}
	}

	#endregion
}