﻿// -- Function: TestFixture.cs
// --- Project: X.PenServer.UnitTest
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2024/06/04 15:06

using System.Security.Cryptography;
using System.Text;
using System.Text.Encodings.Web;
using System.Text.Json;
using System.Text.Json.Serialization;
using Masa.BuildingBlocks.Data;
using Microsoft.AspNetCore.Server.Kestrel.Core;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NLog.Extensions.Logging;
using X.Elegant.Redis;
using X.Elegant.Redis.Infrastructure.Converters;
using X.PenServer.Contracts.Config;
using X.PenServer.Interfaces;
using X.PenServer.Interfaces.Redis;
using X.PenServer.Profiles;
using X.PenServer.Services.Dapper;
using X.PenServer.Services.Redis;
using Xunit.Microsoft.DependencyInjection;
using Xunit.Microsoft.DependencyInjection.Abstracts;

namespace X.PenServer.UnitTest;

/// <inheritdoc />
public class TestFixture : TestBedFixture
{
    #region Overrides of TestBedFixture

    protected override void AddServices(IServiceCollection services, IConfiguration config)
    {
        services.AddOptions();
        if (config == null)
        {
            return;
        }

        services.AddSingleton(config);
        Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
        Register(services);
        services.Configure<MongoConfig>(config.GetSection(nameof(MongoConfig)));
        services.Configure<RabbitConfig>(config.GetSection(nameof(RabbitConfig)));
        services.Configure<KestrelServerOptions>
        (
            x =>
            {
                x.Limits.MaxConcurrentUpgradedConnections = null;
                x.Limits.MaxConcurrentConnections = null;
            }
        );
        services.AddSingleton
        (
            new ParallelOptions
            {
                MaxDegreeOfParallelism = Environment.ProcessorCount - 1
            }
        );
        services.AddSingleton<IMongoConfig>(x => x.GetRequiredService<IOptions<MongoConfig>>().Value);
        services.AddSingleton<IRabbitConfig>(x => x.GetRequiredService<IOptions<RabbitConfig>>().Value);
        services.AddSingleton(x => x.GetRequiredService<IOptions<JsonSerializerOptions>>().Value);
        services.AddSequentialGuidGenerator(SequentialGuidType.SequentialAsBinary);
        var workerid = RandomNumberGenerator.GetInt32(1, 1023);
        Environment.SetEnvironmentVariable("WORKER_ID", workerid.ToString());
        services.AddSnowflake(x => { x.MaxCallBackTime = 1500; });
        //配置AutoMapper
        var automapper = typeof(ProfileBase).Assembly;
        services.AddAutoMapper(automapper);
        var json_options = new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true,
            PropertyNamingPolicy = null,
            DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
            NumberHandling = JsonNumberHandling.AllowReadingFromString,
            Encoder = JavaScriptEncoder.UnsafeRelaxedJsonEscaping,
            ReadCommentHandling = JsonCommentHandling.Skip,
            Converters =
            {
                new DateTimeJsonConverter()
            }
        };
        services.AddSingleton(json_options);
        services.AddElegantRedisService();
        services.AddLogging(x =>
        {
            x.ClearProviders();
            x.AddNLog(config);
        });
    }

    /// <inheritdoc />
    protected override IEnumerable<TestAppSettings> GetTestAppSettings()
    {
        yield return new TestAppSettings
        {
            Filename = "appsettings.json",
            IsOptional = false
        };
    }

    /// <inheritdoc />
    protected override ValueTask DisposeAsyncCore() => new();

    #endregion

    #region Register

    /// <summary>
    /// register
    /// </summary>
    /// <param name="services"></param>
    private static void Register(IServiceCollection services)
    {
        services.AddSingleton<ICommonRedisService, CommonRedisService>();
        services.AddSingleton<IPaperInfoRedisService, PaperInfoRedisService>();
        services.AddSingleton<IPaperLogRedisService, PaperLogRedisService>();
        services.AddSingleton<IPenMappingRedisService, PenMappingRedisService>();
        services.AddSingleton<ITeacherCorrectRedisService, TeacherCorrectRedisService>();
        services.AddSingleton<IDapperFactoryService, DapperFactoryService>();
        services.AddSingleton<IDapperService, DapperService>();
        services.AddSingleton(x => x.GetRequiredService<IOptions<JsonSerializerOptions>>().Value);
    }

    #endregion
}