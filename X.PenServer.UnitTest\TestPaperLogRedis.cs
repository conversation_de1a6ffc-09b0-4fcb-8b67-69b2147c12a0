﻿// -- Function: TestPaperLogRedis.cs
// --- Project: X.PenServer.UnitTest
// ---- Remark: 
// ---- Author: Lucifer
// ------ Date: 2024/06/04 15:06

using System.Security.Cryptography;
using Masa.BuildingBlocks.Data;
using X.PenServer.Interfaces.Redis;
using Xunit.Abstractions;
using Xunit.Microsoft.DependencyInjection.Abstracts;

namespace X.PenServer.UnitTest;

/// <inheritdoc />
public class TestPaperLogRedis : TestBed<TestFixture>
{
    private readonly IPaperLogRedisService _redis_service;
    private readonly ISnowflakeGenerator _snowflake_generator;

    /// <inheritdoc />
    public TestPaperLogRedis(ITestOutputHelper test_output_helper, TestFixture test_fixture) : base(test_output_helper, test_fixture)
    {
        _redis_service = test_fixture.GetService<IPaperLogRedisService>(test_output_helper);
        _snowflake_generator = test_fixture.GetService<ISnowflakeGenerator>(test_output_helper);
    }

    [Fact]
    public async Task MockAsync()
    {
        var paperid = "1782311486340456449";
        var classid = "1294831602592911360";
        await _redis_service.GetPaperAnswerModeAsync(classid, paperid);
        var studentid = _snowflake_generator.NewStringId();
        var pageno = RandomNumberGenerator.GetInt32(100);
        await _redis_service.SetStudentCurrentCorrectPageNoAsync(studentid, paperid, pageno);
        var result = await _redis_service.GetStudentCurrentCorrectPageNoAsync(studentid, paperid);
        Assert.Equal(pageno, result);
    }
}