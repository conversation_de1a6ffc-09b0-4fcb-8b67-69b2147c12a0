﻿// -- Function: MyScriptConfig.cs
// --- Project: X.PenServer.Contracts
// ---- Remark: 
// ---- Author: Lucifer
// ------ Date: 2023/11/13 9:56

using System.Text.Json.Serialization;

namespace X.PenServer.Contracts.Config;

/// <inheritdoc />
public class MyScriptConfig : IMyScriptConfig
{
	/// <inheritdoc />
	[JsonInclude]
	[JsonPropertyName(nameof(ApiUrl))]
	public string ApiUrl { get; set; }

	/// <inheritdoc />
	[JsonInclude]
	[JsonPropertyName(nameof(AppKey))]
	public string AppKey { get; set; }

	/// <inheritdoc />
	[JsonInclude]
	[JsonPropertyName(nameof(HMac))]
	public string HMac { get; set; }
}