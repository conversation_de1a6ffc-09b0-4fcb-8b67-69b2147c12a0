﻿// -- Function: Program.cs
// --- Project: X.PenServer.Sockets
// ---- Remark: 
// ---- Author: Lucifer
// ------ Date: 2023/10/27 13:13

using System.Text.Encodings.Web;
using System.Text.Json.Serialization;
using X.Elegant.Redis;
using X.Elegant.Redis.Infrastructure.Converters;

namespace X.PenServer.Sockets;
using System.Text;
using System.Text.Json;
using Autofac;
using Autofac.Extensions.DependencyInjection;
using Masa.BuildingBlocks.Data;
using MassTransit;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.AspNetCore.Server.Kestrel.Core;
using Microsoft.AspNetCore.WebSockets;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NLog;
using NLog.Web;
using Contracts.Config;
using Infrastructure;
using Middlewares;
using Modules;
using Consumers;

using System.Security.Cryptography;

public static class Program
{
    public static async Task Main(string[] args)
    {
        var nlog = Path.Combine(AppContext.BaseDirectory, "nlog.config");
        var log = LogManager.Setup().LoadConfigurationFromFile(nlog).GetCurrentClassLogger();
        try
        {
            Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
            var builder = WebApplication.CreateBuilder(args);
            builder.Services.AddCors
            (
                x =>
                {
                    x.AddDefaultPolicy
                    (
                        z => { z.AllowAnyHeader().AllowAnyMethod().AllowAnyOrigin(); }
                    );
                }
            );
            var config = builder.Configuration;
            builder.Services.AddOptions();
            builder.Services.Configure<MongoConfig>(config.GetSection(nameof(MongoConfig)));
            builder.Services.Configure<RabbitConfig>(config.GetSection(nameof(RabbitConfig)));
            builder.Services.Configure<KestrelServerOptions>
            (
                x =>
                {
                    x.Limits.MaxConcurrentUpgradedConnections = null;
                    x.Limits.MaxConcurrentConnections = null;
                }
            );
            builder.Services.AddSingleton
            (
                new ParallelOptions
                {
                    MaxDegreeOfParallelism = Environment.ProcessorCount - 1
                }
            );
            builder.Services.AddSingleton<IMongoConfig>(x => x.GetRequiredService<IOptions<MongoConfig>>().Value);
            builder.Services.AddSingleton<IRabbitConfig>(x => x.GetRequiredService<IOptions<RabbitConfig>>().Value);
            builder.Services.AddSingleton(x => x.GetRequiredService<IOptions<JsonSerializerOptions>>().Value);
            builder.Services.AddSequentialGuidGenerator(SequentialGuidType.SequentialAsBinary);
            var workerid = RandomNumberGenerator.GetInt32(1, 1023);
            Environment.SetEnvironmentVariable("WORKER_ID", workerid.ToString());
            builder.Services.AddSnowflake(x => { x.MaxCallBackTime = 1500; });
            builder.Services.Configure<ForwardedHeadersOptions>
            (
                x => { x.ForwardedHeaders = ForwardedHeaders.XForwardedFor | ForwardedHeaders.XForwardedProto; }
            );
            builder.Services.AddElegantRedisService(x =>
            {
                x.PropertyNameCaseInsensitive = true;
                x.PropertyNamingPolicy = null;
                x.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull;
                x.NumberHandling = JsonNumberHandling.AllowReadingFromString;
                x.Converters.Add(new DateTimeJsonConverter());
                x.Encoder = JavaScriptEncoder.UnsafeRelaxedJsonEscaping;
                x.ReadCommentHandling = JsonCommentHandling.Skip;
            });
            //替换默认控制器容器
            builder.Services.Replace(ServiceDescriptor.Scoped<IControllerActivator, ServiceBasedControllerActivator>());

            //添加事件总线和队列
            builder.Services.AddMassTransit
            (
                x =>
                {
                    var mqcfg = config.GetSection(nameof(RabbitConfig)).Get<RabbitConfig>();
                    x.SetKebabCaseEndpointNameFormatter();
                    x.AddDelayedMessageScheduler();
                    x.AddConsumer<RealtimeDotConsumer>();
                    x.AddConsumer<RealtimeWorkbookDotConsumer>();
                    x.AddConsumer<RealtimePenStateConsumer>();
                    x.AddConsumer<RealtimeBatteryConsumer>();
                    x.AddConsumer<RealtimeCorrectStudentConsumer>();
                    x.AddConsumer<StudentAnswerStateConsumer>();
                    x.AddConsumer<RealtimeEachCorrectConsumer>();
                    x.UsingRabbitMq
                    (
                        (
                            ctx,
                            cfg
                        ) =>
                        {
                            cfg.Host
                            (
                                mqcfg.Host, mqcfg.Port, mqcfg.VirtualHost, h =>
                                {
                                    h.Username(mqcfg.UserName);
                                    h.Password(mqcfg.Password);
                                    h.Heartbeat
                                    (
                                        mqcfg.Heartbeat > 0
                                            ? TimeSpan.FromSeconds(mqcfg.Heartbeat)
                                            : TimeSpan.Zero
                                    );
                                    h.ConfigureBatchPublish
                                    (
                                        c =>
                                        {
                                            c.Enabled = true;
                                        }
                                    );
                                    h.PublisherConfirmation = true;
                                }
                            );
                            cfg.PrefetchCount = 64;
                            cfg.PurgeOnStartup = false;
                            cfg.AutoDelete = false;
                            cfg.SingleActiveConsumer = false;
                            cfg.ConcurrentMessageLimit = 4096;
                            cfg.Durable = true;
                            cfg.AutoStart = true;
                            cfg.QueueExpiration = TimeSpan.FromDays(30);
                            cfg.OverrideDefaultBusEndpointQueueName("Lucifer");
                            cfg.UseDelayedRedelivery
                            (
                                z => { z.Interval(3, TimeSpan.FromSeconds(20)); }
                            );

                            //配置消费队列
                            cfg.ReceiveEndpoint
                            (
                                "X-Realtime-Dot", e => { e.ConfigureConsumer<RealtimeDotConsumer>(ctx); }
                            );
                            cfg.ReceiveEndpoint
                            (
                                "X-Realtime-Workbook-Dot", e => { e.ConfigureConsumer<RealtimeWorkbookDotConsumer>(ctx); }
                            );
                            cfg.ReceiveEndpoint
                            (
                                "X-Realtime-PenState",
                                e => { e.ConfigureConsumer<RealtimePenStateConsumer>(ctx); }
                            );
                            cfg.ReceiveEndpoint
                            (
                                "X-Realtime-PenBattery",
                                e => { e.ConfigureConsumer<RealtimeBatteryConsumer>(ctx); }
                            );
                            cfg.ReceiveEndpoint
                            (
                                "X-Realtime-Correct-StudentInfo",
                                e => { e.ConfigureConsumer<RealtimeCorrectStudentConsumer>(ctx); }
                            );
                            cfg.ReceiveEndpoint
                            (
                                "X-Realtime-Student-Answer-State",
                                e => { e.ConfigureConsumer<StudentAnswerStateConsumer>(ctx); }
                            );
                            cfg.ReceiveEndpoint
                            (
                                "X-Realtime-EachCorrect",
                                e => { e.ConfigureConsumer<RealtimeEachCorrectConsumer>(ctx); }
                            );
                        }
                    );
                }
            );

            //主机配置
            builder.Host
                .UseServiceProviderFactory(new AutofacServiceProviderFactory())
                .ConfigureLogging
                (
                    x => { x.ClearProviders(); }
                )
                .UseNLog()
                .UseWindowsService()
                .UseConsoleLifetime()
                .ConfigureContainer<ContainerBuilder>
                (
                    x =>
                    {
                        //注册公用模块
                        x.RegisterModule<AutofacModule>();
                    }
                );
            builder.WebHost.UseKestrel();
            builder.Services.AddControllers()
                .AddControllersAsServices();
            builder.Services.AddWebSockets
            (
                x =>
                {
                    x.AllowedOrigins.Add("*");
                    x.KeepAliveInterval = TimeSpan.FromSeconds(60);
                }
            ).AddRealtimePenSocket();
            var app = builder.Build();
            AutofacProvider.Container = app.Services.GetAutofacRoot();
            app.MapControllers();
            app.UseCors
            (
                x => { x.AllowAnyHeader().AllowAnyMethod().AllowAnyOrigin(); }
            );
            app.UseWebSockets().UseRealtimePenSocket();
            await app.RunAsync();
        }
        catch (Exception e)
        {
            log.Fatal(e);
        }
        finally
        {
            LogManager.Shutdown();
        }
    }
}