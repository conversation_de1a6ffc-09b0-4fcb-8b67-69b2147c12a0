﻿// -- Function: PenExtensions.cs
// --- Project: <PERSON>.PenServer
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2024/1/29 14:8

namespace X.PenServer.Extensions;

using AFPen.Public.ActStructure;

/// <summary>
/// 扩展
/// </summary>
public static class PenExtensions
{
	/// <summary>
	/// paper sizes
	/// </summary>
	public static IEnumerable<AFPaperSize> PaperSizes =>
		new AFPaperSize[]
		{
			new(1000, 1000000, 4961, 7016, 1),
			new(5, 6, 4961, 7016, 2),
			new(10, 10, 4961, 7016, 3),
			new(15, 15, 4229, 5953, 4),
			new(19, 20, 9211, 6377, 5),
			new(22, 23, 8597, 6070, 6),
			new(24, 25, 4298, 6070, 7),
			new(30, 69, 4229, 5953, 8)
		};

	/// <summary>
	/// all paper sizes
	/// </summary>
	public static List<AFPaperSize> AllPageSizes => [new AFPaperSize(1, 1000000, 9920, 7016, 0)];

	/// <summary>
	/// get real paper size & bookno
	/// </summary>
	/// <param name="page">page</param>
	/// <param name="bookno">bookno</param>
	/// <returns></returns>
	public static int GetRealPageId(int page, int bookno)
	{
		if (bookno <= 1)
		{
			return page;
		}

		var size = PaperSizes.ElementAt(bookno - 1);
		return size == null ? page : Convert.ToInt32(size.pageFrom - 1);
	}
}