<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>disable</Nullable>
    <ServerGarbageCollection>true</ServerGarbageCollection>
    <ConcurrentGarbageCollection>true</ConcurrentGarbageCollection>
    <ErrorOnDuplicatePublishOutputFiles>false</ErrorOnDuplicatePublishOutputFiles>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <AppendRuntimeIdentifierToOutputPath>false</AppendRuntimeIdentifierToOutputPath>
    <!--auto version start-->
    <Deterministic>false</Deterministic>
    <GenerateAssemblyFileVersionAttribute>false</GenerateAssemblyFileVersionAttribute>
    <GenerateAssemblyInformationalVersionAttribute>false</GenerateAssemblyInformationalVersionAttribute>
    <AssemblyVersion>1.0.*</AssemblyVersion>
    <!--auto version end-->
  </PropertyGroup>
  <PropertyGroup>
    <Copyright>Copyright © Lucifer. All rights reserved.</Copyright>
    <PackageId>X.PenServer.Sockets</PackageId>
    <PackageProjectUrl>https://github.com/X-Lucifer</PackageProjectUrl>
    <RepositoryUrl>https://github.com/X-Lucifer</RepositoryUrl>
    <Company>X Lucifer</Company>
    <Authors>Lucifer</Authors>
    <AutoIncrementPackageRevision>true</AutoIncrementPackageRevision>
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="..\X.PenServer.Contracts\X.PenServer.Contracts.csproj" />
    <ProjectReference Include="..\X.PenServer.Infrastructure\X.PenServer.Infrastructure.csproj" />
    <ProjectReference Include="..\X.PenServer.Interfaces\X.PenServer.Interfaces.csproj" />
    <ProjectReference Include="..\X.PenServer.Middlewares\X.PenServer.Middlewares.csproj" />
    <ProjectReference Include="..\X.PenServer.Models\X.PenServer.Models.csproj" />
    <ProjectReference Include="..\X.PenServer.Modules\X.PenServer.Modules.csproj" />
    <ProjectReference Include="..\X.PenServer.Profiles\X.PenServer.Profiles.csproj" />
  </ItemGroup>
  <ItemGroup>
    <None Update="nlog.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="appsettings.Development.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <DependentUpon>appsettings.json</DependentUpon>
    </None>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Autofac" Version="8.0.0" />
    <PackageReference Include="Autofac.Extensions.DependencyInjection" Version="9.0.0" />
    <PackageReference Include="Azure.Core" Version="1.41.0" />
    <PackageReference Include="Azure.Identity" Version="1.12.0" />
    <PackageReference Include="Dapper" Version="2.1.52" />
    <PackageReference Include="JetBrains.Annotations" Version="2024.2.0" />
    <PackageReference Include="MassTransit.RabbitMQ" Version="8.2.3" />
    <PackageReference Include="Microsoft.Bcl.AsyncInterfaces" Version="8.0.0" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="5.2.1" />
    <PackageReference Include="Microsoft.Extensions.Caching.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="8.0.2" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting.WindowsServices" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Localization" Version="8.0.7" />
    <PackageReference Include="Microsoft.Identity.Client.Extensions.Msal" Version="4.62.0" />
    <PackageReference Include="Microsoft.IdentityModel.Protocols" Version="8.0.1" />
    <PackageReference Include="Microsoft.IdentityModel.Protocols.OpenIdConnect" Version="8.0.1" />
    <PackageReference Include="Microsoft.NETCore.Platforms" Version="7.0.4" />
    <PackageReference Include="Microsoft.NETCore.Targets" Version="5.0.0" />
    <PackageReference Include="Nito.Disposables" Version="2.5.0" />
    <PackageReference Include="NLog.Web.AspNetCore" Version="5.3.11" />
    <PackageReference Include="System.Linq.Dynamic.Core" Version="1.4.3" />
    <PackageReference Include="System.Memory.Data" Version="8.0.0" />
    <PackageReference Include="System.Reflection.Emit" Version="4.7.0" />
    <PackageReference Include="System.Reflection.Emit.ILGeneration" Version="4.7.0" />
    <PackageReference Include="System.Reflection.Emit.Lightweight" Version="4.7.0" />
    <PackageReference Include="System.Reflection.TypeExtensions" Version="4.7.0" />
    <PackageReference Include="System.Runtime" Version="4.3.1" />
    <PackageReference Include="System.Runtime.Extensions" Version="4.3.1" />
    <PackageReference Include="System.ServiceProcess.ServiceController" Version="8.0.0" />
    <PackageReference Include="System.Text.Encoding.CodePages" Version="8.0.0" />
    <PackageReference Include="System.Text.Json" Version="8.0.4" />
    <PackageReference Include="System.Threading.Channels" Version="8.0.0" />
    <PackageReference Include="System.Windows.Extensions" Version="8.0.0" />
    <PackageReference Include="Volo.Abp.Core" Version="8.2.0" />
  </ItemGroup>
</Project>
