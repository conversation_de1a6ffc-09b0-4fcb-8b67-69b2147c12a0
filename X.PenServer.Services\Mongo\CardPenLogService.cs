﻿// -- Function: CardPenLogService.cs
// --- Project: X.PenServer.Services
// ---- Remark: 
// ---- Author: Lucifer
// ------ Date: 2024/1/31 16:19
namespace X.PenServer.Services.Mongo;

/// <inheritdoc cref="ICardPenLogService" />
public class CardPenLogService : MongoAutoService<CardPenLog>, ICardPenLogService
{
	/// <inheritdoc />
	public CardPenLogService(IMongoConfig config) : base(config)
	{
	}

	/// <inheritdoc />
	protected override void CreateIndex(IMongoCollection<CardPenLog> collection)
	{
		var userid_builder = Builders<CardPenLog>.IndexKeys
			.Ascending(x => x.UserId)
			.Ascending(x => x.PageId)
			.Ascending(x => x.ItemNo)
			.Ascending(x => x.PaperId)
			.Ascending(x => x.Mac)
			.Ascending(x => x.Page)
			.Ascending(x => x.AddTime);
		collection.Indexes.CreateIndex(userid_builder, collection.CollectionNamespace.CollectionName + "_UserId_Key");

		var pageid_builder = Builders<CardPenLog>.IndexKeys
			.Ascending(x => x.PageId)
			.Ascending(x => x.UserId)
			.Ascending(x => x.Mac)
			.Ascending(x => x.AddTime);
		collection.Indexes.CreateIndex(pageid_builder, collection.CollectionNamespace.CollectionName + "_PageId_Key");
	}
}