﻿// -- Function: WorkbookPenLog.cs
// --- Project: X.PenServer.Models
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2024/3/19 14:35

using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace X.PenServer.Models.Mongo;

using System.Text.Json.Serialization;

/// <summary>
/// 练习薄笔迹
/// </summary>
public class WorkbookPenLog : PenLog
{
	/// <summary>
	/// 试卷id
	/// </summary>
	[BsonElement(nameof(PaperId))]
	[BsonRepresentation(BsonType.String)]
	[JsonPropertyName(nameof(PaperId))]
	[JsonInclude]
	public string PaperId { get; set; }

	/// <summary>
	/// 题目id
	/// </summary>
	[BsonElement(nameof(ItemId))]
	[BsonRepresentation(BsonType.String)]
	[JsonPropertyName(nameof(ItemId))]
	[JsonInclude]
	public string ItemId { get; set; }

	/// <summary>
	/// 是否为整行
	/// </summary>
	[BsonElement(nameof(IsFullLine))]
	[BsonRepresentation(BsonType.Boolean)]
	[JsonPropertyName(nameof(IsFullLine))]
	[JsonInclude]
	public bool IsFullLine { get; set; }

	/// <summary>
	/// 行号
	/// </summary>
	[BsonElement(nameof(LineNo))]
	[BsonRepresentation(BsonType.Int32)]
	[JsonPropertyName(nameof(LineNo))]
	[JsonInclude]
	public int LineNo { get; set; }

	/// <summary>
	/// 当前空索引
	/// </summary>
	/// <remarks>非整行生效</remarks>
	[BsonElement(nameof(Blank))]
	[BsonRepresentation(BsonType.Int32)]
	[JsonPropertyName(nameof(Blank))]
	[JsonInclude]
	public int Blank { get; set; }
}