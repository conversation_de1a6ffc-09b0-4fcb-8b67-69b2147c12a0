﻿// -- Function: AutoPenRoleJob.cs
// --- Project: X.PenServer.Jobs
// ---- Remark: 
// ---- Author: Lucifer
// ------ Date: 2024/06/06 15:06

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Quartz;
using X.PenServer.Contracts;
using X.PenServer.Interfaces;
using X.PenServer.Interfaces.Redis;
using X.PenServer.Jobs.Implement;

namespace X.PenServer.Jobs.Schedulers;

public class AutoPenRoleJob : JobBase
{
    private readonly ILogger<AutoPenRoleJob> _log;
    private readonly IDapperService _dapper;
    private readonly ParallelOptions _parallel_options;
    private readonly IPenMappingRedisService _pen_mapping_redis_service;

    /// <inheritdoc />
    public AutoPenRoleJob(ILogger<AutoPenRoleJob> log, IDapperService dapper, ParallelOptions parallel_options, IPenMappingRedisService pen_mapping_redis_service , IConfiguration config)
    {
        _log = log;
        _dapper = dapper;
        _parallel_options = parallel_options;
        _pen_mapping_redis_service = pen_mapping_redis_service;

        var type = GetType();
        var time = DateTime.Today.AddHours(2.5);
        var is_immediate = config.GetValue<bool>("Immediate");
        if (is_immediate)
        {
            time = DateTime.Now;
        }
        JobDetail = JobBuilder.Create(type).WithIdentity(type.Name).Build();
        Trigger = TriggerBuilder.Create()
            .WithIdentity(type.Name)
            .StartAt(new DateTimeOffset(time))
            .WithSimpleSchedule
            (
                x => { x.WithInterval(TimeSpan.FromDays(15)).RepeatForever(); }
            )
            .Build();
        ScheduleJob = async scheduler =>
        {
            var is_exist = await scheduler.CheckExists(JobDetail.Key);
            if (!is_exist)
            {
                await scheduler.ScheduleJob(JobDetail, Trigger).ConfigureAwait(false);
            }
        };
    }

    #region Overrides of QuartzBackgroundWorkerBase

    /// <inheritdoc />
    public override async Task Execute(IJobExecutionContext context)
    {
        try
        {
            const string sql = """
                               SELECT [PMac] AS [Mac],
                                      [UserType]
                               FROM [dbo].[MD_UserPenMapping]
                               WHERE [Deleted] = 0;
                               """;
            var token = context.CancellationToken;
            var list = await _dapper.QueryListAsync<PenMapping>(sql);
            if (list == null || !list.Any())
            {
                return;
            }

            var xlist = list.AsEnumerable();
            _parallel_options.CancellationToken = token;
            await Parallel.ForEachAsync(xlist, _parallel_options, async (x, _) =>
            {
                await _pen_mapping_redis_service.HSetPenRoleAsync(x.Mac, x.UserType).ConfigureAwait(false);
                _log.LogInformation("[update pen role]: {mac} , {type}", x.Mac, x.UserType.ToString());
            });
            _log.LogInformation("update pen role finished");
        }
        catch (Exception e)
        {
            _log.LogCritical("error: {error}", e.StackTrace ?? e.Message);
        }
    }

    #endregion
}