﻿using X.PenServer.Interfaces.Lifecycle;
using static Uwoo.Util.Doubao.DoubaoChatCompletionsRequest;

namespace Uwoo.Util.Doubao
{
    /// <summary>
    /// <para><AUTHOR> <PERSON></para>
    /// <para>@Date   2024-12-20 9:30</para>
    /// <para>@Description Doubao Service</para>
    /// </summary>
    public interface IDoubaoChatCompletionsService : ITransientService
    {
        /// <summary>
        /// Ocr by the Doubao
        /// </summary>
        /// <param name="requestContents"></param>
        /// <returns></returns>
        Task<DoubaoChatCompletionsResponseWrap<DoubaoChatCompletionsResponse>> Ocr(List<ContentBase> requestContents);
    }
}
