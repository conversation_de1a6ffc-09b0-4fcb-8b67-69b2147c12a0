﻿// -- Function: PaperInfoRedisService.cs
// --- Project: X.PenServer.Services
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2023/11/01 13:52

using StackExchange.Redis;
using X.Elegant.Redis;
using X.Elegant.Redis.Infrastructure;
using X.PenServer.Contracts.Paper;
using X.PenServer.Contracts.Redis;
using X.PenServer.Interfaces.Redis;

namespace X.PenServer.Services.Redis;

/// <inheritdoc cref="IPaperInfoRedisService" />
public class PaperInfoRedisService : RedisService, IPaperInfoRedisService
{
    /// <inheritdoc />
    public PaperInfoRedisService(IRedisServiceFactory factory) : base(factory)
    {
    }

    #region Overrides of RedisService

    /// <inheritdoc />
    protected override string Prefix => RedisKeys.PAPER_INFO + "|";

    #endregion

    #region Implementation of IPaperInfoRedisService

    /// <inheritdoc />
    public async Task HSetPaperPageInfoAsync(string paperid, int page, WorkbookPage workpage)
    {
        var key = $"PaperPage|{paperid}";
        await HSetAsync(key, page.ToString(), workpage, flags: CommandFlags.FireAndForget);
    }

    /// <inheritdoc />
    public async Task<WorkbookPage> HGetPaperPageInfoAsync(string paperid, int pageno)
    {
        var key = $"PaperPage|{paperid}";
        return await HGetAsync<WorkbookPage>(key, pageno.ToString());
    }

    /// <inheritdoc />
    public async Task<string> HGetPaperIdAsync(int pageid)
    {
        var key = $"PaperPageId|{pageid.ToString()}";
        return await HGetStringAsync(key, pageid.ToString());
    }

    /// <inheritdoc />
    public async Task HSetPaperInfoCombinationAsync(int pageid)
    {
        const string key = "PaperPageIdCombination";
        await HSetAsync(key, pageid.ToString(), 1, flags: CommandFlags.FireAndForget);
    }

    /// <inheritdoc />
    public async Task<bool> HGetPaperInfoIsCombinationAsync(int pageid)
    {
        const string key = "PaperPageIdCombination";
        return await HExistsAsync(key, pageid.ToString());
    }

    /// <inheritdoc />
    public async Task<List<CombinationMarkRangeInfo>> SMemberCombinationMarkRangeInfoListAsync(int pageid)
    {
        var key = $"PaperPageIdCombinationRange|{pageid.ToString()}";
        var result = await SMembersAsync<CombinationMarkRangeInfo>(key);
        return result.ToList();
    }

    /// <inheritdoc />
    public async Task SAddCombinationMarkRangeInfoListAsync(int pageid, List<CombinationMarkRangeInfo> range_infos)
    {
        var key = $"PaperPageIdCombinationRange|{pageid.ToString()}";
        await SAddAsync(key, range_infos.AsEnumerable(), flags: CommandFlags.FireAndForget);
    }

    /// <inheritdoc />
    public async Task HSetPaperIdAsync(int pageid, string paperid)
    {
        var key = $"PaperPageId|{pageid.ToString()}";
        await HSetAsync(key, pageid.ToString(), paperid, flags: CommandFlags.FireAndForget);
    }

    /// <inheritdoc />
    public async Task<List<WorkbookPage>> HGetAllPaperPageListAsync(string paperid)
    {
        var key = $"PaperPage|{paperid}";
        var dic = await HGetAllAsync<WorkbookPage>(key);
        return dic.Select(item => item.Value).ToList();
    }

    /// <inheritdoc />
    public async Task<bool> HGetPaperIdIsMediaAsync(string paperid)
    {
        const string key = "PaperIsMedia";
        return await HExistsAsync(key, paperid);
    }

    /// <inheritdoc />
    public async Task<List<WorkbookMarkInfo>> SMemberMarkInfoAsync(string paperid)
    {
        var key = $"PaperItemMarkInfo|{paperid}";
        var result = await SMembersAsync<WorkbookMarkInfo>(key);
        return result.ToList();
    }

    /// <inheritdoc />
    public async Task SAddMarkInfoAsync(string paperid, List<WorkbookMarkInfo> markinfos)
    {
        var key = $"PaperItemMarkInfo|{paperid}";
        await SAddAsync(key, markinfos.AsEnumerable(), flags: CommandFlags.FireAndForget);
    }

    /// <inheritdoc />
    public async Task<List<WorkbookMarkInfo>> SMemberSingleFinishMarkInfoAsync(string paperid)
    {
        var key = $"SingleFinishMarkInfo|{paperid}";
        var list = await SMembersAsync<WorkbookMarkInfo>(key);
        return list.ToList();
    }

    /// <inheritdoc />
    public async Task SAddSingleFinishMarkInfoAsync(string paperid, List<WorkbookMarkInfo> infos)
    {
        var key = $"SingleFinishMarkInfo|{paperid}";
        await SAddAsync(key, infos.AsEnumerable(), flags: CommandFlags.FireAndForget);
    }

    #endregion
}