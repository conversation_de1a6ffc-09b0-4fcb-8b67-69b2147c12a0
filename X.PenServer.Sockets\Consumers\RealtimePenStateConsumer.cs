﻿//   -- Function: RealtimePenStateConsumer.cs
//   --- Project: X.PenServer.Sockets
//   ---- Remark: 
//   ---- Author: Lucifer
//   ------ Date: 2023/09/19 16:13

using System.Collections.Immutable;
using System.Net.WebSockets;
using MassTransit;
using X.PenServer.Contracts.Realtime;
using X.PenServer.Contracts.Socket;
using static X.PenServer.Middlewares.MiddlewareExtensions;
using static X.PenServer.Middlewares.RealtimePenSocketMiddleware;

namespace X.PenServer.Sockets.Consumers;

using Infrastructure;

/// <summary>
/// 实时连接状态消费队列
/// </summary>

// ReSharper disable once ClassNeverInstantiated.Global
public class RealtimePenStateConsumer : IConsumer<Batch<RealtimePenStateModel>>
{
    private readonly ParallelOptions _parallel_options;

    public RealtimePenStateConsumer(ParallelOptions parallel_options)
    {
        _parallel_options = parallel_options;
    }

    /// <inheritdoc />
    public async Task Consume(ConsumeContext<Batch<RealtimePenStateModel>> context)
    {
        foreach (var item in context.Message)
        {
            var data = item.Message;
            var wids = GetClassClients(data.ClassId);
            if (wids is null || wids.IsEmpty)
            {
                continue;
            }

            var xdata = new SendData
            {
                Type = "203",
                ClassId = data.ClassId,
                Content = data.ToJsonString()
            };
            var jdata = xdata.ToJsonString();
            var sockets = ImmutableHashSet.Create<WebSocket>();
            sockets = wids.Select(GetSocketClient).Where(socket => socket is
            {
                State: WebSocketState.Open
            }).Aggregate(sockets, (current, socket) => current.Add(socket));
            if (sockets.Count <= 0)
            {
                continue;
            }

            await Parallel.ForEachAsync
            (
                sockets, _parallel_options, async (x, xtoken) => { await SendAsync(x, jdata, xtoken).ConfigureAwait(false); }
            ).ConfigureAwait(false);
        }

        await context.ConsumeCompleted.ConfigureAwait(false);
    }
}