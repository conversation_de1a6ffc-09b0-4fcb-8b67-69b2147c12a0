﻿//  -- Function: IPenClientService.cs
//  --- Project: PenServer
//  ---- Remark:
//  ---- Author: Lucifer
//  ------ Date: 2023/02/22 17:23

using X.PenServer.Contracts;
using X.PenServer.Contracts.Queue;
using X.PenServer.Interfaces.Lifecycle;

namespace X.PenServer.Interfaces;

/// <summary>
/// 点阵笔相关业务逻辑
/// </summary>
public interface IPenBusiness : IScopedService
{
    /// <summary>
    /// 添加点阵笔数据
    /// </summary>
    /// <param name="data"></param>
    /// <returns></returns>
    Task AddPenLog(PenDotData data);

    /// <summary>
    /// 添加离线数据
    /// </summary>
    /// <param name="data"></param>
    /// <returns></returns>
    Task AddPenLogOffline(PenDotData data);

    /// <summary>
    /// 更新用户连接状态
    /// </summary>
    /// <param name="mac">mac</param>
    /// <param name="type">0.未连接 1.已连接</param>
    /// <returns></returns>
    Task<bool> UpdateConnectState(string mac, string type);

    /// <summary>
    /// 更新电池电量状态
    /// </summary>
    /// <param name="mac">mac</param>
    /// <param name="battery">0-10</param>
    /// <returns></returns>
    Task<bool> UpdateBatteryState(string mac, string battery);

    /// <summary>
    /// 更新笔的最新版本号
    /// </summary>
    /// <param name="mac"></param>
    /// <param name="version"></param>
    /// <returns></returns>
    Task<bool> UpdateVersion(string mac, string version);

    /// <summary>
    /// 更新笔当前配网地址
    /// </summary>
    /// <param name="mac">mac</param>
    /// <param name="ssid">ssid</param>
    /// <returns></returns>
    Task<bool> UpdateSsid(string mac, string ssid);

    /// <summary>
    /// 同步用户登录
    /// </summary>
    /// <param name="userType"></param>
    /// <param name="userId"></param>
    /// <returns></returns>
    Task SyncUserLoginLogAsync(int userType, string userId);

    /// <summary>
    /// 最新固件版本列表
    /// </summary>
    /// <returns></returns>
    Task<List<PenFirmwareVersion>> GetVersionList();

    /// <summary>
    /// 获取固件版本
    /// </summary>
    /// <param name="rawver">固件原始版本号</param>
    /// <returns></returns>
    Task<PenFirmwareVersion> GetVersion(string rawver);

    /// <summary>
    /// 录制数据
    /// </summary>
    /// <param name="data"></param>
    /// <returns></returns>
    Task PenRecord(PenRecordData data);

    /// <summary>
    /// 上传文件
    /// </summary>
    /// <param name="filedata">字节数据</param>
    /// <param name="filename">文件名</param>
    /// <param name="contenttype">文档类型</param>
    /// <param name="bucketname">桶名称</param>
    /// <returns></returns>
    Task<string> ObsUpload(byte[] filedata, string filename, string contenttype = "", string bucketname = "dotpenvoice");

    /// <summary>
    /// 上传文件
    /// </summary>
    /// <param name="filepath">文件路径</param>
    /// <param name="filename">文件名</param>
    /// <param name="contenttype">文档类型</param>
    /// <param name="bucketname">桶名称</param>
    /// <returns></returns>
    Task<string> ObsUpload(string filepath, string filename, string contenttype = "", string bucketname = "dotpenvoice");

    /// <summary>
    /// 推送教师离线点位
    /// </summary>
    /// <param name="pendata"></param>
    /// <returns></returns>
    Task PushTeacherOfflineDotsAsync(OfflineCorrectPenDotData pendata);
}