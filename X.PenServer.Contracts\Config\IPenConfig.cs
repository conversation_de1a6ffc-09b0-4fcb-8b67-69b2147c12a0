﻿// -- Function: IPenConfig.cs
//  --- Project: X.PenServer.Contracts
//  ---- Remark:
//  ---- Author: Lucifer
//  ------ Date: 2024/05/16 14:05

namespace X.PenServer.Contracts.Config;

/// <summary>
/// 笔配置
/// </summary>
public interface IPenConfig
{
    /// <summary>
    /// 点阵笔服务端口
    /// </summary>
    int PenServerPort { get; set; }

    /// <summary>
    /// 是否开启点阵笔自动更新
    /// </summary>
    bool IsAutoAll { get; set; }

    /// <summary>
    /// 休眠时间, 以分钟为单位
    /// </summary>
    int StandBy { get; set; }

    /// <summary>
    /// 固件列表
    /// </summary>
    List<Firmware> Firmwares { get; set; }

    /// <summary>
    /// 矫正点位
    /// </summary>
    bool IsFix { get; set; }
}