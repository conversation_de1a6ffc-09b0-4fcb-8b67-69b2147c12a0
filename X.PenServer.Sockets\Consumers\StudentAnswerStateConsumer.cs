﻿using System.Collections.Immutable;
using System.Net.WebSockets;
using MassTransit;
using X.PenServer.Contracts.Realtime;
using X.PenServer.Contracts.Socket;
using X.PenServer.Infrastructure;
using static X.PenServer.Middlewares.MiddlewareExtensions;
using static X.PenServer.Middlewares.RealtimePenSocketMiddleware;

namespace X.PenServer.Sockets.Consumers;

// ReSharper disable once ClassNeverInstantiated.Global
public class StudentAnswerStateConsumer : IConsumer<Batch<StudentAnswerStateModel>>
{
	private readonly ParallelOptions _parallel_options;

	public StudentAnswerStateConsumer(ParallelOptions parallel_options)
	{
		_parallel_options = parallel_options;
	}

	public async Task Consume(ConsumeContext<Batch<StudentAnswerStateModel>> context)
	{
		foreach (var item in context.Message)
		{
			var pendata = item.Message;

			//学生笔迹
			var wids = GetStudentClients(pendata.PaperId, pendata.UserId);
			if (wids is null || wids.IsEmpty)
			{
				continue;
			}

			var sockets = ImmutableHashSet.Create<WebSocket>();
			sockets = wids.Select(GetSocketClient).Where(socket => socket is
			{
				State: WebSocketState.Open
			}).Aggregate(sockets, (current, socket) => current.Add(socket));
			if (sockets.Count <= 0)
			{
				continue;
			}

			await Parallel.ForEachAsync
			(
				sockets, _parallel_options, async (x, xtoken) =>
				{
					var xdata = new SendData
					{
						Type = "206",
						Content = pendata.ToJsonString()
					};

					await SendAsync(x, xdata.ToJsonString(), xtoken).ConfigureAwait(false);
				}
			).ConfigureAwait(false);
		}

		await context.ConsumeCompleted.ConfigureAwait(false);
	}
}