﻿<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Publish X.PenServer.Services" type="DotNetFolderPublish" factoryName="Publish to folder" singleton="false">
    <riderPublish configuration="Release" delete_existing_files="true" platform="Any CPU" runtime="win-x64" target_folder="../Publish/Services" target_framework="net8.0" uuid_high="7524973445918968327" uuid_low="-5818257572065675474" />
    <method v="2" />
  </configuration>
</component>