﻿// -- Function: ITokenProviderService.cs
// --- Project: X.PenServer.Interfaces
// ---- Remark: 
// ---- Author: Lucifer
// ------ Date: 2024/3/6 9:44
namespace X.PenServer.Interfaces;

using Lifecycle;

/// <summary>
/// Token提供程序
/// </summary>
public interface ITokenProviderService : IScopedService
{
	/// <summary>
	/// 获取Token
	/// </summary>
	/// <returns></returns>
	Task<string> GetTokenAsync();
}