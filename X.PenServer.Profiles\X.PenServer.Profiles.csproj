<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>disable</Nullable>
        <ErrorOnDuplicatePublishOutputFiles>false</ErrorOnDuplicatePublishOutputFiles>
        <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
        <AppendRuntimeIdentifierToOutputPath>false</AppendRuntimeIdentifierToOutputPath>
        <!--auto version start-->
        <Deterministic>false</Deterministic>
        <GenerateAssemblyFileVersionAttribute>false</GenerateAssemblyFileVersionAttribute>
        <GenerateAssemblyInformationalVersionAttribute>false</GenerateAssemblyInformationalVersionAttribute>
        <AssemblyVersion>1.0.*</AssemblyVersion>
        <!--auto version end-->
    </PropertyGroup>
    <PropertyGroup>
        <Copyright>Copyright © Lucifer. All rights reserved.</Copyright>
        <PackageId>X.PenServer.Profiles</PackageId>
        <PackageProjectUrl>https://github.com/X-Lucifer</PackageProjectUrl>
        <RepositoryUrl>https://github.com/X-Lucifer</RepositoryUrl>
        <Company>X Lucifer</Company>
        <Authors>Lucifer</Authors>
    </PropertyGroup>
    <ItemGroup>
      <ProjectReference Include="..\X.PenServer.Contracts\X.PenServer.Contracts.csproj" />
      <ProjectReference Include="..\X.PenServer.Models\X.PenServer.Models.csproj" />
      <ProjectReference Include="..\X.PenServer.Protos\X.PenServer.Protos.csproj" />
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="AutoMapper" Version="13.0.1" />
      <PackageReference Include="Microsoft.Extensions.Options" Version="8.0.2" />
      <PackageReference Include="Microsoft.NETCore.Platforms" Version="7.0.4" />
    </ItemGroup>

</Project>
