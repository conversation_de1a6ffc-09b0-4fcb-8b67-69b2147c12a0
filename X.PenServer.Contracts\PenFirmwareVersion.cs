﻿//  -- Function: PenFirmwareVersion.cs
//  --- Project: PenServer
//  ---- Remark: 
//  ---- Author: Lucifer
//  ------ Date: 2023/03/31 15:43

using System.Text.Json.Serialization;

namespace X.PenServer.Contracts;

/// <summary>
/// 固件版本信息 
/// </summary>
public class PenFirmwareVersion
{
	/// <summary>
	/// Oid
	/// </summary>
	[JsonPropertyName(nameof(Oid))]
	[JsonInclude]
	public long Oid { get; set; }

	/// <summary>
	/// 点阵笔原始版本号
	/// </summary>
	[JsonPropertyName(nameof(RawVersion))]
	[JsonInclude]
	public string RawVersion { get; set; }

	/// <summary>
	/// 版本号
	/// </summary>
	[JsonPropertyName(nameof(Version))]
	[JsonInclude]
	public string Version { get; set; }

	/// <summary>
	/// 固件文件名
	/// </summary>
	[JsonPropertyName(nameof(BinName))]
	[JsonInclude]
	public string BinName { get; set; }

	/// <summary>
	/// 备注
	/// </summary>
	[JsonPropertyName(nameof(Remark))]
	[JsonInclude]
	public string Remark { get; set; }

	/// <summary>
	/// 是否最新版本
	/// </summary>
	[JsonPropertyName(nameof(IsLatest))]
	[JsonInclude]
	public bool IsLatest { get; set; }

	/// <summary>
	/// 是否置顶
	/// </summary>
	[JsonPropertyName(nameof(IsTop))]
	[JsonInclude]
	public bool IsTop { get; set; }

	/// <summary>
	/// 创建时间
	/// </summary>
	[JsonPropertyName(nameof(CreateTime))]
	[JsonInclude]
	public DateTime CreateTime { get; set; }

	/// <summary>
	/// 型号编码
	/// </summary>
	/// <remarks>
	/// dotpen: 点阵笔
	/// voicepen: 语音点阵笔
	/// </remarks>
	[JsonPropertyName(nameof(ModelCode))]
	[JsonInclude]
	public string ModelCode { get; set; }
}