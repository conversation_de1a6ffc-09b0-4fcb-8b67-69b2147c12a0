﻿//  -- Function: CorrectPenLogService.cs
//  --- Project: X.PenServer.Services
//  ---- Remark: 
//  ---- Author: Lucifer
//  ------ Date: 2023/10/25 01:30:50

namespace X.PenServer.Services.Mongo;

/// <inheritdoc cref="ICorrectPenLogService" />
public class CorrectPenLogService : MongoAutoService<CorrectPenLog>, ICorrectPenLogService
{
    /// <inheritdoc />
    public CorrectPenLogService(IMongoConfig config) : base(config)
    {
    }

	/// <inheritdoc />
	protected override void CreateIndex(IMongoCollection<CorrectPenLog> collection)
	{
		var userid_builder = Builders<CorrectPenLog>.IndexKeys
			.Ascending(x => x.UserId)
			.Ascending(x => x.PageId)
			.Ascending(x => x.Page)
			.Ascending(x => x.AddTime)
			.Ascending(x => x.Mac);
		collection.Indexes.CreateIndex(userid_builder, collection.CollectionNamespace.CollectionName + "_UserId_Key");

		var mac_builder = Builders<CorrectPenLog>.IndexKeys
			.Ascending(x => x.Mac)
			.Ascending(x => x.UserId)
			.Ascending(x => x.PageId)
			.Ascending(x => x.Page)
			.Ascending(x => x.AddTime);

		collection.Indexes.CreateIndex(mac_builder, collection.CollectionNamespace.CollectionName + "_Mac_Key");
	}
}