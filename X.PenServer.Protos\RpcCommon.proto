syntax = "proto3";
package X.PenServer.Protos;
import "google/protobuf/any.proto";
import "google/protobuf/wrappers.proto";

/*公共响应*/
message RpcResult{
  /*状态*/
  google.protobuf.Int32Value State = 1;
  /*子状态码*/
  google.protobuf.Int32Value SubCode = 2;
  /*消息*/
  google.protobuf.StringValue Message = 3;
  /*动态实体*/
  google.protobuf.Any Data = 4;
}

/*公共请求*/
message RpcRequest{
  /*动态参数*/
  map<string, google.protobuf.Any> ExtraData = 1;
}