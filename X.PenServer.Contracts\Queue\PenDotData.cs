﻿//  -- Function: PenDotData.cs
//  --- Project: PenServer
//  ---- Remark:
//  ---- Author: Lucifer
//  ------ Date: 2023/05/22 13:52

using System.Text.Json.Serialization;

namespace X.PenServer.Contracts.Queue;

public class PenDotData
{
	/// <summary>
	/// 请求id
	/// </summary>
	[JsonPropertyName(nameof(RequestId))]
	[JsonInclude]
	public string RequestId { get; set; } = "";

	/// <summary>
	/// 实际采集到的页码id
	/// </summary>
	/// <remarks>明鼎实际返回的页码id</remarks>
	[JsonPropertyName(nameof(Page))]
	[JsonInclude]
	public int Page { get; set; }

	/// <summary>
	/// 业务页码id
	/// </summary>
	/// <remarks>除去卷码纸,其余情况都一致</remarks>
	[JsonPropertyName(nameof(PageId))]
	[JsonInclude]
	public int PageId { get; set; }

	/// <summary>
	/// Mac
	/// </summary>
	[JsonPropertyName(nameof(Mac))]
	[JsonInclude]
	public string Mac { get; set; } = "";

	/// <summary>
	/// 时间
	/// </summary>
	[JsonPropertyName(nameof(Time))]
	[JsonInclude]
	public DateTime Time { get; set; }

	/// <summary>
	/// 点位数据
	/// </summary>
	[JsonPropertyName(nameof(Dots))]
	[JsonInclude]
	public List<PenDot> Dots { get; set; } = new();

	/// <summary>
	/// 用户id
	/// </summary>
	[JsonPropertyName(nameof(UserId))]
	[JsonInclude]
	public string UserId { get; set; }

	/// <summary>
	/// 数据类型: 1.在线 2.离线
	/// </summary>
	[JsonPropertyName(nameof(DataType))]
	[JsonInclude]
	public int DataType { get; set; }

	/// <summary>
	/// 顺序页码
	/// </summary>
	[JsonPropertyName(nameof(PageNo))]
	[JsonInclude]
	public int PageNo { get; set; }
}