﻿// -- Function: CommonRedisService.cs
//  --- Project: X.PenServer.Services
//  ---- Remark:
//  ---- Author: Lucifer
//  ------ Date: 2024/05/24 16:05

using X.Elegant.Redis;
using X.Elegant.Redis.Infrastructure;
using X.PenServer.Interfaces.Redis;

namespace X.PenServer.Services.Redis;

/// <inheritdoc cref="ICommonRedisService" />
public class CommonRedisService : RedisService, ICommonRedisService
{
    /// <inheritdoc />
    public CommonRedisService(IRedisServiceFactory factory) : base(factory)
    {
    }
}