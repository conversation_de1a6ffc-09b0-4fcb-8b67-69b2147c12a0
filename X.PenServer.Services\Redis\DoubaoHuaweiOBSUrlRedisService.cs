﻿using Microsoft.Extensions.Logging;
using X.Elegant.Redis;
using X.Elegant.Redis.Infrastructure;
using X.PenServer.Contracts;
using X.PenServer.Interfaces.Redis;

namespace X.PenServer.Services.Redis
{
    public class DoubaoHuaweiOBSUrlRedisService : RedisService, IDoubaoHuaweiOBSUrlRedisService
    {
        readonly ILogger<DoubaoHuaweiOBSUrlRedisService> _log;

        public DoubaoHuaweiOBSUrlRedisService(
            IRedisServiceFactory factory
            , ILogger<DoubaoHuaweiOBSUrlRedisService> log) : base(factory)
        {
            _log=log;
        }

        public void SetItems(List<string> objectNames)
        {
            var timeStamp = DateTimeOffset.Now.ToUnixTimeMilliseconds();
            var guid = Guid.NewGuid().ToString("N");

            var data = new DoubaoHuaweiOBSUrlDto
            {
                TimeStamp = timeStamp,
                Guid=guid,
                ObjectNames = objectNames
            };

            HSet(RedisKeys.DoubaoHuaweiOBSUrl,$"{timeStamp}|{guid}", value: data.ToJsonString());

        }
    }
}
