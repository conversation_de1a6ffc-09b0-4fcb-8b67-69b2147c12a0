﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace X.PenServer.Services.Mongo
{
    public class PenB5LogService : MongoAutoService<PenB5Log>, IPenB5LogService
    {
        /// <inheritdoc />
        public PenB5LogService(IMongoConfig config) : base(config)
        {
        }

        /// <inheritdoc />
        protected override void CreateIndex(IMongoCollection<PenB5Log> collection)
        {
            var userid_builder = Builders<PenB5Log>.IndexKeys
                .Ascending(x => x.UserId)
                .Ascending(x => x.PageId)
                .Ascending(x => x.Page)
                .Ascending(x => x.AddTime)
                .Ascending(x => x.Mac);
            collection.Indexes.CreateIndex(userid_builder, collection.CollectionNamespace.CollectionName + "_UserId_Key");

            var mac_builder = Builders<PenB5Log>.IndexKeys
                .Ascending(x => x.Mac)
                .Ascending(x => x.UserId)
                .Ascending(x => x.PageId)
                .Ascending(x => x.Page)
                .Ascending(x => x.AddTime);

            collection.Indexes.CreateIndex(mac_builder, collection.CollectionNamespace.CollectionName + "_Mac_Key");
        }
    }
}
