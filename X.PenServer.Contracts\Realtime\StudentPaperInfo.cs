﻿//   -- Function: StudentPaperInfo.cs
//   --- Project: Uwoo.PenSocket
//   ---- Remark: 
//   ---- Author: Lucifer
//   ------ Date: 2023/09/19 10:57

using System.Text.Json.Serialization;

namespace X.PenServer.Contracts.Realtime;

public class StudentPaperInfo
{
    /// <summary>
    /// 试卷id
    /// </summary>
    [JsonPropertyName(nameof(PaperId))]
    [JsonInclude]
    public string PaperId { get; set; }

    /// <summary>
    /// 页码id
    /// </summary>
    [JsonIgnore]
    public string PageId { get; private set; }

    /// <summary>
    /// 学生id
    /// </summary>
    [JsonIgnore]
    public string StudentId { get; private set; }

    private string _student_pageid;

    /// <summary>
    /// 学生和页码id
    /// </summary>
    [JsonPropertyName("PageId_StudentId")]
    [JsonInclude]
    public string StudentPageId
    {
        get => _student_pageid;
        private set
        {
            _student_pageid = value;
            if (string.IsNullOrWhiteSpace(_student_pageid))
            {
                return;
            }

            var tmp = _student_pageid.Split("|");
            if (tmp is not {Length: 2})
            {
                return;
            }

            PageId = tmp[0];
            StudentId = tmp[1];
        }
    }
}