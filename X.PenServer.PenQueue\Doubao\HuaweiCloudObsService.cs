﻿using System.Net.Http.Headers;
using System.Security.Cryptography;
using System.Text;
using static System.Convert;
using static System.DateTime;
using static System.Net.Http.HttpMethod;
using static System.Text.Encoding;

namespace Uwoo.Util.Obs
{
    public class HuaweiCloudObsService : IHuaweiCloudObsService
    {
        public static string AK = "HPUAOQMESUS0ILVEMYMY";
        public static string SK = "fHCnZ9JELFifmU9WZaEka4GoH1UERsskxr586qA7";
        public static string endpoint = "https://{0}.obs.cn-east-2.myhuaweicloud.com";

        private readonly IHttpClientFactory _client_factory;

        public HuaweiCloudObsService(IHttpClientFactory client_factory)
        {
            _client_factory = client_factory;
        }

        /// <inheritdoc />
        public async Task<string> PutObjectAsync(string bucketName, string objectName, string filePath, string contentType = null)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(bucketName) || string.IsNullOrWhiteSpace(objectName) || string.IsNullOrWhiteSpace(filePath) || !File.Exists(filePath))
                {
                    return null;
                }

                var date = UtcNow.ToString("R");
                var sign = GetPUTSign(bucketName, objectName, date, contentType);
                await using var stream = new FileStream(filePath, FileMode.Open, FileAccess.ReadWrite, FileShare.ReadWrite);
                stream.Seek(0, SeekOrigin.Begin);
                using var content = new StreamContent(stream);
                if (!string.IsNullOrEmpty(contentType))
                {
                    content.Headers.ContentType = MediaTypeHeaderValue.Parse(contentType);
                }

                var url = string.Format(endpoint, bucketName)+ $"/{objectName}";
                using var request_msg = new HttpRequestMessage(Put, url);
                request_msg.Content = content;
                request_msg.Method = Put;
                request_msg.Headers.Authorization = AuthenticationHeaderValue.Parse($"OBS {AK}:{sign}");
                request_msg.Headers.Add("Date", date);
                using var client = _client_factory.CreateClient();
                await client.SendAsync(request_msg);
                return url;
            }
            catch (Exception)
            {
                return null;
            }
        }

        /// <summary>
        /// 计算签名
        /// </summary>
        /// <param name="bucketName"></param>
        /// <param name="objectName"></param>
        /// <param name="date"></param>
        /// <param name="contentType"></param>
        /// <returns></returns>
        private static string GetPUTSign(string bucketName, string objectName, string date, string contentType = null)
        {
            var sb = new StringBuilder("PUT");
            sb.Append("\n\n");
            if (!string.IsNullOrWhiteSpace(contentType))
            {
                sb.Append($"{contentType}\n");
            }
            else
            {
                sb.Append("\n");
            }

            sb.Append($"{date}\n");
            if (!bucketName.StartsWith("/"))
            {
                sb.Append($"/{bucketName}/");
            }
            else
            {
                sb.Append($"{bucketName}/");
            }

            sb.Append(objectName);
            var sign = HmacEncrypt(SK, sb.ToString());
            return sign;
        }

        /// <summary>
        /// 获取HMAC-SHA1加密数据
        /// </summary>
        /// <param name="key">密钥</param>
        /// <param name="content">明文</param>
        /// <returns></returns>
        public static string HmacEncrypt(string key, string content)
        {
            var bites_content = UTF8.GetBytes(content);
            var bites_key = UTF8.GetBytes(key);
            using var encrypt = new HMACSHA1(bites_key);
            var result = encrypt.ComputeHash(bites_content);
            return ToBase64String(result);
        }
    }
}
