﻿// -- Function: RealtimeWorkbookDotModel.cs
// --- Project: X.PenServer.Contracts
// ---- Remark: 
// ---- Author: Lucifer
// ------ Date: 2024/3/21 13:11
namespace X.PenServer.Contracts.Socket;

using System.Text.Json.Serialization;
using Queue;

public class RealtimeWorkbookDotModel
{
	/// <summary>
	/// 试卷id
	/// </summary>
	[JsonPropertyName(nameof(PaperId))]
	[JsonInclude]
	public string PaperId { get; set; }

	/// <summary>
	/// 题目id
	/// </summary>
	[JsonPropertyName(nameof(ItemId))]
	[JsonInclude]
	public string ItemId { get; set; }
	
	/// <summary>
	/// 学生id
	/// </summary>
	[JsonPropertyName(nameof(StudentId))]
	[JsonInclude]
	public string StudentId { get; set; }

	/// <summary>
	/// 学生班级id
	/// </summary>
	[JsonPropertyName(nameof(ClassId))]
	[JsonInclude]
	public string ClassId { get; set; }

	/// <summary>
	/// 点位
	/// </summary>
	[Json<PERSON>ropertyName(nameof(Dots))]
	[JsonInclude]
	public List<WorkbookPenDot> Dots { get; set; }

	/// <summary>
	/// 当前采集的页码
	/// </summary>
	[JsonPropertyName(nameof(Page))]
	[JsonInclude]
	public int Page { get; set; }
}