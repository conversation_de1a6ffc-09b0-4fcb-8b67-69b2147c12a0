﻿using System.Text.Json.Serialization;

namespace X.PenServer.Contracts
{
    public class DotDto
    {
        [JsonInclude]
        public ushort X;

        [JsonInclude]
        public ushort Y;

        [JsonInclude]
        public ushort RawX;

        [JsonInclude]
        public ushort RawY;

        [<PERSON>sonInclude]
        public uint page;

        [JsonInclude]
        public ushort book_no;

        [JsonInclude]
        public int book_width;

        [JsonInclude]
        public int book_height;

        [JsonInclude]
        public uint reserved1;

        [JsonInclude]
        public byte Type;

        [JsonInclude]
        public ushort pr;

        [JsonInclude]
        public DateTime? LogTime { get; set; }
    }
}
