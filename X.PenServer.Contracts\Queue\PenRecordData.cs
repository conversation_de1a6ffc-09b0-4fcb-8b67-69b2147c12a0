﻿// -- Function: PenRecordData.cs
// --- Project: X.PenServer.Contracts
// ---- Remark: 
// ---- Author: Lucifer
// ------ Date: 2024/3/7 14:56
namespace X.PenServer.Contracts.Queue;

using System.Text.Json.Serialization;

/// <summary>
/// 录制
/// </summary>
public class PenRecordData
{
	/// <summary>
	/// 点阵笔Mac地址
	/// </summary>
	[JsonPropertyName(nameof(Mac))]
	[JsonInclude]
	public string Mac { get; set; }

	/// <summary>
	/// 采集到的页码id
	/// </summary>
	[JsonPropertyName(nameof(Page))]
	[JsonInclude]
	public int Page { get; set; }

	/// <summary>
	/// 明鼎页码对应业务页码id
	/// </summary>
	[JsonPropertyName(nameof(PageId))]
	[JsonInclude]
	public int PageId { get; set; }

	/// <summary>
	/// 当前用户角色: 0.学生 1.教师
	/// </summary>
	[JsonPropertyName(nameof(UserRole))]
	[JsonInclude]
	public int UserRole { get; set; }

	/// <summary>
	/// 教师用户id
	/// </summary>
	[JsonPropertyName(nameof(TeacherId))]
	[JsonInclude]
	public string TeacherId { get; set; }

	/// <summary>
	/// 学生用户id
	/// </summary>
	[JsonPropertyName(nameof(StudentId))]
	[JsonInclude]
	public string StudentId { get; set; }

	/// <summary>
	/// 试卷id
	/// </summary>
	[JsonPropertyName(nameof(PaperId))]
	[JsonInclude]
	public string PaperId { get; set; }

	/// <summary>
	/// 题目id
	/// </summary>
	[JsonPropertyName(nameof(ItemId))]
	[JsonInclude]
	public string ItemId { get; set; }

	/// <summary>
	/// 试卷页面id
	/// </summary>
	[JsonPropertyName(nameof(WorkbookPageId))]
	[JsonInclude]
	public string WorkbookPageId { get; set; }

	/// <summary>
	/// 录音文件数据
	/// </summary>
	[JsonPropertyName(nameof(FileUrl))]
	[JsonInclude]
	public string FileUrl { get; set; }
}