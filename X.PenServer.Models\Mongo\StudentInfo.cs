﻿// -- Function: StudentInfo.cs
// --- Project: X.PenServer.Models
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2023/10/31 15:12

using System.Text.Json.Serialization;

namespace X.PenServer.Models.Mongo;

using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

/// <summary>
/// 学生信息
/// </summary>
public class StudentInfo : MongoBaseModel
{
	/// <summary>
	/// 学生id
	/// </summary>
	[BsonElement(nameof(UserId))]
	[BsonRepresentation(BsonType.String)]
	[JsonPropertyName(nameof(UserId))]
	[JsonInclude]
	public string UserId { get; set; }

	/// <summary>
	/// 真实姓名
	/// </summary>
	[BsonElement(nameof(RealName))]
	[BsonRepresentation(BsonType.String)]
	[JsonPropertyName(nameof(RealName))]
	[JsonInclude]
	public string RealName { get; set; }

	/// <summary>
	/// 用户名
	/// </summary>
	[BsonElement(nameof(UserName))]
	[BsonRepresentation(BsonType.String)]
	[JsonPropertyName(nameof(UserName))]
	[JsonInclude]
	public string UserName { get; set; }

	/// <summary>
	/// 昵称
	/// </summary>
	[BsonElement(nameof(NickName))]
	[BsonRepresentation(BsonType.String)]
	[JsonPropertyName(nameof(NickName))]
	[JsonInclude]
	public string NickName { get; set; }

	/// <summary>
	/// 学校id
	/// </summary>
	[BsonElement(nameof(SchoolId))]
	[BsonRepresentation(BsonType.String)]
	[JsonPropertyName(nameof(SchoolId))]
	[JsonInclude]
	public string SchoolId { get; set; }

	/// <summary>
	/// 手机号
	/// </summary>
	[BsonElement(nameof(Mobile))]
	[BsonRepresentation(BsonType.String)]
	[JsonPropertyName(nameof(Mobile))]
	[JsonInclude]
	public string Mobile { get; set; }

	/// <summary>
	/// 班级id
	/// </summary>
	[BsonElement(nameof(ClassId))]
	[BsonRepresentation(BsonType.String)]
	[JsonPropertyName(nameof(ClassId))]
	[JsonInclude]
	public string ClassId { get; set; }

	/// <summary>
	/// 学号
	/// </summary>
	[BsonElement(nameof(StudentNo))]
	[BsonRepresentation(BsonType.String)]
	[JsonPropertyName(nameof(StudentNo))]
	[JsonInclude]
	public string StudentNo { get; set; }
}