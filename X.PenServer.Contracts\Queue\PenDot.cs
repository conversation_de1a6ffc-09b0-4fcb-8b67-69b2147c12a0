﻿//  -- Function: PenDot.cs
//  --- Project: PenServer
//  ---- Remark:
//  ---- Author: Lucifer
//  ------ Date: 2023/02/22 14:45

using System.Text.Json.Serialization;

namespace X.PenServer.Contracts.Queue;

public class PenDot
{
	/// <summary>
	/// 雪花id
	/// </summary>
	[JsonPropertyName(nameof(Oid))]
	[JsonInclude]
	public long Oid { get; set; }

	/// <summary>
	/// X
	/// </summary>
	[JsonPropertyName(nameof(X))]
	[JsonInclude]
	public int X { get; set; }

	/// <summary>
	/// Y
	/// </summary>
	[JsonPropertyName(nameof(Y))]
	[JsonInclude]
	public int Y { get; set; }

	/// <summary>
	/// 点位类型: 1.常规点位 2.结束点位
	/// </summary>
	[JsonPropertyName(nameof(Type))]
	[JsonInclude]
	public int Type { get; set; }

	/// <summary>
	/// 采集到的页码
	/// </summary>
	[JsonPropertyName(nameof(Page))]
	[JsonInclude]
	public int Page { get; set; }

	/// <summary>
	/// 时间
	/// </summary>
	[JsonPropertyName(nameof(Time))]
	[JsonInclude]
	public DateTime Time { get; set; }

	/// <summary>
	/// BookNo
	/// </summary>
	[JsonPropertyName(nameof(BookNo))]
	[JsonInclude]
	public int BookNo { get; set; }

	/// <summary>
	/// Pressure
	/// </summary>
	[JsonPropertyName(nameof(Pressure))]
	[JsonInclude]
	public int Pressure { get; set; }

    /// <summary>
    /// 笔迹颜色
    /// </summary>
    [JsonPropertyName(nameof(Color))]
    [JsonInclude]
    public string Color { get; set; }
}