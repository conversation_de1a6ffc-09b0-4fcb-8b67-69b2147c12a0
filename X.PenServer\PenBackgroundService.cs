﻿// -- Function: PenBackgroundService.cs
// --- Project: <PERSON><PERSON>Pen<PERSON>erver
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2023/10/24 17:59

using Microsoft.Extensions.Hosting;
using X.PenServer.Interfaces;

namespace X.PenServer;

using static Task;

public class PenBackgroundService : IHostedService
{
    private readonly IPenService _service;

    public PenBackgroundService(IPenService service)
    {
        _service = service;
    }

    #region Implementation of IHostedService

    /// <inheritdoc />
    public async Task StartAsync(CancellationToken token)
    {
        _service.Start();
        await Delay(100, token);
    }

    /// <inheritdoc />
    public async Task StopAsync(CancellationToken token)
    {
        _service.Stop();
        await Delay(100, token);
    }

    #endregion
}