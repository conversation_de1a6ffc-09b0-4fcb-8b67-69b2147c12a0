﻿// -- Function: TeacherCorrectRedisService.cs
// --- Project: X.PenServer.Services
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2023/10/31 11:35

using StackExchange.Redis;
using X.Elegant.Redis;
using X.Elegant.Redis.Infrastructure;
using X.PenServer.Contracts.Redis;
using X.PenServer.Interfaces.Redis;

namespace X.PenServer.Services.Redis;

/// <inheritdoc cref="ITeacherCorrectRedisService" />
public class TeacherCorrectRedisService : RedisService, ITeacherCorrectRedisService
{
    /// <inheritdoc />
    public TeacherCorrectRedisService(IRedisServiceFactory factory) : base(factory)
    {
    }

    #region Overrides of RedisService

    /// <inheritdoc />
    protected override string Prefix => RedisKeys.TEACHER_CORRECT + "|";

    #endregion

    #region Implementation of ITeacherCorrectRedisService

    /// <inheritdoc />
    public async Task SetCorrectStudentAsync(string teacherid, string paperid, string classid, string studentid, int pageno)
    {
        var key = $"TeacherCorrectStudentNo|{teacherid}";
        var data = new TeacherCorrectingStudent
        {
            Page = pageno,
            StudentId = studentid,
            ClassId = classid,
            PaperId = paperid
        };
        await SetAsync(key, data, TimeSpan.FromHours(2), flags: CommandFlags.FireAndForget);
    }

    /// <inheritdoc />
    public async Task<TeacherCorrectingStudent> GetCorrectStudentAsync(string teacherid)
    {
        var key = $"TeacherCorrectStudentNo|{teacherid}";
        return await GetAsync<TeacherCorrectingStudent>(key);
    }

    /// <inheritdoc />
    public async Task<string> HGetCorrectStudentAsync(string teacherid)
    {
        const string key = "TeacherCurrentCorrect";
        return await HGetStringAsync(key, teacherid);
    }

    /// <inheritdoc />
    public async Task HSetCorrectStudentAsync(string teacherid, string studentid)
    {
        const string key = "TeacherCurrentCorrect";
        await HSetAsync(key, teacherid, studentid, flags: CommandFlags.FireAndForget);
    }

    /// <inheritdoc />
    public async Task HSetCorrectStudentStatusAsync(string paperid, string studentid, int status)
    {
        const string key = "CorrectStudentStatus";
        var field = $"{paperid}|{studentid}";
        await HSetAsync(key, field, status, flags: CommandFlags.FireAndForget);
    }

    /// <inheritdoc />
    public async Task<int> HGetCorrectStudentStatusAsync(string paperid, string studentid)
    {
        const string key = "CorrectStudentStatus";
        var field = $"{paperid}|{studentid}";
        return await HGetAsync<int>(key, field);
    }

    /// <inheritdoc />
    public async Task HSetStudentCorrectPaperAsync(string studentid, string paperid)
    {
        const string key = "StudentCorrectPaperStatus";
        var field = $"{studentid}|{paperid}";
        await HSetAsync(key, field, 1, flags: CommandFlags.FireAndForget);
    }

    /// <inheritdoc />
    public async Task<string> HGetCorrectClassIdAsync(string teacherid)
    {
        const string key = "TeacherCorrectClass";
        return await HGetStringAsync(key, teacherid);
    }

    /// <inheritdoc />
    public async Task<StudentInfo> GetCorrectStudentInfoAsync(string studentid)
    {
        var key = $"EachCorrectStudent|{studentid}";
        return await GetAsync<StudentInfo>(key);
    }

    /// <inheritdoc />
    public async Task SetCorrectStudentInfoAsync(string studentid, StudentInfo by_correct_student)
    {
        var key = $"EachCorrectStudent|{studentid}";
        await SetAsync(key, by_correct_student, TimeSpan.FromHours(2), flags: CommandFlags.FireAndForget);
    }

    /// <inheritdoc />
    public async Task SAddByCorrectStudentInfoAsync(string by_correct_studentid, StudentInfo student)
    {
        var key = $"EachCorrectByStudent|{by_correct_studentid}";
        await SAddAsync(key, student);
        await ExpireAsync(key, TimeSpan.FromHours(2), flags: CommandFlags.FireAndForget);
    }

    #endregion
}