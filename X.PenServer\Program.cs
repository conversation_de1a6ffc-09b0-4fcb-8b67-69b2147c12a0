﻿using System.Text;
using System.Text.Encodings.Web;
using System.Text.Json;
using System.Text.Json.Serialization;
using Autofac;
using Autofac.Extensions.DependencyInjection;
using Masa.BuildingBlocks.Data;
using MassTransit;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.AspNetCore.Server.Kestrel.Core;
using Microsoft.AspNetCore.WebSockets;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NLog;
using NLog.Extensions.Logging;
using NLog.Web;
using X.Elegant.Redis;
using X.Elegant.Redis.Infrastructure.Converters;
using X.PenServer.Contracts.Config;
using X.PenServer.Infrastructure;
using X.PenServer.Middlewares;
using X.PenServer.Modules;
using X.PenServer.Profiles;
using X.PenServer.Contracts.Queue;
using X.PenServer.Contracts.Socket;

namespace X.PenServer;

using System.Security.Cryptography;
using Huawei.Cloud.OBS.Core;
using Microsoft.AspNetCore.DataProtection;
using Microsoft.IdentityModel.Tokens;
using Implement;
using static AutofacProvider;
using static CodePagesEncodingProvider;
using static ForwardedHeaders;
using static Microsoft.AspNetCore.Authentication.JwtBearer.JwtBearerDefaults;
using static SequentialGuidType;
using static WebApplication;

public static class Program
{
    public static async Task Main(string[] args)
    {
        var nlog = Path.Combine(AppContext.BaseDirectory, "nlog.config");
        var log = LogManager.Setup().LoadConfigurationFromFile(nlog).GetCurrentClassLogger();
        const string cors_policy = "CorsPolicy";
        try
        {
            Encoding.RegisterProvider(Instance);
            var builder = CreateBuilder(args);
            builder.Services.AddCors
            (
                x =>
                {
                    x.AddPolicy
                    (
                        cors_policy,
                        z =>
                        {
                            z.AllowAnyHeader()
                                .AllowAnyMethod()
                                .AllowAnyOrigin()
                                .WithExposedHeaders("Grpc-Status", "Grpc-Message", "Grpc-Encoding", "Grpc-Accept-Encoding");
                        }
                    );
                }
            );
            var config = builder.Configuration;
            builder.Services.AddOptions();
            builder.Services.Configure<PenConfig>(config.GetSection(nameof(PenConfig)));
            builder.Services.Configure<MongoConfig>(config.GetSection(nameof(MongoConfig)));
            builder.Services.Configure<RabbitConfig>(config.GetSection(nameof(RabbitConfig)));
            builder.Services.Configure<SsoConfig>(config.GetSection(nameof(SsoConfig)));
            builder.Services.Configure<KestrelConfig>(config.GetSection(nameof(KestrelConfig)));
            //配置数据保护
            var protectdir = Path.Combine(AppContext.BaseDirectory, "Protection");
            if (!Directory.Exists(protectdir))
            {
                Directory.CreateDirectory(protectdir);
            }

            builder.Services.AddTransient<IDataProtector, DataProtector>();
            builder.Services.AddDataProtection().PersistKeysToFileSystem(new DirectoryInfo(protectdir)).SetApplicationName("Lucifer").SetDefaultKeyLifetime(TimeSpan.FromDays(60));
            builder.Services.AddSingleton<IPenConfig>(x => x.GetRequiredService<IOptions<PenConfig>>().Value);
            builder.Services.AddSingleton<IMongoConfig>(x => x.GetRequiredService<IOptions<MongoConfig>>().Value);
            builder.Services.AddSingleton<IRabbitConfig>(x => x.GetRequiredService<IOptions<RabbitConfig>>().Value);
            builder.Services.AddSingleton<ISsoConfig>(x => x.GetRequiredService<IOptions<SsoConfig>>().Value);
            builder.Services.AddSingleton(x => x.GetRequiredService<IOptions<JsonSerializerOptions>>().Value);
            builder.Services.AddSingleton<IKestrelConfig>(x => x.GetRequiredService<IOptions<KestrelConfig>>().Value);
            builder.Services.AddSingleton
            (
                new ParallelOptions
                {
                    MaxDegreeOfParallelism = Environment.ProcessorCount - 1
                }
            );
            builder.Services.AddSequentialGuidGenerator(SequentialAsBinary);
            var workerid = RandomNumberGenerator.GetInt32(1, 1023);
            Environment.SetEnvironmentVariable("WORKER_ID", workerid.ToString());
            builder.Services.AddSnowflake(x =>
            {
                x.MaxCallBackTime = 1500;
            });
            var kestrel_config = config.GetSection(nameof(KestrelConfig)).Get<KestrelConfig>();
            builder.Services.Configure<KestrelServerOptions>
            (
                x =>
                {
                    x.Limits.MaxConcurrentUpgradedConnections = null;
                    x.Limits.MaxConcurrentConnections = null;
                    foreach (var item in kestrel_config.KestrelOptions)
                    {
                        x.ListenAnyIP
                        (
                            item.Port, z => { z.Protocols = Enum.Parse<HttpProtocols>(item.Protocol.ToString()); }
                        );
                    }
                }
            );
            builder.Services.Configure<ForwardedHeadersOptions>
            (
                x => { x.ForwardedHeaders = XForwardedFor | XForwardedHost | XForwardedProto; }
            );
            builder.Services.AddElegantRedisService(x =>
            {
                x.PropertyNameCaseInsensitive = true;
                x.PropertyNamingPolicy = null;
                x.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull;
                x.NumberHandling = JsonNumberHandling.AllowReadingFromString;
                x.Converters.Add(new DateTimeJsonConverterLater());
                x.Encoder = JavaScriptEncoder.UnsafeRelaxedJsonEscaping;
                x.ReadCommentHandling = JsonCommentHandling.Skip;
            });
            //替换默认控制器容器
            builder.Services.Replace(ServiceDescriptor.Scoped<IControllerActivator, ServiceBasedControllerActivator>());

            //添加事件总线和队列
            builder.Services.AddMassTransit
            (
                x =>
                {
                    var mqcfg = config.GetSection(nameof(RabbitConfig)).Get<RabbitConfig>();
                    x.SetKebabCaseEndpointNameFormatter();
                    x.AddDelayedMessageScheduler();
                    x.UsingRabbitMq
                    (
                        (ctx, cfg) =>
                        {
                            cfg.Host
                            (
                                mqcfg.Host, mqcfg.Port, mqcfg.VirtualHost, h =>
                                {
                                    h.Username(mqcfg.UserName);
                                    h.Password(mqcfg.Password);
                                    h.Heartbeat
                                    (
                                        mqcfg.Heartbeat > 0
                                            ? TimeSpan.FromSeconds(mqcfg.Heartbeat)
                                            : TimeSpan.Zero
                                    );
                                    h.ConfigureBatchPublish
                                    (
                                        c =>
                                        {
                                            c.Enabled = true;
                                        }
                                    );
                                    h.PublisherConfirmation = true;
                                }
                            );
                            cfg.PrefetchCount = 64;
                            cfg.PurgeOnStartup = false;
                            cfg.AutoDelete = false;
                            cfg.SingleActiveConsumer = false;
                            cfg.ConcurrentMessageLimit = 2048;
                            cfg.Durable = true;
                            cfg.AutoStart = true;
                            cfg.QueueExpiration = TimeSpan.FromDays(30);
                            cfg.OverrideDefaultBusEndpointQueueName("Lucifer");
                            cfg.UseDelayedRedelivery
                            (
                                z => { z.Interval(3, TimeSpan.FromSeconds(20)); }
                            );
                            cfg.ConfigureEndpoints
                            (
                                ctx, z =>
                                {
                                    z.Include<PenDotData>();
                                    z.Include<RealtimeBatteryModel>();
                                    z.Include<RealtimePenStateModel>();
                                    z.Include<PenRecordData>();
                                    z.Include<OfflineCorrectPenDotData>();
                                }, new SnakeCaseEndpointNameFormatter(false)
                            );
                        }
                    );
                }
            );

            //配置AutoMapper
            var automapper = typeof(ProfileBase).Assembly;
            builder.Services.AddAutoMapper(automapper);
            builder.Services.AddObsService();

            //主机配置
            builder.Host
                .UseServiceProviderFactory(new AutofacServiceProviderFactory())
                .ConfigureLogging
                (
                    x =>
                    {
                        x.ClearProviders();
                        x.AddNLog();
                    }
                )
                .UseNLog()
                .UseWindowsService()
                .UseConsoleLifetime()
                .ConfigureContainer<ContainerBuilder>
                (
                    x =>
                    {
                        //注册公用模块
                        x.RegisterModule<AutofacModule>();
                    }
                );
            builder.WebHost.UseKestrel();
            var sso_config = new SsoConfig();
            config.Bind(nameof(SsoConfig), sso_config);
            builder.Services.AddAuthentication
            (
                x =>
                {
                    x.DefaultScheme = AuthenticationScheme;
                    x.DefaultAuthenticateScheme = AuthenticationScheme;
                    x.DefaultChallengeScheme = AuthenticationScheme;
                }
            ).AddJwtBearer
            (
                x =>
                {
                    var bytes = Encoding.UTF8.GetBytes(sso_config.SigningKey);
                    x.SaveToken = true;
                    x.TokenValidationParameters = new TokenValidationParameters
                    {
                        ValidateIssuer = true,
                        ValidateAudience = true,
                        ValidateIssuerSigningKey = true,
                        ValidAudience = sso_config.Audience,
                        ValidIssuer = sso_config.Issuer,
                        IssuerSigningKey = new SymmetricSecurityKey(bytes)
                    };
                }
            );
            builder.Services.AddAuthorization();
            builder.Services.AddControllers().AddControllersAsServices();
            builder.Services
                .AddWebSockets
                (
                    x =>
                    {
                        x.AllowedOrigins.Add("*");
                        x.KeepAliveInterval = TimeSpan.FromSeconds(60);
                    }
                )
                .AddPenServerSocket()
                .AddRealtimeMonitor();
            builder.Services.AddHostedService<PenBackgroundService>();
            builder.Services.AddHostedService<ReceivePortMessageBackgroundService>();
            builder.Services.AddHttpContextAccessor();
            builder.Services.AddGrpc();
            var app = builder.Build();
            Container = app.Services.GetAutofacRoot();
            app.UseRouting();
            app.UseAuthentication();
            app.UseAuthorization();
            app.MapControllers();
            app.UseCors(cors_policy);
            app.MapGrpcService<MediaRpcServerService>()
                .RequireCors(cors_policy);

            app.UseWebSockets()
                .UsePenServerSocket()
                .UseRealtimeMonitor();
            await app.RunAsync();
        }
        catch (Exception e)
        {
            log.Fatal(e);
        }
        finally
        {
            LogManager.Shutdown();
        }
    }
}