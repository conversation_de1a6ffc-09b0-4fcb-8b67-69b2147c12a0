<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>disable</Nullable>
        <IsPackable>true</IsPackable>
        <IsTestProject>true</IsTestProject>
        <ErrorOnDuplicatePublishOutputFiles>false</ErrorOnDuplicatePublishOutputFiles>
        <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
        <AppendRuntimeIdentifierToOutputPath>false</AppendRuntimeIdentifierToOutputPath>
        <!--auto version start-->
        <Deterministic>false</Deterministic>
        <GenerateAssemblyFileVersionAttribute>false</GenerateAssemblyFileVersionAttribute>
        <GenerateAssemblyInformationalVersionAttribute>false</GenerateAssemblyInformationalVersionAttribute>
        <AssemblyVersion>1.0.*</AssemblyVersion>
        <!--auto version end-->
    </PropertyGroup>
    <PropertyGroup>
        <Copyright>Copyright © Lucifer. All rights reserved.</Copyright>
        <PackageId>X.PenServer.UnitTest</PackageId>
        <PackageProjectUrl>https://github.com/X-Lucifer</PackageProjectUrl>
        <RepositoryUrl>https://github.com/X-Lucifer</RepositoryUrl>
        <Company>X Lucifer</Company>
        <Authors>Lucifer</Authors>
        <AutoIncrementPackageRevision>true</AutoIncrementPackageRevision>
    </PropertyGroup>
    <ItemGroup>
        <PackageReference Include="coverlet.collector" Version="6.0.2">
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.10.0" />
        <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
        <PackageReference Include="System.Reflection.Metadata" Version="8.0.0" />
        <PackageReference Include="xunit" Version="2.9.0" />
        <PackageReference Include="Xunit.Microsoft.DependencyInjection" Version="8.2.0" />
        <PackageReference Include="xunit.runner.visualstudio" Version="2.8.2">
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
    </ItemGroup>
    <ItemGroup>
        <PackageReference Include="Dapper" Version="2.1.52" />
        <PackageReference Include="JetBrains.Annotations" Version="2024.2.0" />
        <PackageReference Include="NLog.Web.AspNetCore" Version="5.3.11" />
    </ItemGroup>
    <ItemGroup>
        <Using Include="Xunit"/>
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\X.PenServer.Contracts\X.PenServer.Contracts.csproj" />
      <ProjectReference Include="..\X.PenServer.Infrastructure\X.PenServer.Infrastructure.csproj" />
      <ProjectReference Include="..\X.PenServer.Interfaces\X.PenServer.Interfaces.csproj" />
      <ProjectReference Include="..\X.PenServer.Models\X.PenServer.Models.csproj" />
      <ProjectReference Include="..\X.PenServer.Profiles\X.PenServer.Profiles.csproj" />
      <ProjectReference Include="..\X.PenServer.Services\X.PenServer.Services.csproj" />
    </ItemGroup>

    <ItemGroup>
      <None Update="nlog.config">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </None>
      <None Update="appsettings.json">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </None>
    </ItemGroup>

</Project>
