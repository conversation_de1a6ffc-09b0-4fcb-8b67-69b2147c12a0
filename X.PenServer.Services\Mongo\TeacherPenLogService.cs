﻿// -- Function: TeacherPenLogService.cs
// --- Project: X.PenServer.Services
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2023/10/26 15:30

namespace X.PenServer.Services.Mongo;

/// <inheritdoc cref="ITeacherPenLogService" />
public class TeacherPenLogService : MongoAutoService<TeacherPenLog>, ITeacherPenLogService
{
    /// <inheritdoc />
    public TeacherPenLogService(IMongoConfig config) : base(config)
    {
    }

    /// <inheritdoc />
    protected override void CreateIndex(IMongoCollection<TeacherPenLog> collection)
    {
	    var teacherid_builder = Builders<TeacherPenLog>.IndexKeys
		    .Ascending(x => x.TeacherId)
		    .Ascending(x => x.UserId)
		    .Ascending(x => x.PageId)
		    .Ascending(x => x.Page)
		    .Ascending(x => x.AddTime)
		    .Ascending(x => x.Mac);
	    collection.Indexes.CreateIndex(teacherid_builder, collection.CollectionNamespace.CollectionName + "_TeacherId_Key");

	    var userid_builder = Builders<TeacherPenLog>.IndexKeys
		    .Ascending(x => x.UserId)
		    .Ascending(x => x.PageId)
		    .Ascending(x => x.Mac)
		    .Ascending(x => x.Page);
	    collection.Indexes.CreateIndex(userid_builder, collection.CollectionNamespace.CollectionName + "_UserId_Key");
    }
}