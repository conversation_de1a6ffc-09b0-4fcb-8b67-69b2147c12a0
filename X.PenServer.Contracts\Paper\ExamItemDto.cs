﻿namespace X.PenServer.Contracts.Paper
{
    public class ExamItemDto
    {
        /// <summary>
        /// 题目ID
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 题目类型
        /// </summary>
        public string TypeId { get; set; }
    }

    public class ExamPaperDto
    {
        public string Id { get; set; }

        public int? HomeworkType { get; set; }

        public string SubjectId { get; set; }
    }

    public class PaperHandwritingColor
    {
        public string Color { get; set; }
    }

    /// <summary>
    /// 笔码、纸码、卷码的映射关系
    /// </summary>
    public class MD_PaperMacMapping
    {
        public string Id { get; set; }

        /// <summary>
        /// 纸码，对应 dot.page
        /// </summary>
        public int PaperCode { get; set; }

        /// <summary>
        /// 试卷Id
        /// </summary>
        public string PaperId { get; set; }

        /// <summary>
        /// 纸码所对应的试卷的页码
        /// </summary>
        public int PaperPageIndex { get; set; }

        /// <summary>
        /// 笔Mac
        /// </summary>
        public string PMac { get; set; }

        /// <summary>
        /// 用户Id
        /// </summary>
        public string UserId { get; set; }

        public DateTime CreateTime { get; set; }
    }
}
