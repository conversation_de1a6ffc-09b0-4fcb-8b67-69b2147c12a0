﻿// -- Function: AutoPenStateJob.cs
// --- Project: <PERSON>.PenServer.Jobs
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2024/04/04 16:04:59

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Quartz;
using X.PenServer.Interfaces;
using X.PenServer.Jobs.Implement;

namespace X.PenServer.Jobs.Schedulers;

public class AutoPenStateJob : JobBase
{
    private readonly ILogger<AutoPenStateJob> _log;
    private readonly IDapperService _dapper;

    /// <inheritdoc />
    public AutoPenStateJob(ILogger<AutoPenStateJob> log, IDapperService dapper , IConfiguration config)
    {
        _log = log;
        _dapper = dapper;

        var type = GetType();
        var time = DateTime.Today.AddHours(1);
        var is_immediate = config.GetValue<bool>("Immediate");
        if (is_immediate)
        {
            time = DateTime.Now;
        }
        JobDetail = JobBuilder.Create(type).WithIdentity(type.Name).Build();
        Trigger = TriggerBuilder.Create()
            .WithIdentity(type.Name)
            .StartAt(new DateTimeOffset(time))
            .WithSimpleSchedule
            (
                x => { x.WithInterval(TimeSpan.FromDays(1)).RepeatForever(); }
            )
            .Build();
        ScheduleJob = async scheduler =>
        {
            var is_exist = await scheduler.CheckExists(JobDetail.Key);
            if (!is_exist)
            {
                await scheduler.ScheduleJob(JobDetail, Trigger).ConfigureAwait(false);
            }
        };
    }

    #region Overrides of QuartzBackgroundWorkerBase

    /// <inheritdoc />
    public override async Task Execute(IJobExecutionContext context)
    {
        try
        {
            const string sql = """
                               UPDATE [dbo].[MD_UserPenMapping]
                                   SET [state] = 0,
                                       [Battery] = 5
                                   WHERE [Deleted] = 0;
                               """;
            var result = await _dapper.ExecuteAsync(sql).ConfigureAwait(false);
            _log.LogInformation("update pen state: {count}", result.ToString());
        }
        catch (Exception e)
        {
            _log.LogCritical("error: {error}", e.StackTrace ?? e.Message);
        }
    }

    #endregion
}