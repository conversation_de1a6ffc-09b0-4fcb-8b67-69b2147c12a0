﻿// -- Function: TeacherPenDotData.cs
// --- Project: X.PenServer.Contracts
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2023/10/31 15:57

using System.Text.Json.Serialization;

namespace X.PenServer.Contracts.Queue;

/// <summary>
/// 教师笔迹
/// </summary>
public class TeacherPenDotData : PenDotData
{
	/// <summary>
	/// 教师用户id
	/// </summary>
	[JsonPropertyName(nameof(TeacherId))]
	[JsonInclude]
	public string TeacherId { get; set; }

	/// <summary>
	/// 当前页面为第几页
	/// </summary>
	[JsonPropertyName(nameof(CurrentPageNo))]
	[JsonInclude]
	public int CurrentPageNo { get; set; }
}