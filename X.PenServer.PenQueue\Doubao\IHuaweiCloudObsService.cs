﻿using System.Threading.Tasks;
using X.PenServer.Interfaces.Lifecycle;

namespace Uwoo.Util.Obs
{
    /// <summary>
    /// <para><AUTHOR> <PERSON></para>
    /// <para>@Date   2024-12-24 11:04</para>
    /// <para>@Description Huawei OBS Service</para>
    /// </summary>
    public interface IHuaweiCloudObsService : ITransientService
    {
        /// <summary>
        /// 通过文件上传到对象中。
        /// </summary>
        /// <param name="bucketName">存储桶名称。</param>
        /// <param name="objectName">存储桶里的对象名称。</param>
        /// <param name="filePath">要上传的本地文件名。</param>
        /// <param name="contentType">mime类型</param>
        /// <returns></returns>
        Task<string> PutObjectAsync(string bucketName, string objectName, string filePath, string contentType = null);
    }
}
