﻿// -- Function: PenRecordEventArgs.cs
// --- Project: X.PenServer.Contracts
// ---- Remark: 
// ---- Author: Lucifer
// ------ Date: 2024/3/5 14:44
// ReSharper disable InconsistentNaming
// ReSharper disable ClassNeverInstantiated.Global

namespace X.PenServer.Contracts;

using System.Text.Json.Serialization;

/// <summary>
/// 录制事件参数
/// </summary>
public class PenRecordEventArgs : PenMediaBaseEventArgs
{
	/// <summary>
	/// 题目id
	/// </summary>
	[JsonPropertyName(nameof(ItemId))]
	[JsonInclude]
	public string ItemId { get; set; }

	/// <summary>
	/// 试卷页面id
	/// </summary>
	[JsonPropertyName(nameof(WorkbookPageId))]
	[JsonInclude]
	public string WorkbookPageId { get; set; }
}