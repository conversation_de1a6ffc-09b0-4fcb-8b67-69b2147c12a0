﻿//  -- Function: ReceiveData.cs
//  --- Project: Uwoo.PenSocket
//  ---- Remark: 
//  ---- Author: Lucifer
//  ------ Date: 2023/09/19 10:43


using System.Text.Json.Serialization;

namespace X.PenServer.Contracts.Realtime;

/// <summary>
/// 接收消息
/// </summary>
/// <typeparam name="T"></typeparam>
public class ReceiveData<T> : ReceiveData where T : class, new()
{
    /// <summary>
    /// 消息内容
    /// </summary>
    [JsonPropertyName(nameof(Content))]
    [JsonInclude]
    public T Content { get; set; }
}

/// <summary>
/// 接收消息
/// </summary>
public class ReceiveData
{
    /// <summary>
    /// 连接状态类型
    /// </summary>
    [JsonPropertyName("type")]
    [JsonInclude]
    public string Type { get; set; }

    /// <summary>
    /// ClassId
    /// </summary>
    [JsonPropertyName(nameof(ClassId))]
    [JsonInclude]
    public string ClassId { get; set; }
}

/// <summary>
/// 发送消息
/// </summary>
public class SendData : ReceiveData
{
    /// <summary>
    /// 消息内容
    /// </summary>
    [JsonPropertyName(nameof(Content))]
    [JsonInclude]
    public string Content { get; set; }
}