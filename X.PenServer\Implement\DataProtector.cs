﻿// -- Function: DataProtector.cs
// --- Project: X.PenServer.Services
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2024/3/6 11:50

namespace X.PenServer.Implement;
using System.Text;
using Microsoft.AspNetCore.DataProtection;

/// <inheritdoc />
public class DataProtector : IDataProtector
{
	private readonly IDataProtectionProvider _provider;

	/// <summary>
	/// Init
	/// </summary>
	/// <param name="provider"></param>
	public DataProtector(IDataProtectionProvider provider)
	{
		_provider = provider;
	}

	#region Implementation of IDataProtectionProvider

	/// <inheritdoc />
	public IDataProtector CreateProtector(string purpose)
	{
		return _provider.CreateProtector(purpose);
	}

	#endregion

	#region Implementation of IDataProtector

	/// <inheritdoc />
	public byte[] Protect(byte[] text)
	{
		var bytes = Convert.ToBase64String(text);
		return Encoding.UTF8.GetBytes(bytes);
	}

	/// <inheritdoc />
	public byte[] Unprotect(byte[] encryptdata)
	{
		var bytes = Encoding.UTF8.GetString(encryptdata);
		return Convert.FromBase64String(bytes);
	}

	#endregion
}