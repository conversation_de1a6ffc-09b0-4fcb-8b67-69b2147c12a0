﻿// -- Function: MatrixRegion.cs
// --- Project: X.PenServer.Infrastructure
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2024/2/20 14:45
namespace X.PenServer.Infrastructure;

using System.Text.Json.Serialization;

/// <summary>
/// 大区域
/// </summary>
public class MatrixRegion : MatrixBase
{
	/// <summary>
	/// 大区域名称
	/// </summary>
	[JsonInclude]
	[JsonPropertyName(nameof(Name))]
	public string Name { get; set; }

	/// <summary>
	/// 大区域类型
	/// </summary>
	/// <remarks>
	/// 1.选择和判断<br/>
	/// 2.单题单空<br/>
	/// 3.单题多空<br/>
	/// 4.整行填空带分值框<br/>
	/// 5.单题多行填空(作文类型, 不区分字数)<br/>
	/// 6.单题多行多小列填空(练习簿类型, 单行每个小列区分)<br/>
	/// 7.卷码号<br/>
	/// 8.学号<br/>
	/// 9.互动答题卡<br/>
	/// </remarks>
	[JsonInclude]
	[JsonPropertyName(nameof(RegionType))]
	public int RegionType { get; set; }

	/// <summary>
	/// 小区域列表
	/// </summary>
	[JsonInclude]
	[JsonPropertyName(nameof(Ranges))]
	public List<MatrixItemRange> Ranges { get; set; }
}