﻿// -- Function: MatrixPoint.cs
// --- Project: X.PenServer.Infrastructure
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2024/2/20 14:42
namespace X.PenServer.Infrastructure;

using System.Text.Json.Serialization;

/// <summary>
/// 矩阵坐标
/// </summary>
public struct MatrixPoint
{
	[JsonInclude]
	[JsonPropertyName(nameof(X))]
	public float X { get; set; }

	[JsonInclude]
	[JsonPropertyName(nameof(Y))]
	public float Y { get; set; }

	public MatrixPoint(float x, float y)
	{
		X = x;
		Y = y;
	}
}