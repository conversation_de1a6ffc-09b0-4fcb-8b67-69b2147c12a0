﻿// -- Function: ApiExtensions.cs
// --- Project: X.PenServer.Infrastructure
// ---- Remark: 
// ---- Author: Lucifer
// ------ Date: 2023/10/27 13:09

using System.Security.Cryptography;
using System.Text;

namespace X.PenServer.Infrastructure;

using System.Collections.Concurrent;
using System.Linq.Expressions;
using System.Reflection;
using System.Runtime.Serialization;
using System.Text.Json;

public static class ApiExtensions
{
    /// <summary>
    /// MD5哈希加密
    /// </summary>
    /// <param name="txt">明文</param>
    /// <param name="lower">默认为小写</param>
    /// <returns></returns>
    public static string HashEncrypt(string txt, bool lower = true)
    {
        var bytes = Encoding.UTF8.GetBytes(txt);
        var hash_bytes = MD5.HashData(bytes);
        var result = Convert.ToHexString(hash_bytes);
        return lower ? result.ToLower() : result;
    }

    /// <summary>
    /// 获取枚举的Memeber值
    /// </summary>
    /// <param name="enumval"></param>
    /// <returns></returns>
    public static string GetEnumValue(this Enum enumval)
    {
        var type = enumval.GetType();
        var meminfo = type.GetMember(enumval.ToString());
        if (meminfo.Length <= 0)
        {
            return "";
        }

        var xmeminfo = meminfo.FirstOrDefault();
        if (xmeminfo == null)
        {
            return "";
        }

        var attrs = xmeminfo.GetCustomAttribute<EnumMemberAttribute>(false);
        return attrs?.Value ?? "";
    }

    /// <summary>
    /// 转为时间戳
    /// </summary>
    /// <param name="time">时间</param>
    /// <returns>秒级时间戳</returns>
    public static long ToSecondStamp(this DateTime time)
    {
        var epoch = DateTime.UnixEpoch;
        return (long) (time - epoch).TotalSeconds;
    }

    /// <summary>
    /// 转为时间戳
    /// </summary>
    /// <param name="time">时间</param>
    /// <returns>毫秒级时间戳</returns>
    public static long ToMilliStamp(this DateTime time)
    {
        var epoch = DateTime.UnixEpoch;
        return (long) (time - epoch).TotalMilliseconds;
    }

    /// <summary>
    /// 从并发字典中移除数据
    /// </summary>
    /// <param name="dictionary">字典</param>
    /// <param name="key">键盘</param>
    /// <typeparam name="TKey"></typeparam>
    /// <typeparam name="TValue"></typeparam>
    /// <returns></returns>
    public static bool TryRemove<TKey, TValue>(this ConcurrentDictionary<TKey, TValue> dictionary, TKey key)
    {
        try
        {
            return ((IDictionary<TKey, TValue>) dictionary).Remove(key);
        }
        catch (Exception)
        {
            return false;
        }
    }

    /// <summary>
    /// 序列化JSON字符串
    /// </summary>
    /// <param name="source">数据源</param>
    /// <typeparam name="T">实体类型</typeparam>
    /// <returns></returns>
    public static string ToJsonString<T>(this T source) where T : class
    {
        var option = AutofacProvider.GetService<JsonSerializerOptions>();
        return JsonSerializer.Serialize(source, option);
    }

    /// <summary>
    /// JSON字符串反序列化为数据实体
    /// </summary>
    /// <param name="json"></param>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    public static T ToEntity<T>(this string json)
    {
        var option = AutofacProvider.GetService<JsonSerializerOptions>();
        return JsonSerializer.Deserialize<T>(json, option);
    }

    /// <summary>
    /// 矩阵区域定位
    /// </summary>
    /// <typeparam name="T">类型</typeparam>
    /// <param name="matrices">矩阵列表</param>
    /// <param name="coordx">当前X坐标</param>
    /// <param name="coordy">当前Y坐标</param>
    /// <param name="page">铺码页码</param>
    /// <returns></returns>
    public static T Locate<T>(this IEnumerable<T> matrices, float coordx, float coordy, int page = 0) where T : MatrixBase
    {
        var list = matrices.ToList();
        var result = list.FirstOrDefault(x => x.IsHitRegion(coordx, coordy, page));
        return result;
    }

    /// <summary>
    /// 是否命中当前区域
    /// </summary>
    /// <typeparam name="T">矩阵点位</typeparam>
    /// <param name="matrix">当前矩阵</param>
    /// <param name="coordx">当前X坐标</param>
    /// <param name="coordy">当前Y坐标</param>
    /// <param name="page">铺码页码</param>
    /// <returns></returns>
    public static bool IsHitRegion<T>(this T matrix, float coordx, float coordy, int page = 0) where T : MatrixBase
    {
        var expression = BuildLocateExpression<T>(coordx, coordy, page);
        var predicate = expression.Compile();
        return predicate.Invoke(matrix);
    }

    /// <summary>
    /// 动态构建区域定位表达式
    /// </summary>
    /// <param name="coordx">X</param>
    /// <param name="coordy">Y</param>
    /// <param name="page">页码</param>
    /// <typeparam name="T"><see cref="MatrixBase"/></typeparam>
    /// <returns></returns>
    private static Expression<Func<T, bool>> BuildLocateExpression<T>(float coordx, float coordy, int page = 0) where T : MatrixBase
    {
        Expression<Func<T, bool>> expression = x => coordx >= x.TopLeft.X &&
                                                    coordx <= x.BottomRight.X &&
                                                    coordy >= x.TopLeft.Y &&
                                                    coordy <= x.BottomRight.Y;
        if (page <= 0)
        {
            return expression;
        }

        Expression<Func<T, bool>> expression_page = x => x.Page == page;
        return expression.AndAlso(expression_page);
    }
}