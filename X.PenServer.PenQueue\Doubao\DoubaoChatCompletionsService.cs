﻿using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Text;
using static Uwoo.Util.Doubao.DoubaoChatCompletionsRequest;

namespace Uwoo.Util.Doubao
{
    public class DoubaoChatCompletionsService : IDoubaoChatCompletionsService
    {
        const string MODEL = "ep-20241223163900-ttqsc";
        const string APIKEY = "7c47b780-c644-45ad-b13f-4c1efc54e0a6";
        const string OCRAPIURL = "https://ark.cn-beijing.volces.com/api/v3/chat/completions";

        readonly ILogger<DoubaoChatCompletionsService> _logger;
        readonly IHttpClientFactory _httpClientFactory;

        public DoubaoChatCompletionsService(
            ILogger<DoubaoChatCompletionsService> logger
            , IHttpClientFactory httpClientFactory
            )
        {
            _logger = logger;
            _httpClientFactory = httpClientFactory;
        }

        public async Task<DoubaoChatCompletionsResponseWrap<DoubaoChatCompletionsResponse>> Ocr(List<ContentBase> requestContents)
        {
            var wrap = new DoubaoChatCompletionsResponseWrap<DoubaoChatCompletionsResponse>();

            if (requestContents==null||requestContents.Count==0)
            {
                goto Over;
            }

            var input = new DoubaoChatCompletionsRequest
            {
                model=MODEL,
                messages=new List<ContentBaseWrap> {
                     new() {
                         role="user",
                         content=requestContents
                     }
                }
            };

            var data = string.Empty;
            var content = new StringContent(JsonConvert.SerializeObject(input), Encoding.UTF8, "application/json");
            var httpClient = _httpClientFactory.CreateClient();

            httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {APIKEY}");

            try
            {
                var response = await httpClient.PostAsync(OCRAPIURL, content);
                if (response.IsSuccessStatusCode)
                {
                    data = await response.Content.ReadAsStringAsync();
                    wrap.IsSuccess=true;
                    wrap.Data = JsonConvert.DeserializeObject<DoubaoChatCompletionsResponse>(data);
                }
                else
                {
                    wrap.Message=response.ReasonPhrase;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Doubao-OCR Error:{}", ex.Message);
                wrap.Message=data;
            }

        Over:
            return wrap;
        }

        readonly static Dictionary<string, string> SubjectiveDics = new()
        {
            { "小学数学",@"
计算题：要求学生根据给定的数字、公式、条件等，运用相应的运算规则进行计算，得出正确的结果。
应用题：这类题目给出一个实际生活场景，要求学生根据所学知识解决具体问题。例如，购物找零、行程问题、工程问题等。
操作题：涉及到使用工具（如尺子、量角器）进行测量或者画图，以及通过动手操作来解决问题，如拼图、折纸等。
开放性问题：问题没有唯一确定的答案，鼓励学生创造性地思考并提出自己的解决方案。这类题目有助于培养学生的创新意识和批判性思维。
探索与发现题：给出一些数据或图形，让学生观察规律、提出猜想，并尝试验证自己的想法。这有助于培养学生的研究能力和科学态度。"
            },
            {
            "小学语文",@"
读准字音：包括多音字组词、选择多音字的正确读音以及根据一个音节写出多个汉字等
认清字形：要求学生写出汉字的笔画或笔顺，按结构要求写字，加（或换）偏旁组字再组词，选字填空，区别形近字组词，找出错别字并改正，把繁体字转换为简化字等
理解字义：查字典填空、给句子中的加点字选择正确的字义、根据一个字的不同意思组词等
理解词语：给出带点字的正确解释，一词多义，联系上下文解释词语的意思等
辨析近义词和反义词：写出近义词或反义词，找出句子中的近反义词，选词填空等
句子改写：如“把”字句和“被”字句的转换，肯定句和否定句的转换等
扩句和缩句：在不改变原句意思的基础上进行扩写或缩写
造句：根据给定的词语或关联词，或者一定的句式来造句
修改病句：识别并改正句子中的毛病，注意使用正确的修改符号
修辞手法：理解和运用比喻、拟人、夸张、排比、设问、反问等修辞手法
理解句子：联系生活实际理解句子含义，抓住关键词语体会意思
阅读理解：通过阅读理解文章的内容、情感及表达方法
写作题：让学生根据给定的主题发挥想象进行写作
"
            },
            {
                "小学英语",@"
听力题：考查学生的听力理解能力，通常包括听录音选图片、判断正误、回答问题等
单词题：主要围绕单词进行考查，包括单词的拼写、词义、词性、词形变化等。例如，根据中文意思写单词、写出单词的复数形式、反义词、近义词等。单词题是英语学习的基础，通过这类题型可以帮助学生巩固词汇量，掌握单词的各种用法。
排序题：将一些打乱顺序的单词、句子或段落等，要求学生按照一定的逻辑顺序（如时间顺序、事件发展顺序、语法结构顺序等）进行重新排列。例如，将打乱的句子排成一段通顺的话、将制作某种物品的步骤按顺序排列等。排序题可以考查学生对语言逻辑和篇章结构的理解能力，以及对语法和词汇在语境中运用的掌握程度。
阅读理解：通过阅读短文或文章，回答相关问题
翻译题：将中文翻译成英文或将英文翻译成中文
句型转换：改变句子的结构或形式，如肯定句变否定句、被动语态等
完形填空：在一篇短文中留出若干个空白，要求学生根据上下文填写适当的单词
补全对话/短文：给出一段不完整的对话或短文，要求学生补充完整
仿写：模仿给定的句子或段落进行写作
书面表达：可能是写一封信、描述一个场景或表达个人观点等，考查学生的书面表达能力
连词成句：给出几个单词，要求学生将其连成一个通顺的句子
"
            },
            {
                "初中数学",@"
解答题：解答题通常是给出一个问题情境，要求学生通过一系列的计算和推理得出答案，并且需要写出详细的解题步骤。例如：
整式的化简：涉及多项式的加减乘除、幂的运算等。
一元二次方程求解：使用因式分解、配方法、公式法求解。
函数的定义域和值域：确定函数的有效输入范围和输出范围。
三角函数的变换与求值：应用三角恒等变换公式解决问题。
数列的通项公式和求和：根据已知条件找出数列的规律并求和。
几何图形的面积和体积计算：运用几何原理进行测量和计算。
证明题：证明题要求学生利用已知定理和公理来论证某个数学命题的真实性。这类题目强调逻辑推理和演绎能力，是培养严谨性的重要途径。例如：
平面几何证明：如证明线段相等、角相等、三角形全等等。
立体几何证明：比如证明两个平面平行或垂直，直线和平面的关系等。
代数结构证明：例如证明群、环、域等抽象代数结构中的性质。
不等式证明：利用均值不等式、柯西-施瓦茨不等式等工具证明数值关系。
极限和连续性的证明：在分析学中，证明函数的极限存在或函数在其定义域内连续。
应用题：""应用题是将实际生活中的问题抽象为数学问题，让学生运用数学知识进行求解。它考查学生的数学建模能力，即把实际情境转化为数学模型，并运用相应的数学方法解决问题；例如：
经济应用：包括成本利润分析、供求关系、投资回报率等经济概念的量化分析。
工程应用：像建筑结构设计、机械制造中的尺寸计算、流体力学问题等。
统计与概率应用：从数据中提取信息，进行预测和决策，如抽样调查、风险评估等。
优化问题：寻找最佳解决方案，如最短路径、最小费用、最大收益等问题。
"
            },
            {
                "初中语文",@"
默写题：要求学生背诵并准确默写出指定的古诗文段落或篇章。这种题型考查学生的记忆力以及对古诗文的熟悉程度。
文言文阅读：以古代文言文为文本材料，考查学生对文言文实词、虚词的理解，句子的翻译，以及对文章内容、人物形象、主题思想等方面的把握
综合性学习：这是一种综合性较强的题型，通常会结合生活实际和语文知识，考查学生的语言表达、信息提取、组织协调等能力。例如开展一个关于 “传统节日文化” 的综合性学习活动，会要求学生设计活动方案，如活动的主题、形式（演讲比赛、手抄报制作等）、步骤；还可能要求学生根据调查数据写调查报告，或者针对某一现象发表自己的观点，并且语言表达要符合逻辑、通顺得体。
诗词鉴赏：这类题型主要考查学生对古代诗歌的理解和欣赏能力。要求学生能理解诗歌的内容，包括诗句的字面意思、深层含义；体会诗歌的情感，如思乡之情、爱国情怀、送别之愁等；分析诗歌的艺术手法，像修辞手法（比喻、拟人、夸张等）、表现手法（借景抒情、托物言志等）、表达方式（描写、抒情、议论）等。
阅读理解：提供一篇现代文文章，要求学生阅读并回答问题。问题可能包括对文章内容的理解、作者观点的把握、语言特色的分析等。
名著阅读：要求学生阅读指定的名著，并回答相关问题。这些问题可能涉及名著的主要情节、人物形象、主题思想等
写作题：要求学生根据给定的题目或材料进行写作。作文题目可能涉及记叙文、议论文、说明文等不同类型的文体，考查学生的写作能力和思维水平。
"
            },
            {
                "初中英语",@"
完形填空：一般是一篇有一定情节的短文，文中会有若干个空。它综合考查学生的词汇、语法、阅读理解等能力。学生需要根据文章的上下文语境，选择合适的单词或短语填入空白处，使文章意思通顺、逻辑连贯
阅读理解：通常会提供一篇或多篇短文，短文题材多样，包括记叙文、说明文、议论文等。题目类型主要有细节理解题，要求学生从文中找到与题目相关的具体信息；主旨大意题，需要学生归纳文章的中心思想；推理判断题，学生要根据文章内容进行合理的推断，如人物的情感、事件的发展趋势等；词义猜测题，让学生结合上下文猜测文中生词或短语的意思
任务型阅读：这种题型要求学生在阅读文章后完成一系列任务。常见的任务包括回答问题、翻译句子、填写表格、完成句子等
补全对话：提供一个对话场景，对话中有部分空缺，要求学生根据上下文和交际习惯，选择合适的句子补全对话
句型转换：主要考查学生对英语句子结构和语法知识的掌握。包括陈述句变疑问句（一般疑问句、特殊疑问句）
短文填空：提供一篇短文，文中有若干个空，要求学生根据短文内容和语法规则，用所给单词的适当形式填空，或者根据语境填写合适的单词。
书面表达：要求学生根据题目要求，用英语写一篇短文。题材包括记叙文（如描述一次难忘的经历）、说明文（如介绍一种产品）、议论文（如表达对某个现象的看法）等。写作时要注意语法正确、词汇丰富、句式多样、条理清晰
翻译题：将中文翻译成英文或将英文翻译成中文
补全对话/短文：给出一段不完整的对话或短文，要求学生补充完整
"
            }
        };

        readonly static Dictionary<string, string> SubjectiveScoreDics = new()
        {
            { "应用题",@"
满分标准：1、计算正确
算式列法正确，且计算过程和最终答案都准确无误。例如对于应用题 “小明有 10 个苹果，小红的苹果数是小明的 2 倍，小红有多少个苹果？”，学生列式为10×2=20（个），这样的答案可以得满分。
2、单位正确
当应用题涉及单位时，单位的书写必须正确。比如在行程问题 “甲乙两地相距 300 千米，一辆汽车从甲地开往乙地，速度是每小时 50 千米，行驶了 3 小时后，离乙地还有多远？” 中，学生不仅要算出距离是300-50×3=150千米，单位 “千米” 也必须书写正确才能得满分。
3、答题完整
学生完整地回答了问题，包括按照题目要求求出所有需要的结果。例如在 “一个正方体棱长为 3 厘米，求它的表面积和体积” 这一问题中，学生需要分别计算出表面积6×3×3=54平方厘米和体积3×3×3=27立方厘米，两个结果都正确且完整回答才可以得满分。
部分正确（酌情扣分）：1、计算错误
如果算式列法正确，但在计算过程中出现错误，要根据错误的严重程度扣分。比如在 “商店进了一批文具，铅笔有 50 盒，每盒 10 支，圆珠笔比铅笔少 100 支，圆珠笔有多少支？” 这个题目中，学生正确列出算式50×10-100，但计算50×10时算错为 400，最后得出圆珠笔有 300 支的错误答案，这种情况需要扣掉 30% - 50% 的分数；
2、单位错误
若算式正确、计算结果正确，但单位写错，也应扣分。例如在求面积的题目中，结果正确但单位写成了长度单位，可扣除 5% - 10% 的分数
3、步骤缺失
当应用题需要多步计算时，如果学生省略了关键步骤，但答案正确，要适当扣分。
“0”分标准：1、列式错误
如果学生列出的算式与正确的解题思路完全不符，得零分。比如对于 “小明有 10 个苹果，小红的苹果数是小明的 2 倍，小红有多少个苹果？” 这一问题，学生列式为10÷2，这种情况就判定为完全错误。
2、理解错误
当学生对题目意思理解偏差，导致回答与问题要求无关时，得零分。例如在几何图形求面积的问题中，题目要求求三角形的面积，学生却计算了周长，这种情况得零分。
3、只有答案没有过程也不得分
"},
            { "计算题",@"
满分标准:1、整数计算
对于简单的整数加、减、乘、除运算，如3+5、7-2、4×6、10÷2，算式书写正确，计算结果准确（答案分别是 8、5、24、5），可以得满分。
在多步整数运算中，例如2+3×4-5，按照正确的运算顺序（先乘除后加减），先计算3×4=12，再计算2+12=14，最后14-5=9，且书写规范，能够得满分。
2、小数计算
对于小数的加减法，如3.2+1.5，学生要能正确对齐小数点进行计算，得到4.7为正确结果，可得满分。
在小数乘除法中，如2.5×1.2，按照小数乘法的计算方法，先按照整数乘法计算，然后看因数中一共有两位小数，从积的右边起数出两位点上小数点，得到3.00（化简后为 3），计算正确、步骤完整、书写规范的得满分。
3、分数计算
在分数加减法中，能正确通分，答案正确、过程完整的得满分。
对于分数乘除法，按照分数乘法法则，分子相乘做分子，分母相乘做分母，计算过程和结果都正确的可以得满分。
部分正确（酌情扣分）:""1、计算失误
如果在整数、小数或分数计算过程中出现了简单的计算错误，要根据错误的严重程度扣分。例如在34+27的计算中，学生错算为 51，这种一步计算错误可以扣除 30% - 50% 的分数。
在小数计算中，如1.25×0.8，如果学生在计算过程中忘记点小数点或者点错小数点位置，得到错误结果，可扣除 40% - 60% 的分数。
在分数计算中，学生如果忘记将除法转化为乘法，直接用分子除分子、分母除分母得到错误结果，可扣除 50% - 70% 的分数。
2、步骤缺失
在多步计算或者有规定计算方法的题目中，如果学生省略了必要的步骤，按30%-50%适当扣分。
在简便计算题目中，如果学生没有利用乘法结合律，而是直接按顺序计算，虽然结果正确，但没有体现简便计算的思路，可扣除 20% - 40% 的分数。""
“0”分标准：1、概念性错误
如果学生对基本的计算概念理解错误，如在除法运算中，除数为 0，或者在分数计算中，分母相加，这种基于错误概念的计算得零分。
2、方法完全错误
如果学生采用了完全错误的计算方法，如在乘法运算中用加法的方法计算，或者在分数除法中，完全颠倒了计算规则，得零分。
"},
            { "操作题",@"
满分标准：图形要素准确：如果要求画一个三角形，如直角三角形，三个顶点位置准确，三条边长度符合要求，直角角度绘制准确（用直角符号正确标注）。对于规定边长的图形，如画一个边长为 4 厘米的正方形，四条边长度均为 4 厘米，四个角都是直角，且图形整体形状规则、线条清晰。
部分正确（酌情扣分）：所画图形部分符合要求，如要求画一个等腰三角形，两条边长度相等符合要求，但角度有偏差；或者要求画一个平行四边形，对边平行但长度有轻微差异，可扣除 30% - 50% 的分数。
“0”分标准：所画图形与题目要求相差甚远，如要求画一个长方形，却画成了三角形；或者图形形状扭曲，无法辨认出是要求的图形类型。
"},
            { "开放性问题",@"
满分标准：准确性：学生提供的答案完全符合题目要求，能够准确地解决问题。
完整性：回答涵盖了所有重要的方面或要点，没有遗漏关键信息。
逻辑性：解题思路清晰，逻辑严密，每一步骤都有合理的解释和支持。
创新性：如果适用，学生提出了新颖的解决方案或从不同角度思考问题。
表达清晰：语言表达流畅，术语使用正确，能够清楚地传达自己的想法。
论证充分：如果有需要证明的观点，学生提供了足够的证据或例子来支持其结论。

部分正确（酌情扣分）：部分准确性：虽然答案不是完全正确，但展示了对问题的理解，存在一些正确的元素。
部分完整性：回答中包含了大部分重要信息，可能缺少了一些细节或未考虑到某些方面。
逻辑有缺陷：解题过程中存在一些逻辑上的瑕疵，但整体思路是连贯的。
表达一般：语言表达基本能够理解，但可能存在语法错误或术语使用不当的情况。
论证不充分：提供的证据或例子不足以完全支持其观点，或者论证过程不够详细。

“0”分标准：无关内容：回答与题目要求无关，明显偏离主题。
无实质内容：回答仅包含一些空洞的陈述，没有任何实质性的分析或解答。
完全不理解：显示出对题目要求或背景知识的完全误解。
抄袭：如果检测到学生复制了其他来源的答案而没有自己的思考和贡献。
空白或无效回答：学生没有提供任何有意义的回答，或者是回答无法被理解。
"},
            { "探索与发现题",@"
满分标准：完整的解题过程：学生展示了完整的思考和解题过程，能够清楚地说明每一步骤的理由。
正确的结论：最终得出的答案是正确的，并且与题目要求相符。
合理的推理：学生能够基于给定的信息进行合理推理，提出的猜想或假设是有根据的。
创新的方法：如果题目允许多种解法，学生使用了新颖而有效的解决问题的方法。
清晰的表达：解题过程和结论的表达清晰明了，逻辑连贯，容易理解。
全面的回答：回答涵盖了题目中所有的关键点，没有遗漏重要的信息或步骤。

部分正确（酌情扣分）：部分正确的解题过程：学生展示了解题过程，但存在一些小错误或不完整的地方，比如计算上的失误、推理不够严密等。
接近正确的结论：尽管最终答案不是完全正确，但非常接近正确答案，或者在某些情况下，虽然数值不对，但思路是正确的,扣20%-40%的分数；
基本合理的推理：学生提出了基本合理的猜想或假设，但缺乏充分的证据支持，或者推理过程中有一些漏洞。
表达上有改进空间：解题过程的表达有些混乱，虽然可以理解，但不够清晰或条理不够分明。
部分回答：学生只回答了题目的一部分内容，忽略了其他关键点，但对所涉及的部分有较好的理解和解释。

“0”分标准：无解题过程：学生没有展示任何解题过程，直接给出了答案，或者完全没有回答。
完全错误的结论：最终答案明显错误，与题目要求严重不符，且没有任何合理的推导过程。
无关的推理：学生提出的猜想或假设与题目给定的信息无关，或者完全是错误的。
无法理解的表达：解题过程和结论的表达非常混乱，几乎无法理解。
完全未作答：学生没有尝试解答问题，空白交卷。
"}

        };

        public static string GetTypeIdPrompt(string grade, string subjectId)
        {
            var name = "小学";
            var key = $"{name}{GetSubjectNameById(subjectId)}";
            if (SubjectiveDics.ContainsKey(key))
            {
                return SubjectiveDics[key];
            }

            return string.Empty;
        }

        public static string GetScorePromptByTypeName(string typeName)
        {
            var key = $"{typeName}";
            if (SubjectiveScoreDics.ContainsKey(key))
            {
                return SubjectiveScoreDics[key];
            }

            return string.Empty;
        }

        public static string GetSubjectNameById(string subjectId)
        {
            var subjectName = "";

            if (subjectId=="1")
            {
                subjectName="语文";
            }
            else if (subjectId=="2")
            {
                subjectName="数学";
            }
            else if (subjectId=="3")
            {
                subjectName="英语";
            }

            return subjectName;
        }

        public static string GetPeriodByGrade(string grade)
        {
            var gradeId = int.Parse(grade);
            if (gradeId<6)
                return "小学";

            if (gradeId>=6&&gradeId<=9)
                return "初中";

            if (gradeId>=10&&gradeId<=12)
                return "高中";

            return string.Empty;
        }

    }
}
