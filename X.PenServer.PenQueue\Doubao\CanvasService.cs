﻿using Microsoft.Extensions.Logging;
using SkiaSharp;
using Uwoo.Util.Doubao;
using Uwoo.Util.Obs;

namespace Uwoo.Util
{
    /// <summary>
    /// <para><AUTHOR> <PERSON></para>
    /// <para>@Date   2024-12-20 10:32</para>
    /// <para>@Description Canvas Service</para>
    /// </summary>
    public class CanvasService : ICanvasService
    {
        public static readonly string OCRIMGPATH = Path.Combine(AppContext.BaseDirectory, "ocrimgs");
        readonly IHuaweiCloudObsService _huaweiCloudObsService;
        readonly ILogger<CanvasService> _logger;
        readonly IHttpClientFactory _client_factory;

        public CanvasService(
            ILogger<CanvasService> logger
            , IHuaweiCloudObsService huaweiCloudObsService
            , IHttpClientFactory client_factory
            )
        {
            _logger=logger;
            _huaweiCloudObsService =huaweiCloudObsService;
            _client_factory=client_factory;
        }

        public const string DrawCaptureUploadObsForSubjective_Step_Sign = "subjectivestep";

        /// <summary>
        /// Upload capture to OBS after draw by handwriting
        /// </summary>
        /// <param name="dots"></param>
        /// <param name="markPoints"></param>
        public DrawUploadObsResponse DrawCaptureUploadObsByHandwriting(List<DotDto> dots, List<MarkPointsDto> markPoints)
        {
            var response = new DrawUploadObsResponse();
            var drawfile = DrawHandwriting(dots);
            var captureResponse = Capture(imagePath: drawfile, markPoints: markPoints);

            if (!string.IsNullOrEmpty(drawfile))
            {
                TryDeleteFile(drawfile);
            }

            if (captureResponse.FilePaths?.Count>0)
            {
                captureResponse.FilePaths.ForEach(z =>
                {
                    var url = _huaweiCloudObsService.PutObjectAsync(bucketName: DoubaoOCR.BUCKETNAME, objectName: z.ObjectName, filePath: z.FilePath).ConfigureAwait(false).GetAwaiter().GetResult();
                    TryDeleteFile(z.FilePath);
                    response.ObjectNames.Add(z.ObjectName);
                    response.Urls.Add(url);
                    response.IsSuccess=true;
                });
            }

            return response;
        }

        /// <summary>
        /// Upload capture to OBS after drawing the img | handwriting
        /// </summary>
        /// <param name="imgUrl"></param>
        /// <param name="dots"></param>
        /// <param name="markPoints"></param>
        /// <returns></returns>
        public async Task<DrawUploadObsResponse> DrawCaptureUploadObsByHandwritingForSubjectiveAsync(string imgUrl, List<DotDto> dots, List<MarkPointsDto> markPoints)
        {
            var response = new DrawUploadObsResponse();
            var drawfile = await DrawByImgUrlAsync(imgUrl, dots);
            var captureResponse = Capture(imagePath: drawfile, markPoints: markPoints, sign: DrawCaptureUploadObsForSubjective_Step_Sign);

            if (!string.IsNullOrEmpty(drawfile))
            {
                TryDeleteFile(drawfile);
            }

            if (captureResponse.FilePaths?.Count>0)
            {
                foreach (var z in captureResponse.FilePaths)
                {
                    var url = await _huaweiCloudObsService.PutObjectAsync(bucketName: DoubaoOCR.BUCKETNAME, objectName: z.ObjectName, filePath: z.FilePath);
                    TryDeleteFile(z.FilePath);
                    response.ObjectNames.Add(z.ObjectName);
                    response.Urls.Add(url);
                    response.IsSuccess=true;
                }
            }

            return response;
        }

        public static string GetMarkSignStep(string markId)
        {
            return GetMarkSign(markId, DrawCaptureUploadObsForSubjective_Step_Sign);
        }

        static string GetMarkSign(string markId, string sign)
        {
            return $"{markId}_{sign}";
        }

        static void TryDeleteFile(string path)
        {
            try { File.Delete(path); } catch { }
        }

        /// <summary>
        /// Draw handwriting
        /// </summary>
        /// <param name="dots"></param>
        /// <returns></returns>
        string DrawHandwriting(List<DotDto> dots)
        {
            var finalFile = string.Empty;

            try
            {
                using var bmp = new SKBitmap(1654, 2339);
                using var canvas = new SKCanvas(bmp);
                canvas.Clear(SKColors.White);

                using var font = new SKFont()
                {
                    Size = 100,
                    Typeface=SKTypeface.FromFamilyName("Arial", SKFontStyle.Normal),
                    ForceAutoHinting = true,
                    EmbeddedBitmaps = true,
                    LinearMetrics = true,
                };

                using var paint = new SKPaint()
                {
                    Color = SKColors.Blue,
                    StrokeWidth = 1.6F,
                    IsAntialias=true,
                    IsStroke=true,
                    IsDither= true,
                };

                using var spath = new SKPath();
                var first = dots.FirstOrDefault();
                if (first == null)
                {
                    return string.Empty;
                }

                spath.MoveTo(first.X, first.Y);
                var ismove = false;
                foreach (var item in dots)
                {
                    if (ismove)
                    {
                        spath.MoveTo(item.X, item.Y);
                        ismove = false;
                    }

                    spath.LineTo(item.X, item.Y);
                    if (item.Type != 2)
                    {
                        continue;
                    }
                    spath.MoveTo(item.X, item.Y);
                    spath.Close();
                    canvas.Save();
                    ismove = true;
                }
                canvas.DrawPath(spath, paint);
                canvas.Save();

                using var image = SKImage.FromBitmap(bmp);
                var imgid = Guid.NewGuid().ToString("N") + ".jpg";
                if (!Directory.Exists(OCRIMGPATH))
                {
                    Directory.CreateDirectory(OCRIMGPATH);
                }
                finalFile = Path.Combine(OCRIMGPATH, imgid);
                using var files = new FileStream(finalFile, FileMode.OpenOrCreate, FileAccess.ReadWrite);
                image.Encode(SKEncodedImageFormat.Jpeg, 80).SaveTo(files);
            }
            catch (Exception ex)
            {
                _logger.LogError("DrawHandwriting Ex:{}", ex.ToString());
            }

            return finalFile;
        }

        /// <summary>
        /// Capture area
        /// </summary>
        /// <param name="imagePath"></param>
        /// <param name="markPoints"></param>
        /// <param name="sign"></param>
        /// <returns></returns>
        static CaptureResponse Capture(string imagePath, List<MarkPointsDto> markPoints, string sign = "")
        {
            var response = new CaptureResponse();

            if (markPoints==null||markPoints.Count==0)
            {
                return response;
            }

            using var originalBitmap = SKBitmap.Decode(imagePath);

            foreach (var mark in markPoints)
            {
                var xy = mark.XYs;
                var width = xy[1].X-xy[0].X;
                var height = xy[2].Y-xy[0].Y;
                var x = xy[0].X;
                var y = xy[0].Y;

                var croppedBitmap = new SKBitmap(width, height);

                using var canvas = new SKCanvas(croppedBitmap);
                var cropRect = new SKRect(x, y, x + width, y + height);
                canvas.DrawBitmap(originalBitmap, cropRect, new SKRect(0, 0, width, height));

                var imgid = $"{GetMarkSign(mark.MarkId, sign)}_"+Guid.NewGuid().ToString("N") + ".jpg";
                var finalFile = Path.Combine(OCRIMGPATH, imgid);
                using var files = new FileStream(finalFile, FileMode.OpenOrCreate, FileAccess.ReadWrite);
                croppedBitmap.Encode(SKEncodedImageFormat.Jpeg, 80).SaveTo(files);
                response.FilePaths.Add(new FilePathBase { FilePath=finalFile, ObjectName=imgid });
            }

            return response;
        }

        /// <summary>
        /// Draw By ImgURL
        /// </summary>
        /// <param name="imgUrl"></param>
        /// <returns></returns>
        async Task<string> DrawByImgUrlAsync(string imgUrl)
        {
            var finalFile = string.Empty;

            SKBitmap bmp = await GetSKBitmapByImgUrlAsync(imgUrl);
            if (bmp == null)
                return finalFile;

            try
            {
                using var canvas = new SKCanvas(bmp);

                using var font = new SKFont()
                {
                    Size = 100,
                    Typeface=SKTypeface.FromFamilyName("Arial", SKFontStyle.Normal),
                    ForceAutoHinting = true,
                    EmbeddedBitmaps = true,
                    LinearMetrics = true,
                };

                using var paint = new SKPaint()
                {
                    Color = SKColors.Blue,
                    StrokeWidth = 1.6F,
                    IsAntialias=true,
                    IsStroke=true,
                    IsDither= true,
                };

                paint.Color = SKColors.Black;
                paint.Style = SKPaintStyle.Stroke;
                canvas.Save();
                canvas.Restore();

                using var image = SKImage.FromBitmap(bmp);
                var imgid = Guid.NewGuid().ToString("N") + ".jpg";
                if (!Directory.Exists(OCRIMGPATH))
                {
                    Directory.CreateDirectory(OCRIMGPATH);
                }
                finalFile = Path.Combine(OCRIMGPATH, imgid);
                using var files = new FileStream(finalFile, FileMode.OpenOrCreate, FileAccess.ReadWrite);
                image.Encode(SKEncodedImageFormat.Jpeg, 80).SaveTo(files);
            }
            catch (Exception ex)
            {
            }

            return finalFile;
        }

        /// <summary>
        /// Draw By ImgURL | DOTS
        /// </summary>
        /// <param name="imgUrl"></param>
        /// <param name="dots"></param>
        /// <returns></returns>
        async Task<string> DrawByImgUrlAsync(string imgUrl, List<DotDto> dots)
        {
            var finalFile = string.Empty;

            SKBitmap bmp = await GetSKBitmapByImgUrlAsync(imgUrl);
            if (bmp == null)
                return finalFile;

            try
            {
                using var canvas = new SKCanvas(bmp);

                using var font = new SKFont()
                {
                    Size = 100,
                    Typeface=SKTypeface.FromFamilyName("Arial", SKFontStyle.Normal),
                    ForceAutoHinting = true,
                    EmbeddedBitmaps = true,
                    LinearMetrics = true,
                };

                using var paint = new SKPaint()
                {
                    Color = SKColors.Blue,
                    StrokeWidth = 1.6F,
                    IsAntialias=true,
                    IsStroke=true,
                    IsDither= true,
                };

                paint.Color = SKColors.Black;
                paint.Style = SKPaintStyle.Stroke;
                canvas.Save();
                canvas.Restore();

                using var spath = new SKPath();
                var first = dots.FirstOrDefault();
                if (first == null)
                {
                    return string.Empty;
                }

                spath.MoveTo(first.X, first.Y);
                var ismove = false;
                foreach (var item in dots)
                {
                    if (ismove)
                    {
                        spath.MoveTo(item.X, item.Y);
                        ismove = false;
                    }

                    spath.LineTo(item.X, item.Y);
                    if (item.Type != 2)
                    {
                        continue;
                    }
                    spath.MoveTo(item.X, item.Y);
                    spath.Close();
                    canvas.Save();
                    ismove = true;
                }
                canvas.DrawPath(spath, paint);
                canvas.Save();
                canvas.Restore();

                using var image = SKImage.FromBitmap(bmp);
                var imgid = Guid.NewGuid().ToString("N") + ".jpg";
                if (!Directory.Exists(OCRIMGPATH))
                {
                    Directory.CreateDirectory(OCRIMGPATH);
                }
                finalFile = Path.Combine(OCRIMGPATH, imgid);
                using var files = new FileStream(finalFile, FileMode.OpenOrCreate, FileAccess.ReadWrite);
                image.Encode(SKEncodedImageFormat.Png, 80).SaveTo(files);
            }
            catch (Exception ex)
            {
            }

            return finalFile;
        }

        /// <summary>
        /// SKBitmap
        /// </summary>
        /// <param name="imgUrl"></param>
        /// <returns></returns>
        async Task<SKBitmap> GetSKBitmapByImgUrlAsync(string imgUrl)
        {
            SKBitmap bmp = null;
            using var client = _client_factory.CreateClient();
            var response = await client.GetAsync(imgUrl);
            if (response.IsSuccessStatusCode)
            {
                using var stream = await response.Content.ReadAsStreamAsync();
                bmp= SKBitmap.Decode(stream);
            }

            return bmp;
        }

    }
}
