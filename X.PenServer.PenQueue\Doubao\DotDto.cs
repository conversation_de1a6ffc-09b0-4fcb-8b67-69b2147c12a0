﻿namespace Uwoo.Util
{
    public class DotDto
    {
        public int X { get; set; }

        public int Y { get; set; }

        public int Type { get; set; }

        public long Oid { get; set; }

        public DateTime CreateTime { get; set; }
    }

    public class DrawUploadObsResponse
    {
        public bool IsSuccess { get; set; }

        public List<string> ObjectNames { get; set; } = new();

        public List<string> Urls { get; set; } = new();
    }

    public class CaptureResponse
    {
        public List<FilePathBase> FilePaths { get; set; } = new();
    }

    public class FilePathBase
    {
        public string FilePath { get; set; }

        public string ObjectName { get; set; }
    }

    public class MarkPointsDto
    {
        public string MarkId { get; set; }

        public List<XY> XYs { get; set; }
    }

    public class ObjectNamesGroupDto
    {
        public DateTime CreateTime { get; set; }

        public List<string> ObjectNames { get; set; } = new();
    }

    public class XY
    {
        public int X { get; set; }
        public int Y { get; set; }

    }
}
