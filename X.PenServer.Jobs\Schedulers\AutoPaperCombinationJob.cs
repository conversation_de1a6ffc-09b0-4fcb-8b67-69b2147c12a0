﻿// -- Function: AutoPaperCombinationJob.cs
// --- Project: X.PenServer.Jobs
// ---- Remark: 
// ---- Author: Lucifer
// ------ Date: 2024/06/05 13:06

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Quartz;
using StackExchange.Redis;
using X.PenServer.Contracts.Paper;
using X.PenServer.Contracts.Redis;
using X.PenServer.Interfaces;
using X.PenServer.Interfaces.Redis;
using X.PenServer.Jobs.Implement;

namespace X.PenServer.Jobs.Schedulers;

public class AutoPaperCombinationJob : JobBase
{
    private readonly ILogger<AutoPaperCombinationJob> _log;
    private readonly IDapperService _dapper;
    private readonly IPaperInfoRedisService _paper_info_redis_service;
    private readonly ParallelOptions _parallel_options;

    /// <inheritdoc />
    public AutoPaperCombinationJob(ILogger<AutoPaperCombinationJob> log, IDapperService dapper, IPaperInfoRedisService paper_info_redis_service, ParallelOptions parallel_options , IConfiguration config)
    {
        _log = log;
        _dapper = dapper;
        _paper_info_redis_service = paper_info_redis_service;
        _parallel_options = parallel_options;

        var type = GetType();
        var time = DateTime.Today.AddHours(2);
        var is_immediate = config.GetValue<bool>("Immediate");
        if (is_immediate)
        {
            time = DateTime.Now;
        }
        JobDetail = JobBuilder.Create(type).WithIdentity(type.Name).Build();
        Trigger = TriggerBuilder.Create()
            .WithIdentity(type.Name)
            .StartAt(new DateTimeOffset(time))
            .WithSimpleSchedule
            (
                x =>
                {
                    x.WithInterval(TimeSpan.FromDays(15)).RepeatForever();
                }
            )
            .Build();
        ScheduleJob = async scheduler =>
        {
            var is_exist = await scheduler.CheckExists(JobDetail.Key);
            if (!is_exist)
            {
                await scheduler.ScheduleJob(JobDetail, Trigger).ConfigureAwait(false);
            }
        };
    }

    #region Overrides of QuartzBackgroundWorkerBase

    /// <inheritdoc />
    public override async Task Execute(IJobExecutionContext context)
    {
        try
        {
            var token = context.CancellationToken;
            const string comsql = """
                                  SELECT DISTINCT
                                         [PageId]
                                  FROM [dbo].[WorkbookPage]
                                  WHERE [IsCombination] = 1
                                  ORDER BY [PageId] ASC;
                                  """;
            var pageids = await _dapper.QueryListAsync<WorkbookPage>(comsql);
            _parallel_options.CancellationToken = token;
            await Parallel.ForEachAsync(pageids, _parallel_options, async (x, _) =>
            {
                var iscombina = await _paper_info_redis_service.HGetPaperInfoIsCombinationAsync(x.PageId);
                if (!iscombina)
                {
                    await _paper_info_redis_service.HSetPaperInfoCombinationAsync(x.PageId);
                }
            });

            const string sql = """
                               SELECT [Id] AS [WorkPageId],
                                      [PaperId],
                                      [Page],
                                      [PageId]
                               FROM [dbo].[WorkbookPage]
                               WHERE [IsCombination] = 1
                               ORDER BY [PageId] ASC;
                               """;
            var list = await _dapper.QueryListAsync<WorkbookPage>(sql);
            await Parallel.ForEachAsync(list, _parallel_options, async (x, _) =>
            {
                const string xsql = """
                                    SELECT [b].[Id] AS [MarkId],
                                           [b].[WorkbookPageId] AS [WorkPageId],
                                           [a].[PaperId],
                                           [a].[Page],
                                           [b].[Range]
                                    FROM [dbo].[WorkbookPage] AS a
                                        INNER JOIN [dbo].[WorkbookMarkInfo] AS b
                                            ON [a].[Id] = [b].[WorkbookPageId]
                                    WHERE [a].[IsCombination] = 1
                                          AND [b].[Deleted] = 0
                                          AND [b].[QuestionLevel] = 2
                                          AND [b].[Range] IS NOT NULL
                                          AND [b].[Range] <> '[]'
                                          AND [a].[PageId] = @pageid;
                                    """;
                var xlist = await _dapper.QueryListAsync<CombinationMarkRangeInfo>(xsql, new
                {
                    pageid = x.PageId
                });
                await _paper_info_redis_service.RemoveAsync($"PaperPageIdCombinationRange|{x.PageId}", flags: CommandFlags.FireAndForget);
                await _paper_info_redis_service.SAddCombinationMarkRangeInfoListAsync(x.PageId, xlist.ToList());
                _log.LogInformation("update combination: {pageid}", x.PageId.ToString());
            });
            _log.LogInformation("update combination cache finished");
        }
        catch (Exception e)
        {
            _log.LogCritical("error: {error}", e.StackTrace ?? e.Message);
        }
    }

    #endregion
}