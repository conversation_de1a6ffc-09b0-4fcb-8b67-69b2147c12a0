<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="author" content="朱春锋">
    <meta name="date" content="2025-06-04">
    <title>PenServer-WebSocket-Tool 测试工具</title>
    <style>
       
        footer.simple-footer {
            background-color: #f5f5f5;
            text-align: center;        
            padding: 5px 0; 
            color: #999;          
            font-size: 12px;           
            border-top: 1px solid #eee;
        }
    </style>
</head>
<body>
    <h2>PenServer-WebSocket-Tool 测试工具</h2>
    <div>
        <label for="serverUri">服务器 URI:</label>
        <input type="text" id="serverUri" value="ws://127.0.0.1:8080/Echo" size="50">
        <button id="connectBtn">连接</button>
        <button id="disconnectBtn" disabled>断开</button>
        <span id="connectionStatus">未连接</span>
    </div>
    
    <div>
        <label for="dataFormat">数据格式:</label>
        <select id="dataFormat">
            <option value="hex">十六进制</option>
        </select>
        <button id="sendBtn" disabled>发送</button>
    </div>
    
    <div>
        <label for="dataInput">发送数据:</label>
        <br>
        <textarea id="dataInput" rows="4" cols="80" placeholder="输入要发送的数据...">abba1234567890AA</textarea>
    </div>
    
    <div>
        <button id="loadPreset1">预设1</button>
        <button id="loadPreset2">预设2</button>
        <button id="loadPreset3">预设3</button>
        <button id="clearLogBtn">清空日志</button>
    </div>
    
    <div>
        <label>通信日志:</label>
        <br>
        <textarea id="logOutput" rows="15" cols="80" readonly>等待连接到服务器...</textarea>
    </div>

    <p>
       测试： ws://pen.test.eduwon.cn:5008/wifi
       <br/>
       生产： ws://wifi.eduwon.cn:5008/wifi
       <br/>
       实验测试： ws://wifi2.eduwon.cn:5018/wifi
    </p>

    <script>
        // 预设数据
        const presets = [
            "abba1234567890AA", // Pen Connect
            "fffe002800024f4b80a4fffd", // OfflineTaskReady
            "fffe130c8e7914bb014008c4ffc9ffb472cffffdfffe130c8e5d1499014000de00080048d0d7fffdfffe130c8e67152d014000e3fffa00d07c03fffdfffe130c8e6115f1014000e2fffe0098342ffffdfffe130c8e601673014000d1fffc00381dcafffdfffe130c8e5f168d01400077fffdffe097fbfffdfffe12080e5e16490140000170a4fffdfffe800800040000005f4c30fffdfffec00800040000000011fafffd" // 扩展数据示例
        ];
        
        let websocket = null;
        let isConnected = false;
        
        // DOM 元素
        const serverUriInput = document.getElementById('serverUri');
        const dataInput = document.getElementById('dataInput');
        const dataFormatSelect = document.getElementById('dataFormat');
        const connectBtn = document.getElementById('connectBtn');
        const disconnectBtn = document.getElementById('disconnectBtn');
        const sendBtn = document.getElementById('sendBtn');
        const connectionStatus = document.getElementById('connectionStatus');
        const logOutput = document.getElementById('logOutput');
        const clearLogBtn = document.getElementById('clearLogBtn');
        const loadPresetBtns = [
            document.getElementById('loadPreset1'),
            document.getElementById('loadPreset2'),
            document.getElementById('loadPreset3')
        ];
        
        // 日志函数
        function addLog(message) {
            const now = new Date();
            const timeString = now.toLocaleTimeString();
            logOutput.value += `[${timeString}] ${message}\n`;
            logOutput.scrollTop = logOutput.scrollHeight;
        }
        
        // 十六进制字符串转Uint8Array
        function hexToBytes(hexStr) {
            const cleanHex = hexStr.replace(/\s/g, '');
            const bytes = new Uint8Array(cleanHex.length / 2);
            for (let i = 0; i < cleanHex.length; i += 2) {
                bytes[i/2] = parseInt(cleanHex.substr(i, 2), 16);
            }
            return bytes;
        }
        
        // 连接到服务器
        function connect() {
            const uri = serverUriInput.value;
            addLog(`正在连接到 ${uri}...`);
            
            try {
                websocket = new WebSocket(uri);
                
                websocket.onopen = function(event) {
                    isConnected = true;
                    updateUI();
                    addLog(`已连接到 ${uri}`);
                };
                
                websocket.onmessage = function(event) {
                    let message = event.data;
                    
                    // 如果是二进制数据，转换为十六进制字符串
                    if (event.data instanceof ArrayBuffer) {
                        const bytes = new Uint8Array(event.data);
                        message = Array.from(bytes)
                            .map(byte => byte.toString(16).padStart(2, '0'))
                            .join(' ');
                    }
                    
                    addLog(`接收: ${message}`);
                };
                
                websocket.onerror = function(error) {
                    addLog(`连接错误: ${error.message}`);
                };
                
                websocket.onclose = function(event) {
                    isConnected = false;
                    updateUI();
                    addLog(`连接已关闭 (代码: ${event.code})`);
                };
            } catch (error) {
                addLog(`连接异常: ${error.message}`);
            }
        }
        
        // 断开连接
        function disconnect() {
            if (websocket) {
                addLog('正在断开连接...');
                websocket.close();
            }
        }
        
        // 发送数据
        function sendData() {
            if (!websocket || websocket.readyState !== WebSocket.OPEN) {
                addLog('未连接到服务器');
                return;
            }
            
            const data = dataInput.value.trim();
            if (!data) {
                addLog('数据不能为空');
                return;
            }
            
            try {
                let dataToSend;
                
                if (dataFormatSelect.value === 'hex') {
                    // 十六进制格式
                    dataToSend = hexToBytes(data);
                    addLog(`发送: ${data.replace(/(.{2})/g, '$1 ')}`);
                } else {
                    // 文本格式
                    dataToSend = data;
                    addLog(`发送: ${data}`);
                }
                
                websocket.send(dataToSend);
            } catch (error) {
                addLog(`发送失败: ${error.message}`);
            }
        }
        
        // 更新UI状态
        function updateUI() {
            if (isConnected) {
                connectionStatus.textContent = '已连接';
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
                sendBtn.disabled = false;
            } else {
                connectionStatus.textContent = '未连接';
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
                sendBtn.disabled = true;
            }
        }
        
        // 加载预设数据
        function loadPreset(index) {
            dataInput.value = presets[index];
        }
        
        // 清空日志
        function clearLog() {
            logOutput.value = '日志已清空\n';
        }
        
        // 事件监听器
        connectBtn.addEventListener('click', connect);
        disconnectBtn.addEventListener('click', disconnect);
        sendBtn.addEventListener('click', sendData);
        clearLogBtn.addEventListener('click', clearLog);
        
        loadPresetBtns.forEach((btn, index) => {
            btn.addEventListener('click', () => loadPreset(index));
        });
        
        // 按Enter发送数据
        dataInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey && isConnected) {
                e.preventDefault();
                sendData();
            }
        });
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateUI();
        });
    </script>

    <footer class="simple-footer">
        <p>作者：朱春锋  日期：2025-06-04</p>
    </footer>
</body>
</html>
    