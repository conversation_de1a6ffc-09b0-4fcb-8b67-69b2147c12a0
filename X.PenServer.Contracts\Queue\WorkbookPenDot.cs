﻿// -- Function: WorkbookPenDot.cs
// --- Project: X.PenServer.Contracts
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2024/3/21 13:21
namespace X.PenServer.Contracts.Queue;

using System.Text.Json.Serialization;

/// <summary>
/// 练习薄点位
/// </summary>
public class WorkbookPenDot : PenDot
{
	/// <summary>
	/// 是否为整行
	/// </summary>
	[JsonPropertyName(nameof(IsFullLine))]
	[JsonInclude]
	public bool IsFullLine { get; set; }

	/// <summary>
	/// 行号
	/// </summary>
	[JsonPropertyName(nameof(LineNo))]
	[JsonInclude]
	public int LineNo { get; set; }

	/// <summary>
	/// 当前空索引
	/// </summary>
	/// <remarks>非整行生效</remarks>
	[JsonPropertyName(nameof(Blank))]
	[JsonInclude]
	public int Blank { get; set; }
}