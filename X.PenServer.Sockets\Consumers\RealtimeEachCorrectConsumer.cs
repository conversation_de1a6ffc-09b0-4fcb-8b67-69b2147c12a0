﻿// -- Function: RealtimeEachCorrectConsumer.cs
//  --- Project: X.PenServer.Sockets
//  ---- Remark: 
//  ---- Author: Lucifer
//  ------ Date: 2024/05/30 14:05

using System.Collections.Immutable;
using System.Net.WebSockets;
using MassTransit;
using X.PenServer.Contracts.Realtime;
using X.PenServer.Contracts.Socket;
using X.PenServer.Infrastructure;
using static X.PenServer.Middlewares.MiddlewareExtensions;
using static X.PenServer.Middlewares.RealtimePenSocketMiddleware;

namespace X.PenServer.Sockets.Consumers;

/// <summary>
/// 互批
/// </summary>
public class RealtimeEachCorrectConsumer : IConsumer<Batch<RealtimeEachCorrectModel>>
{
    private readonly ParallelOptions _parallel_options;

    public RealtimeEachCorrectConsumer(ParallelOptions parallel_options)
    {
        _parallel_options = parallel_options;
    }

    #region Implementation of IConsumer<in Batch<RealtimeEachCorrectModel>>

    /// <inheritdoc />
    public async Task Consume(ConsumeContext<Batch<RealtimeEachCorrectModel>> context)
    {
        foreach (var item in context.Message)
        {
            var data = item.Message;
            //学生笔迹
            var wids = GetStudentClients(data.PaperId, data.ByStudentId);
            if (wids is null || wids.IsEmpty)
            {
                continue;
            }

            var sockets = ImmutableHashSet.Create<WebSocket>();
            sockets = wids.Select(GetSocketClient).Where(socket => socket is
            {
                State: WebSocketState.Open
            }).Aggregate(sockets, (current, socket) => current.Add(socket));
            if (sockets.Count <= 0)
            {
                continue;
            }

            await Parallel.ForEachAsync
            (
                sockets, _parallel_options, async (x, xtoken) =>
                {
                    var xdata = new SendData
                    {
                        Type = "301",
                        Content = data.ToJsonString(),
                        ClassId = data.ClassId
                    };
                    await SendAsync(x, xdata.ToJsonString(), xtoken).ConfigureAwait(false);
                }
            );
        }

        await context.ConsumeCompleted.ConfigureAwait(false);
    }

    #endregion
}