﻿//  -- Function: PenMappingService.cs
//  --- Project: X.PenServer.Services
//  ---- Remark:
//  ---- Author: Lucifer
//  ------ Date: 2023/10/25 01:32:06


namespace X.PenServer.Services.Mongo;

/// <inheritdoc cref="IPenMappingService" />
public class PenMappingService : MongoService<PenMapping>, IPenMappingService
{
    /// <inheritdoc />
    public PenMappingService(IMongoConfig config) : base(config)
    {
    }

	/// <inheritdoc />
	protected override void CreateIndex(IMongoCollection<PenMapping> collection)
	{
		var userid_builder = Builders<PenMapping>.IndexKeys
			.Ascending(x => x.UserId)
			.Ascending(x => x.Mac)
			.Ascending(x => x.AddTime);
		collection.Indexes.CreateIndex(userid_builder, collection.CollectionNamespace.CollectionName + "_UserId_Key");

		var mac_builder = Builders<PenMapping>.IndexKeys
			.Ascending(x => x.Mac)
			.Ascending(x => x.UserId)
			.Ascending(x => x.AddTime);
		collection.Indexes.CreateIndex(mac_builder, collection.CollectionNamespace.CollectionName + "_Mac_Key");
	}
}