﻿// -- Function: ParameterExpressionVisitor.cs
// --- Project: X.PenServer.Infrastructure
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2024/3/20 11:14
namespace X.PenServer.Infrastructure;

using System.Linq.Expressions;

/// <inheritdoc />
public class ParameterExpressionVisitor: ExpressionVisitor
{
	private readonly Dictionary<ParameterExpression, ParameterExpression> _map;

	private ParameterExpressionVisitor(Dictionary<ParameterExpression, ParameterExpression> map)
	{
		_map = map ?? new Dictionary<ParameterExpression, ParameterExpression>();
	}

	/// <summary>
	/// 替换参数
	/// </summary>
	/// <param name="map"></param>
	/// <param name="expression"></param>
	/// <returns></returns>
	public static Expression ReplaceParameters(Dictionary<ParameterExpression, ParameterExpression> map, Expression expression)
	{
		return new ParameterExpressionVisitor(map).Visit(expression);
	}

	/// <inheritdoc />
	protected override Expression VisitParameter(ParameterExpression parameter_expression)
	{
		if (_map.TryGetValue(parameter_expression, out var replacement))
		{
			parameter_expression = replacement;
		}
		return base.VisitParameter(parameter_expression);
	}
}