﻿//  -- Function: PenConfig.cs
//  --- Project: PenServer
//  ---- Remark: 
//  ---- Author: Lucifer
//  ------ Date: 2023/03/09 9:46

using System.Text.Json.Serialization;

namespace X.PenServer.Contracts.Config;

/// <summary>
/// 笔配置
/// </summary>
public class PenConfig : IPenConfig
{
    /// <inheritdoc />
    [JsonInclude]
    [JsonPropertyName(nameof(PenServerPort))]
    public int PenServerPort { get; set; }

    /// <inheritdoc />
    [JsonInclude]
    [JsonPropertyName(nameof(IsAutoAll))]
    public bool IsAutoAll { get; set; }

    /// <inheritdoc />
    [JsonInclude]
    [JsonPropertyName(nameof(StandBy))]
    public int StandBy { get; set; }

    /// <inheritdoc />
    [JsonInclude]
    [JsonPropertyName(nameof(Firmwares))]
    public List<Firmware> Firmwares { get; set; }

    /// <inheritdoc />
    [JsonInclude]
    [JsonPropertyName(nameof(IsFix))]
    public bool IsFix { get; set; }
}