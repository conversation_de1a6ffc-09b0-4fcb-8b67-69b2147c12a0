﻿//  -- Function: PenUtils.cs
//  --- Project: PenServer
//  ---- Remark:
//  ---- Author: Lucifer
//  ------ Date: 2023/02/22 14:05

using System.Globalization;
using System.Text;

namespace X.PenServer.Infrastructure;

public static class PenUtils
{
    public static string LongToString(this ulong mac)
    {
        var result = mac.ToString("X12");
        result = result.Insert(2, ":");
        result = result.Insert(5, ":");
        result = result.Insert(8, ":");
        result = result.Insert(11, ":");
        result = result.Insert(14, ":");
        return result;
    }

    public static string BytesToString(this byte[] bytes)
    {
        var sb = new StringBuilder(bytes.Length * 2);
        foreach (var item in bytes)
        {
            sb.Append($"{item:x2}");
        }

        return sb.ToString();
    }

    public static ulong StringToLong(this string mac)
    {
        var result = ulong.Parse(mac.Replace(":", ""), NumberStyles.HexNumber);
        return result;
    }

    public static string GetMac(this ulong mac)
    {
        return mac.ToString("X12");
    }

    public static string ToMac(this string mac)
    {
        return mac.Replace(":", "");
    }

    public static string ToLongDate(this DateTime date)
    {
        return date.ToString("yyyy-MM-dd HH:mm:ss.fff");
    }
}