﻿<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Publish X.PenServer.PenQueue" type="DotNetFolderPublish" factoryName="Publish to folder" singleton="false">
    <riderPublish configuration="Release" delete_existing_files="true" platform="Any CPU" runtime="win-x64" target_folder="../Publish/PenQueue" target_framework="net8.0" uuid_high="1660509276703967340" uuid_low="-4643129461543393841" />
    <method v="2" />
  </configuration>
</component>