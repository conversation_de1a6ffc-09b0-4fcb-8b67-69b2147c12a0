﻿//  -- Function: PenVersionData.cs
//  --- Project: PenServer
//  ---- Remark:
//  ---- Author: Lucifer
//  ------ Date: 2023/03/31 15:10

using System.Text.Json.Serialization;

namespace X.PenServer.Contracts;

/// <summary>
/// 版本号数据
/// </summary>
[Serializable]
public class PenVersionData
{
	/// <summary>
	/// 固件版本号
	/// </summary>
	[JsonPropertyName(nameof(Version))]
	[JsonInclude]
	public string Version { get; set; }

	/// <summary>
	/// 固件原始版本号
	/// </summary>
	[JsonPropertyName(nameof(RawVersion))]
	[JsonInclude]
	public string RawVersion { get; set; }
}