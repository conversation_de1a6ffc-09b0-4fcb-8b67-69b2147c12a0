﻿// -- Function: RealtimeMonitorMiddleware.cs
// --- Project: X.PenServer.Middlewares
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2023/11/30 22:50:21
namespace X.PenServer.Middlewares;

using System.Collections.Concurrent;
using System.IdentityModel.Tokens.Jwt;
using System.Net.WebSockets;
using System.Text;
using System.Text.Json;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using Microsoft.Net.Http.Headers;
using Contracts;
using Contracts.Config;
using Interfaces;
using Infrastructure;
using static MiddlewareExtensions;

/// <summary>
/// 实时监控
/// </summary>
public class RealtimeMonitorMiddleware : IMiddleware
{
	private readonly IPenService _service;
	private readonly ILogger<RealtimeMonitorMiddleware> _log;
	private readonly ISsoConfig _sso_config;

	/// <summary>
	/// 所有WebSocket客户端
	/// </summary>
	public static ConcurrentDictionary<string, WebSocket> AllMonitorClients { get; } = new();

	public RealtimeMonitorMiddleware(IPenService service, ILogger<RealtimeMonitorMiddleware> log, ISsoConfig sso_config)
	{
		_service = service;
		_log = log;
		_sso_config = sso_config;
	}

	/// <inheritdoc />
	public async Task InvokeAsync(HttpContext context, RequestDelegate next)
	{
		if (!context.WebSockets.IsWebSocketRequest)
		{
			await next(context);
			return;
		}

		if (!context.Request.Path.Equals("/monitor", StringComparison.OrdinalIgnoreCase))
		{
			await next(context);
			return;
		}

		//JsonWebToken验证
		var ispass = context.Request.Headers.TryGetValue(HeaderNames.Authorization, out var jtoken);
		if (!ispass || string.IsNullOrWhiteSpace(jtoken))
		{
			await next(context);
			return;
		}

		var xtoken = jtoken.ToString();
		if (string.IsNullOrWhiteSpace(xtoken))
		{
			await next(context);
			return;
		}

		xtoken = xtoken.Replace("Bearer ", "");
		var key_bytes = Encoding.UTF8.GetBytes(_sso_config.SigningKey);
		var validate_param = new TokenValidationParameters
		{
			ValidateIssuer = true,
			ValidateAudience = true,
			ValidateIssuerSigningKey = true,
			ValidAudience = _sso_config.Audience,
			ValidIssuer = _sso_config.Issuer,
			IssuerSigningKey = new SymmetricSecurityKey(key_bytes)
		};
		var validate_result = await new JwtSecurityTokenHandler().ValidateTokenAsync(xtoken, validate_param);
		if (validate_result is not {IsValid: true})
		{
			await next(context);
			return;
		}

		var socket = await context.WebSockets.AcceptWebSocketAsync
		(
			new WebSocketAcceptContext
			{
				KeepAliveInterval = TimeSpan.FromSeconds(60),
				DangerousEnableCompression = false
			}
		);
		var wid = Guid.NewGuid().ToString("N");
		if (!AllMonitorClients.ContainsKey(wid))
		{
			AllMonitorClients.TryAdd(wid, socket);
		}

		var token = context.RequestAborted;
		try
		{
			while (!token.IsCancellationRequested)
			{
				if (socket.State != WebSocketState.Open && socket.State != WebSocketState.Connecting)
				{
					break;
				}

				var response = await ReceiveAsync(socket, token);
				if (string.IsNullOrWhiteSpace(response))
				{
					continue;
				}

				var xdata = JsonSerializer.Deserialize<PenMonitorData>(response);
				if (xdata == null)
				{
					continue;
				}

				switch (xdata.TypeId)
				{
					case 1:
					{
						var zdata = new PenMonitorData
						{
							TypeId = 1,
							Message = "pong"
						};
						await SendAsync(socket, zdata.ToJsonString(), token);
						break;
					}
					case 2:
					{
						var wdata = xdata.Content;
						await SendAsync(socket, wdata.ToJsonString(), token);
						break;
					}
					case 3:
					{
						var list = _service.GetCurrentList();
						await SendAsync(socket, list.ToJsonString(), token);
						break;
					}
				}
			}

			AllMonitorClients.TryRemove(wid);
		}
		catch (Exception e)
		{
			_log.LogCritical(e, "error: {error}", e.Message);
		}
		finally
		{
			if (socket.State != WebSocketState.Closed)
			{
				socket.Abort();
				socket.Dispose();
			}
		}
	}
}