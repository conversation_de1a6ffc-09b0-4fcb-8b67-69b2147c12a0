﻿// -- Function: IMyScriptClient.cs
// --- Project: X.PenServer.Interfaces
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2023/11/13 9:47

namespace X.PenServer.Interfaces;

using Contracts.Queue;
using Lifecycle;

/// <summary>
/// MyScript客户端
/// </summary>
public interface IMyScriptClient : ISingletonService
{
	/// <summary>
	/// 卷码识别
	/// </summary>
	/// <param name="dots">点位列表</param>
	/// <returns></returns>
	Task<int> NumberRecognition(List<PenDot> dots);
}