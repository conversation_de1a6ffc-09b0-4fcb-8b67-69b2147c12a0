﻿// -- Function: ISingleItemPenLogService.cs
// --- Project: X.PenServer.Interfaces
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2024/04/04 15:04:47

using X.PenServer.Interfaces.Lifecycle;
using X.PenServer.Models.Mongo;

namespace X.PenServer.Interfaces.Mongo;

/// <summary>
/// 单题模式
/// </summary>
public interface ISingleItemPenLogService: IMongoAutoService<SingleItemPenLog>, ISingletonService
{
}