﻿// -- Function: SubjectiveAnswerResultData.cs
// --- Project: X.PenServer.Contracts
// ---- Remark: 
// ---- Author: Lucifer
// ------ Date: 2024/1/31 16:31
namespace X.PenServer.Contracts.Queue;

using System.Text.Json.Serialization;

/// <summary>
/// 主观题作答教师批阅结果
/// </summary>
/// <remarks>英语单元答题卡</remarks>
public class EnglishUnitSubjectiveAnswerResultData : AnswerResultData
{
	/// <summary>
	/// 子题目编号
	/// </summary>
	/// <remarks>一道题目多个空</remarks>
	[JsonPropertyName(nameof(SubItemNo))]
	[JsonInclude]
	public int SubItemNo { get; set; }

	/// <summary>
	/// 教师id
	/// </summary>
	[JsonPropertyName(nameof(TeacherId))]
	[JsonInclude]
	public string TeacherId { get; set; }

	/// <summary>
	/// 学生id
	/// </summary>
	[JsonPropertyName(nameof(StudentId))]
	[JsonInclude]
	public string StudentId { get; set; }

	/// <summary>
	/// 学生编号
	/// </summary>
	[JsonPropertyName(nameof(StudentNo))]
	[JsonInclude]
	public string StudentNo { get; set; }
}