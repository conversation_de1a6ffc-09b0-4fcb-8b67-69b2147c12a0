﻿//  -- Function: ReceiveData.cs
//  --- Project: PenServer
//  ---- Remark: 
//  ---- Author: Lucifer
//  ------ Date: 2023/04/11 15:57

using System.Text.Json.Serialization;

namespace X.PenServer.Contracts;

[Serializable]
public class ReceiveData
{
	/// <summary>
	/// 类型: 1.ping 2.请求更新
	/// </summary>
	[JsonPropertyName(nameof(TypeId))]
	[JsonInclude]
	public int TypeId { get; set; }

	/// <summary>
	/// 内容
	/// </summary>
	[JsonPropertyName(nameof(Content))]
	[JsonInclude]
	public string Content { get; set; }
}