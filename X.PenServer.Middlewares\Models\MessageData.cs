﻿//  -- Function: MessageData.cs
//  --- Project: PenServer
//  ---- Remark:
//  ---- Author: Lucifer
//  ------ Date: 2023/02/22 17:09

// ReSharper disable PropertyCanBeMadeInitOnly.Global
namespace X.PenServer.Middlewares.Models;

using X.PenServer.Infrastructure;

public class MessageData
{
    /// <summary>
    /// RawId
    /// </summary>
    public string Id { get; set; }

    /// <summary>
    /// KeyId
    /// </summary>
    public ulong Key => Id.StringToLong();

    /// <summary>
    /// MacAddress
    /// </summary>
    public string Mac => Id.ToMac();

    /// <summary>
    /// Event
    /// </summary>
    public int EventType { get; set; }

    /// <summary>
    /// EventData
    /// </summary>
    public object EventData { get; set; }
}