﻿<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Multi-Launch: PenServer" type="com.intellij.execution.configurations.multilaunch" factoryName="MultiLaunchConfiguration">
    <option name="activateToolWindows" value="true" />
    <rows>
      <ExecutableRowSnapshot>
        <option name="condition">
          <ConditionSnapshot>
            <option name="type" value="immediately" />
          </ConditionSnapshot>
        </option>
        <option name="disableDebugging" value="true" />
        <option name="executable">
          <ExecutableSnapshot>
            <option name="id" value="runConfig:.NET Launch Settings Profile.X.PenServer" />
          </ExecutableSnapshot>
        </option>
      </ExecutableRowSnapshot>
      <ExecutableRowSnapshot>
        <option name="condition">
          <ConditionSnapshot>
            <option name="type" value="immediately" />
          </ConditionSnapshot>
        </option>
        <option name="disableDebugging" value="true" />
        <option name="executable">
          <ExecutableSnapshot>
            <option name="id" value="runConfig:.NET Launch Settings Profile.X.PenServer.PenQueue" />
          </ExecutableSnapshot>
        </option>
      </ExecutableRowSnapshot>
      <ExecutableRowSnapshot>
        <option name="condition">
          <ConditionSnapshot>
            <option name="type" value="immediately" />
          </ConditionSnapshot>
        </option>
        <option name="disableDebugging" value="true" />
        <option name="executable">
          <ExecutableSnapshot>
            <option name="id" value="runConfig:.NET Launch Settings Profile.X.PenServer.Sockets" />
          </ExecutableSnapshot>
        </option>
      </ExecutableRowSnapshot>
    </rows>
    <method v="2" />
  </configuration>
</component>