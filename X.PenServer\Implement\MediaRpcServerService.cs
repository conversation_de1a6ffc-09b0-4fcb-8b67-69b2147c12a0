﻿// -- Function: RpcClientService.cs
// --- Project: X.PenServer
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2024/3/5 10:17

namespace X.PenServer.Implement;
using static Protos.RpcService;
using AutoMapper;
using Google.Protobuf.WellKnownTypes;
using Grpc.Core;
using Microsoft.AspNetCore.Authorization;
using Contracts;
using Protos;

/// <inheritdoc />
public class MediaRpcServerService : RpcServiceBase
{
	#region Common

	/// <summary>
	/// 媒体事件
	/// </summary>
	/// <typeparam name="TArgs"></typeparam>
	public delegate Task AsyncMediaEventHandler<in TArgs>(TArgs args) where TArgs : PenMediaBaseEventArgs;

	/// <summary>
	/// 请求播放事件
	/// </summary>
	public static event AsyncMediaEventHandler<PenPlayEventArgs> RequestPlayAsync;

	/// <summary>
	/// 请求录制事件
	/// </summary>
	public static event AsyncMediaEventHandler<PenRecordEventArgs> RequestRecordAsync;

	#endregion

	#region Init

	private readonly IMapper _mapper;

	/// <inheritdoc />
	public MediaRpcServerService(IMapper mapper)
	{
		_mapper = mapper;
	}

	#endregion

	#region Overrides of RpcServiceBase

	/// <inheritdoc />
	[Authorize]
	public override async Task<Empty> PlayService(PlayRequest request, ServerCallContext context)
	{
		if (RequestPlayAsync == null || context.CancellationToken.IsCancellationRequested)
		{
			return new Empty();
		}

		var args = _mapper.Map<PenPlayEventArgs>(request.BaseRequest);
		args.Url = request.Url;
		await RequestPlayAsync.Invoke(args).ConfigureAwait(false);
		return new Empty();
	}

	/// <inheritdoc />
	[Authorize]
	public override async Task<Empty> RecordService(RecordRequest request, ServerCallContext context)
	{
		if (RequestRecordAsync == null || context.CancellationToken.IsCancellationRequested)
		{
			return new Empty();
		}

		var args = _mapper.Map<PenRecordEventArgs>(request.BaseRequest);
		args.ItemId = request.ItemId;
		args.WorkbookPageId = request.WorkbookPageId;
		await RequestRecordAsync.Invoke(args).ConfigureAwait(false);
		return new Empty();
	}

	#endregion
}