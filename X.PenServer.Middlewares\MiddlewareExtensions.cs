﻿//  -- Function: ApiExtensions.cs
//  --- Project: PenServer
//  ---- Remark:
//  ---- Author: Lucifer
//  ------ Date: 2023/03/31 10:39

namespace X.PenServer.Middlewares;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using System.Net.WebSockets;
using System.Text;

/// <summary>
/// 中间件扩展
/// </summary>
public static class MiddlewareExtensions
{
    #region 点阵笔在线服务端socket

    /// <summary>
    /// 点阵笔服务
    /// </summary>
    /// <param name="services"></param>
    /// <returns></returns>
    public static IServiceCollection AddPenServerSocket(this IServiceCollection services)
    {
        return services.AddSingleton<PenServerSocketMiddleware>();
    }

    /// <summary>
    /// 点阵笔服务
    /// </summary>
    /// <param name="app"></param>
    /// <returns></returns>
    public static IApplicationBuilder UsePenServerSocket(this IApplicationBuilder app)
    {
        return app.UseMiddleware<PenServerSocketMiddleware>();
    }

    #endregion

    #region 点阵笔实时转发socket

    /// <summary>
    /// 点阵笔服务
    /// </summary>
    /// <param name="services"></param>
    /// <returns></returns>
    public static IServiceCollection AddRealtimePenSocket(this IServiceCollection services)
    {
        return services.AddSingleton<RealtimePenSocketMiddleware>();
    }

    /// <summary>
    /// 点阵笔服务
    /// </summary>
    /// <param name="app"></param>
    /// <returns></returns>
    public static IApplicationBuilder UseRealtimePenSocket(this IApplicationBuilder app)
    {
        return app.UseMiddleware<RealtimePenSocketMiddleware>();
    }

    #endregion

    #region 点阵笔设备监控

    /// <summary>
    /// 点阵笔监控
    /// </summary>
    /// <param name="services"></param>
    /// <returns></returns>
    public static IServiceCollection AddRealtimeMonitor(this IServiceCollection services)
    {
        return services.AddSingleton<RealtimeMonitorMiddleware>();
    }

    /// <summary>
    /// 点阵笔监控
    /// </summary>
    /// <param name="app"></param>
    /// <returns></returns>
    public static IApplicationBuilder UseRealtimeMonitor(this IApplicationBuilder app)
    {
        return app.UseMiddleware<RealtimeMonitorMiddleware>();
    }

    #endregion

    #region WebSocket

    /// <summary>
    /// 发送websocket消息
    /// </summary>
    /// <param name="socket"></param>
    /// <param name="data"></param>
    /// <param name="token"></param>
    public static async Task SendAsync(WebSocket socket, string data, CancellationToken token = default)
    {
        var buffer = Encoding.UTF8.GetBytes(data);
        var segment = new ArraySegment<byte>(buffer);
        if (socket.State != WebSocketState.Open)
        {
            return;
        }

        await socket.SendAsync(segment, WebSocketMessageType.Text, true, token);
    }

    /// <summary>
    /// 接收websocket消息
    /// </summary>
    /// <param name="socket"></param>
    /// <param name="token"></param>
    /// <returns></returns>
    public static async Task<string> ReceiveAsync(WebSocket socket, CancellationToken token = default)
    {
        try
        {
            var buffer = new ArraySegment<byte>(new byte[8192]);
            using var ms = new MemoryStream();
            WebSocketReceiveResult result;
            do
            {
                result = await socket.ReceiveAsync(buffer, token);
                if (buffer.Array is
                    {
                        Length: > 0
                    })
                {
                    await ms.WriteAsync(buffer.Array.AsMemory(buffer.Offset, result.Count), token);
                }
            } while (!result.EndOfMessage && !socket.CloseStatus.HasValue);

            ms.Seek(0, SeekOrigin.Begin);

            if (result.MessageType != WebSocketMessageType.Text && result.MessageType != WebSocketMessageType.Close)
            {
                if (result.MessageType == WebSocketMessageType.Close)
                {
                    await socket.CloseOutputAsync(WebSocketCloseStatus.NormalClosure, string.Empty, CancellationToken.None);
                }

                return null;
            }

            using var reader = new StreamReader(ms, Encoding.UTF8);
            return await reader.ReadToEndAsync(token);
        }
        catch (Exception)
        {
            return null;
        }
    }

    #endregion
}