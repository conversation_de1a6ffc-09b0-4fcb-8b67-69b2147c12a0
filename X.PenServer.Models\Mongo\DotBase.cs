﻿// -- Function: DotBase.cs
// --- Project: X.PenServer.Models
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2024/07/01 13:07

using System.Text.Json.Serialization;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace X.PenServer.Models.Mongo;

public class DotBase : MongoBaseModel
{
    /// <summary>
    /// X坐标
    /// </summary>
    [BsonElement(nameof(X))]
    [BsonRepresentation(BsonType.Int32)]
    [JsonPropertyName(nameof(X))]
    [JsonInclude]
    public int X { get; set; }

    /// <summary>
    /// Y坐标
    /// </summary>
    [BsonElement(nameof(Y))]
    [BsonRepresentation(BsonType.Int32)]
    [JsonPropertyName(nameof(Y))]
    [JsonInclude]
    public int Y { get; set; }

    /// <summary>
    /// 点位类型: 1.开始 2.结束
    /// </summary>
    [BsonElement(nameof(Type))]
    [BsonRepresentation(BsonType.Int32)]
    [JsonPropertyName(nameof(Type))]
    [JsonInclude]
    public int Type { get; set; }

    /// <summary>
    /// BookNo
    /// </summary>
    [BsonElement(nameof(BookNo))]
    [BsonRepresentation(BsonType.Int32)]
    [JsonPropertyName(nameof(BookNo))]
    [JsonInclude]
    public int BookNo { get; set; }

    /// <summary>
    /// Pressure
    /// </summary>
    [BsonElement(nameof(Pressure))]
    [BsonRepresentation(BsonType.Int32)]
    [JsonPropertyName(nameof(Pressure))]
    [JsonInclude]
    public int Pressure { get; set; }

    /// <summary>
    /// 笔迹颜色(美术场景)
    /// </summary>
    [BsonElement(nameof(Color))]
    [BsonRepresentation(BsonType.String)]
    [JsonPropertyName(nameof(Color))]
    [JsonInclude]
    public string Color { get; set; }
}