﻿using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Bson;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace X.PenServer.Models.Mongo
{
    public class PenB5Log : MongoBaseModel
    {
        [BsonElement(nameof(Page))]
        [BsonRepresentation(BsonType.Int32)]
        public int Page { get; set; }

        [BsonElement(nameof(PageId))]
        [BsonRepresentation(BsonType.Int32)]
        public int PageId { get; set; }

        [BsonElement(nameof(Mac))]
        [BsonRepresentation(BsonType.String)]
        public string Mac { get; set; }

        [BsonElement(nameof(UserId))]
        [BsonRepresentation(BsonType.String)]
        public string UserId { get; set; }

        [BsonElement(nameof(Dots))]
        [JsonPropertyName(nameof(Dots))]
        public List<DotBase> Dots { get; set; } = [];
    }
}
