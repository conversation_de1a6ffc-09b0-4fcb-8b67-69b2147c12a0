﻿// -- Function: MarkingStudentData.cs
// --- Project: X.PenServer.Contracts
// ---- Remark: 
// ---- Author: Lucifer
// ------ Date: 2024/1/31 16:38
namespace X.PenServer.Contracts.Queue;

using System.Text.Json.Serialization;

/// <summary>
/// 选中对应学号开始批阅学生
/// </summary>
/// <remarks>英语单元答题卡</remarks>
public class EnglishUnitMarkingStudentData
{
	/// <summary>
	/// 教师id
	/// </summary>
	[JsonPropertyName(nameof(TeacherId))]
	[JsonInclude]
	public string TeacherId { get; set; }

	/// <summary>
	/// 学生id
	/// </summary>
	[JsonPropertyName(nameof(StudentId))]
	[JsonInclude]
	public string StudentId { get; set; }

	/// <summary>
	/// 学生编号
	/// </summary>
	[JsonPropertyName(nameof(StudentNo))]
	[JsonInclude]
	public string StudentNo { get; set; }

	/// <summary>
	/// 试卷id
	/// </summary>
	[JsonPropertyName(nameof(PaperId))]
	[JsonInclude]
	public string PaperId { get; set; }
}