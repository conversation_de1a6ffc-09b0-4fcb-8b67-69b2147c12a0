﻿// -- Function: PenMappingRedisService.cs
// --- Project: X.PenServer.Services
// ---- Remark: 
// ---- Author: Lucifer
// ------ Date: 2023/10/30 17:28

using StackExchange.Redis;
using X.Elegant.Redis;
using X.Elegant.Redis.Infrastructure;
using X.PenServer.Interfaces.Redis;

namespace X.PenServer.Services.Redis;

/// <inheritdoc cref="IPenMappingRedisService" />
public class PenMappingRedisService : RedisService, IPenMappingRedisService
{
    /// <inheritdoc />
    public PenMappingRedisService(IRedisServiceFactory factory) : base(factory)
    {
    }

    #region Overrides of RedisService

    /// <inheritdoc />
    protected override string Prefix => RedisKeys.PEN_MAPPING + "|";

    #endregion

    #region Implementation of IPenMappingRedisService

    /// <inheritdoc />
    public async Task<string> HGetMappingUserAsync(string mac)
    {
        const string key = "MacToUser";
        return await HGetStringAsync(key, mac);
    }

    /// <inheritdoc />
    public async Task HSetMappingUserAsync(string mac, string userid)
    {
        const string key = "MacToUser";
        await HSetAsync(key, mac, userid, flags: CommandFlags.FireAndForget);
    }

    /// <inheritdoc />
    public async Task<int> HGetPenRoleAsync(string mac)
    {
        const string key = "PenRole";
        return await HGetAsync<int>(key, mac);
    }

    /// <inheritdoc />
    public async Task HSetPenRoleAsync(string mac, int role)
    {
        const string key = "PenRole";
        await HSetAsync(key, mac, role, flags: CommandFlags.FireAndForget);
    }

    #endregion
}