﻿// -- Function: IPaperLogRedisService.cs
// --- Project: X.PenServer.Interfaces
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2023/10/31 17:40

using X.Elegant.Redis.Infrastructure;
using X.PenServer.Contracts.Paper;
using X.PenServer.Contracts.Queue;
using X.PenServer.Contracts.Redis;
using X.PenServer.Interfaces.Lifecycle;

namespace X.PenServer.Interfaces.Redis;

/// <summary>
/// 做卷相关笔迹缓存
/// </summary>
public interface IPaperLogRedisService : IRedisService, ISingletonService
{
    /// <summary>
    /// 获取当前一键赋码的试卷
    /// </summary>
    /// <param name="classid">班级id</param>
    /// <remarks>卷码纸专用</remarks>
    /// <returns></returns>
    Task<CurrentPaperId> GetCurrentPaperIdAsync(string classid);

    /// <summary>
    /// 获取当前离线一键赋码的试卷
    /// </summary>
    /// <param name="classid">班级id</param>
    /// <remarks>卷码纸专用</remarks>
    /// <returns></returns>
    Task<CurrentPaperId> GetCurrentOfflinePaperIdAsync(string classid);

    /// <summary>
    /// 获取学生正在书写的页面
    /// </summary>
    /// <param name="userid">学生用户id</param>
    /// <returns></returns>
    Task<WorkbookPage> GetStudentCurrentDoPaperPageInfoAsync(string userid);

    /// <summary>
    /// 获取学生正在书写的8K页面
    /// </summary>
    /// <param name="userid"></param>
    /// <returns></returns>
    Task<WorkbookPageGroup> GetStudentCurrentDoPaperPageInfoFor8KAsync(string userid);

    /// <summary>
    /// 设置学生正在书写的页面
    /// </summary>
    /// <param name="userid">用户id</param>
    /// <param name="workpage">当前作答子页面</param>
    /// <returns></returns>
    Task SetStudentCurrentDoPaperPageInfoAsync(string userid, WorkbookPage workpage);

    /// <summary>
    /// 设置学生书写的8K页面
    /// </summary>
    /// <param name="userid"></param>
    /// <param name="workpageGroup"></param>
    /// <returns></returns>
    Task SetStudentCurrentDoPaperPageInfoFor8KAsync(string userid, WorkbookPageGroup workpageGroup);

    /// <summary>
    /// 缓存卷码区域书写笔迹
    /// </summary>
    /// <param name="userid">用户id</param>
    /// <param name="dots">点位数据</param>
    /// <returns></returns>
    Task SAddUserPaperNumAsync(string userid, List<PenDot> dots);

    /// <summary>
    /// 获取卷码区域书写点位笔迹
    /// </summary>
    /// <param name="userid">用户id</param>
    /// <returns></returns>
    Task<List<PenDot>> SMemberUserPaperNumAsync(string userid);

    /// <summary>
    /// 缓存识别的卷码
    /// </summary>
    /// <param name="userid">用户id</param>
    /// <param name="paperno">卷码</param>
    /// <returns></returns>
    Task SaveRecognitionNumberAsync(string userid, string paperno);

    /// <summary>
    /// 获取识别的卷码
    /// </summary>
    /// <param name="userid">用户id</param>
    /// <returns></returns>
    Task<int> GetRecognitionNumberAsync(string userid);

    /// <summary>
    /// 清理临时卷码区域书写笔迹
    /// </summary>
    /// <param name="userid">用户id</param>
    /// <returns></returns>
    Task DelUserPaperNumAsync(string userid);

    /// <summary>
    /// 获取卷码对应的试卷信息
    /// </summary>
    /// <param name="paperno">卷码</param>
    /// <returns></returns>
    Task<string> HGetPaperNumAsync(string paperno);

    /// <summary>
    /// 设置卷码对应的试卷信息
    /// </summary>
    /// <param name="paperno">卷码</param>
    /// <param name="paperid">试卷id</param>
    /// <returns></returns>
    Task HSetPaperNumAsync(string paperno, string paperid);

    /// <summary>
    /// 获取该试卷是否开启了互批
    /// </summary>
    /// <param name="classid">班级id</param>
    /// <param name="paperid">试卷id</param>
    /// <returns></returns>
    Task<bool> HGetCorrectAsync(string classid, string paperid);

    /// <summary>
    /// 获取当前正在作答的试卷题目信息
    /// </summary>
    /// <param name="classid">班级id</param>
    /// <param name="paperid">试卷id</param>
    /// <returns></returns>
    Task<CurrentPaperItem> GetCurrentPaperItemIdAsync(string classid, string paperid);

    /// <summary>
    /// 设置当前手写卷码过期时间
    /// </summary>
    /// <param name="userid"></param>
    /// <param name="time_span"></param>
    /// <returns></returns>
    Task ExpirePaperNumAsync(string userid, TimeSpan time_span);

    /// <summary>
    /// 获取试卷做答方式, 默认整卷模式
    /// </summary>
    /// <param name="classid">班级id</param>
    /// <param name="paperid">试卷id</param>
    /// <returns>1.整卷做答 2.单题做答</returns>
    Task<int> GetPaperAnswerModeAsync(string classid, string paperid);

    /// <summary>
    /// 设置题目橡皮擦状态
    /// </summary>
    /// <param name="mac">mac</param>
    /// <param name="paperid">试卷id</param>
    /// <param name="itemno">题目id</param>
    /// <returns></returns>
    Task SetEraserStateAsync(string mac, string paperid, string itemno);

    /// <summary>
    /// 设置互动练习簿学生单题作答状态
    /// </summary>
    /// <param name="mac"></param>
    /// <param name="paperid"></param>
    /// <param name="classId"></param>
    /// <param name="itemNo"></param>
    /// <returns></returns>
    Task SetSingleItemAnswerStateAsync(string mac, string paperid,string classId, string itemNo);

    /// <summary>
    /// 获取互动练习簿学生单题作答状态
    /// </summary>
    /// <param name="mac"></param>
    /// <param name="paperid"></param>
    /// <param name="classId"></param>
    /// <param name="itemno"></param>
    /// <returns></returns>
    Task<bool> GetSingleItemAnswerStateAsync(string mac, string paperid, string classId, string itemno);

	/// <summary>
	/// 获取题目橡皮擦状态
	/// </summary>
	/// <param name="mac">mac</param>
	/// <param name="paperid">试卷id</param>
	/// <param name="itemno">题目id</param>
	/// <returns></returns>
	Task<bool> GetEraserStateAsync(string mac, string paperid, string itemno);

    /// <summary>
    /// 删除题目橡皮擦状态
    /// </summary>
    /// <param name="mac">mac</param>
    /// <param name="paperid">试卷id</param>
    /// <param name="itemno">题目id</param>
    /// <returns></returns>
    Task DeleteEraserStateAsync(string mac, string paperid, string itemno);

    /// <summary>
    /// 设置学生当前正在互批的试卷页面
    /// </summary>
    /// <param name="studentid">学生id</param>
    /// <param name="paperid">试卷id</param>
    /// <param name="pageno">页码id</param>
    /// <returns></returns>
    Task SetStudentCurrentCorrectPageNoAsync(string studentid, string paperid, int pageno);

    /// <summary>
    /// 获取学生当前正在互批的试卷页面
    /// </summary>
    /// <param name="studentid">学生id</param>
    /// <param name="paperid">试卷id</param>
    /// <returns></returns>
    Task<int> GetStudentCurrentCorrectPageNoAsync(string studentid, string paperid);
}