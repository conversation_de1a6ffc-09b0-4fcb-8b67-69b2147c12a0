﻿//  -- Function: ICorrectPenLogService.cs
//  --- Project: X.PenServer.Interfaces
//  ---- Remark:
//  ---- Author: Lucifer
//  ------ Date: 2023/10/25 01:28:34

using X.PenServer.Interfaces.Lifecycle;
using X.PenServer.Models.Mongo;

namespace X.PenServer.Interfaces.Mongo;

/// <summary>
/// 订正笔迹
/// </summary>
public interface ICorrectPenLogService : IMongoAutoService<CorrectPenLog>, ISingletonService
{
    
}