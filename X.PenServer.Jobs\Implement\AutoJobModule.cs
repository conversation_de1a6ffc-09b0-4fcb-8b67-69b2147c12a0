﻿// -- Function: AutoJobModule.cs
// --- Project: X.PenServer.Jobs
// ---- Remark: 
// ---- Author: Lucifer
// ------ Date: 2024/04/04 16:04:28

// ReSharper disable ClassNeverInstantiated.Global

using System.Security.Cryptography;
using System.Text;
using System.Text.Encodings.Web;
using System.Text.Json;
using System.Text.Json.Serialization;
using Masa.BuildingBlocks.Data;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NLog.Extensions.Logging;
using X.Elegant.Redis;
using X.Elegant.Redis.Infrastructure.Converters;

namespace X.PenServer.Jobs.Implement;
using Microsoft.Extensions.DependencyInjection;
using Volo.Abp.Autofac;
using Volo.Abp.BackgroundWorkers.Quartz;
using Volo.Abp.Modularity;
using Volo.Abp.Quartz;

/// <summary>
/// 自动任务模块
/// </summary>
[DependsOn(typeof(AbpAutofacModule), typeof(AbpBackgroundWorkersQuartzModule))]
public class AutoJobModule : AbpModule
{
    #region Overrides of AbpModule

    /// <inheritdoc />
    public override void PostConfigureServices(ServiceConfigurationContext context)
    {
        Configure<AbpQuartzOptions>
        (
            x =>
            {
                x.Configurator = cfg =>
                {
                    cfg.UseInMemoryStore();
                    cfg.SchedulerName = "Auto Job Scheduler";
                    cfg.UseSimpleTypeLoader();
                    cfg.UseDedicatedThreadPool
                    (
                        z =>
                        {
                            z.MaxConcurrency = 20;
                        }
                    );
                };
            }
        );
    }

    /// <inheritdoc />
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
        var config = context.Services.GetConfiguration();
        context.Services.AddSingleton(x => x.GetRequiredService<IOptions<JsonSerializerOptions>>().Value);
        context.Services.AddSingleton
        (
            new ParallelOptions
            {
                MaxDegreeOfParallelism = Environment.ProcessorCount - 1
            }
        );
        context.Services.AddSequentialGuidGenerator(SequentialGuidType.SequentialAsBinary);
        var workerid = RandomNumberGenerator.GetInt32(1, 1023);
        Environment.SetEnvironmentVariable("WORKER_ID", workerid.ToString());
        context.Services.AddSnowflake(x => { x.MaxCallBackTime = 1500; });
        context.Services.AddLogging(x =>
        {
            x.ClearProviders();
            x.AddNLog(config);
        });
        context.Services.AddElegantRedisService(x =>
        {
            x.PropertyNameCaseInsensitive = true;
            x.PropertyNamingPolicy = null;
            x.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull;
            x.NumberHandling = JsonNumberHandling.AllowReadingFromString;
            x.Converters.Add(new DateTimeJsonConverter());
            x.Encoder = JavaScriptEncoder.UnsafeRelaxedJsonEscaping;
            x.ReadCommentHandling = JsonCommentHandling.Skip;
        });
        context.Services.AddHostedService<HostService>();
    }

    #endregion
}