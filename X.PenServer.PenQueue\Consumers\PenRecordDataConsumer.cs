﻿// -- Function: PenRecordDataConsumer.cs
// --- Project: X.PenServer.PenQueue
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2024/3/7 16:10
// ReSharper disable ClassNeverInstantiated.Global
namespace X.PenServer.PenQueue.Consumers;

using MassTransit;
using Microsoft.Extensions.Logging;
using Contracts.Queue;
using Infrastructure;
using Interfaces;

/// <inheritdoc cref="PenRecordData"/>
public class PenRecordDataConsumer : IConsumer<Batch<PenRecordData>>
{
	private readonly ILogger<PenRecordDataConsumer> _log;
	private readonly IDotQueueService _service;

	public PenRecordDataConsumer(ILogger<PenRecordDataConsumer> log, IDotQueueService service)
	{
		_log = log;
		_service = service;
	}

	#region Implementation of IConsumer<in Batch<PenRecordData>>

	/// <inheritdoc />
	public async Task Consume(ConsumeContext<Batch<PenRecordData>> context)
	{
		foreach (var item in context.Message)
		{
			var data = item.Message;
			_log.LogInformation
			(
				"[{Name}_Service]: {Data}", nameof(PenRecordDataConsumer),
				data.ToJsonString()
			);
			await _service.RecordProcessAsync(data);
		}

		await context.ConsumeCompleted;
	}

	#endregion
}