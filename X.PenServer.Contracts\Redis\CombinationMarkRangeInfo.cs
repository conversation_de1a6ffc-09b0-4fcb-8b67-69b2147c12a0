﻿// -- Function: CombinationMarkRangeInfo.cs
// --- Project: X.PenServer.Contracts
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2023/12/28 13:49

// ReSharper disable UnusedMember.Global
namespace X.PenServer.Contracts.Redis;

/// <summary>
/// 组合页区域范围
/// </summary>
public class CombinationMarkRangeInfo
{
	/// <summary>
	/// 标记id
	/// </summary>
	public string MarkId { get; set; }

	/// <summary>
	/// 试卷页唯一id
	/// </summary>
	public string WorkPageId { get; set; }

	/// <summary>
	/// 试卷id
	/// </summary>
	public string PaperId { get; set; }

	/// <summary>
	/// 页码
	/// </summary>
	public int Page { get; set; }

	/// <summary>
	/// 区域范围
	/// </summary>
	public string Range { get; set; }
}