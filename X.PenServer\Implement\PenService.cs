﻿//  -- Function: PenService.cs
//  --- Project: PenServer
//  ---- Remark:
//  ---- Author: Lucifer
//  ------ Date: 2023/02/22 13:29

using NLog;
using X.PenServer.Interfaces.Redis;

namespace X.PenServer.Implement;
using AFPen;
using AFPen.Public;
using AFPen.Public.EvtStruct;
using AutoMapper;
using Contracts;
using Contracts.Config;
using Contracts.Queue;
using Infrastructure;
using Interfaces;
using Masa.BuildingBlocks.Data;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Net.WebSockets;
using System.Runtime.InteropServices;
using System.Text;
using X.PenServer.Contracts.Tips;
using X.PenServer.Middlewares.Models;
using xb;
using static AFPen.PenCtrl;
using static Extensions.PenExtensions;
using static MediaRpcServerService;
using static Middlewares.MiddlewareExtensions;
using static Middlewares.PenServerSocketMiddleware;
using static Middlewares.RealtimeMonitorMiddleware;
using static Volo.Abp.Threading.AsyncHelper;
using static X.PenServer.Storage.ReceivePortMessageStorage;

/// <inheritdoc cref="IPenService" />
public class PenService : AF_IPenEvent, AF_IPortMsg, AF_IAFPenUpdaterReport, AF_IAFPenMediaPlayer, IPenService
{
    private readonly ILogger<PenService> _logger;
    private static readonly ConcurrentDictionary<ulong, PenPrensenter> _current = new();
    private static readonly ConcurrentDictionary<string, PenRecordEventArgs> _current_record = new();
    private readonly IPenBusiness _pen;
    private readonly IPenConfig _pen_config;
    private readonly ISnowflakeGenerator _snowflake;
    private readonly ParallelOptions _parallel_options;
    private readonly IMapper _mapper;
    private readonly ISequentialGuidGenerator _generator;
    private readonly IPenMappingRedisService _pen_mapping_redis_service;
    private readonly ICommonRedisService _redis_service;

    public PenService(ILogger<PenService> logger, IPenBusiness pen, IPenConfig pen_config, ISnowflakeGenerator snowflake, ParallelOptions parallel_options, IMapper mapper, ISequentialGuidGenerator generator, IPenMappingRedisService pen_mapping_redis_service, ICommonRedisService redis_service)
    {
        _logger = logger;
        _pen = pen;
        _pen_config = pen_config;
        _snowflake = snowflake;
        _parallel_options = parallel_options;
        _mapper = mapper;
        _generator = generator;
        _pen_mapping_redis_service = pen_mapping_redis_service;
        _redis_service = redis_service;
    }

    #region Implementation of IPenService

    /// <inheritdoc />
    public void Start()
    {
        try
        {
            var port = Convert.ToInt32(_pen_config.PenServerPort);
            var nlog = Path.Combine(AppContext.BaseDirectory, "nlog.config");
            var log = LogManager.Setup().LoadConfigurationFromFile(nlog).GetCurrentClassLogger();
            Instance.setNLogger(log);
            Instance.AFAP_Init(port);
            Instance.SetConsoleLogEnabled(true);
            Instance.AF_SetPortMsgListener(this);
            Instance.AF_SetPenEventListener(this);
            Instance.AFAP_SetUpdaterListener(this);
            Instance.AFAP_SetMediaPlayerListener(this);
            Instance.SetSyncAfterConnectEnabled(true);
            Instance.AF_SetDejitterEnable(true);
            Instance.AF_SetPaperSize(AllPageSizes);
            OnNotify += async (_, e) => await OnSocketNotify(e).ConfigureAwait(false);
            RequestPlayAsync += async e => await OnRequestPlayAsync(e).ConfigureAwait(false);
            RequestRecordAsync += async e => await OnRequestRecordAsync(e).ConfigureAwait(false);
            _logger.LogInformation("start pen server... 更新v-20250708版本 增加-充電連上網後可再延時連上的功能");
        }
        catch (Exception e)
        {
            _logger.LogCritical("start server error: {e}", e.Message);
        }
    }

    #region 异步事件处理

    /// <summary>
    /// 播放请求
    /// </summary>
    /// <param name="e"></param>
    private async Task OnRequestPlayAsync(PenPlayEventArgs e)
    {
        _logger.LogInformation("request play: {data}", e.ToJsonString());
        await Task.Factory.StartNew(x =>
        {
            var data = (PenPlayEventArgs)x;

            if (IsExistRecord(data.Mac))
            {
                RemoveRecord(data.Mac);
            }

            AFMediaPlayer.RequestPlayerPlay(data.Mac, data.Url);
        }, e, CancellationToken.None).ConfigureAwait(false);
    }

    /// <summary>
    /// 录音请求
    /// </summary>
    /// <param name="e"></param>
    private async Task OnRequestRecordAsync(PenRecordEventArgs e)
    {
        _logger.LogInformation("request record: {data}", e.ToJsonString());
        if (_current_record.ContainsKey(e.Mac))
        {
            _current_record.TryRemove(e.Mac);
        }

        _current_record.TryAdd(e.Mac, e);
        await Task.Factory.StartNew(async x =>
        {
            var edata = (PenRecordEventArgs)x;

            try
            {
                var tips = await GetRecordStartTipsConfigAsync();

                if (!string.IsNullOrWhiteSpace(tips?.VoiceUrl))
                {
                    AddRecord(mac: edata.Mac, 1);
                    AFMediaPlayer.RequestPlayerPlay(edata.Mac, tips.VoiceUrl);
                }
                else
                {
                    AFMediaPlayer.RequestRecorderStart(edata.Mac);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("[请求播放提示音]:{ex}", ex.ToString());
            }

        }, e, CancellationToken.None).ConfigureAwait(false);
    }

    /// <summary>
    /// 接收数据事件
    /// </summary>
    /// <param name="e"></param>
    private async Task OnSocketNotify(PenEventArgs e)
    {
        const string method_name = nameof(OnSocketNotify);
        try
        {
            if (string.IsNullOrWhiteSpace(e.Wid) || string.IsNullOrWhiteSpace(e.Mac) || string.IsNullOrWhiteSpace(e.ReceiveData))
            {
                return;
            }

            var key = e.Mac.StringToLong();
            if (!_current.ContainsKey(key))
            {
                return;
            }

            var ispass = _current.TryGetValue(key, out var xpen);
            if (!ispass || xpen == null)
            {
                return;
            }

            var logid = $" clientid: {e.Wid} , mac: {e.Mac}";
            _logger.LogInformation("[{name}]:{logid} , data: {data}", method_name, logid, e.ReceiveData);
            await _redis_service.HSetAsync("mac", e.Mac, "1").ConfigureAwait(false);
            var result= Instance.AFAP_GetFirmwareVer(e.Mac);
            _logger.LogInformation("[{name}]:{logid} , data: {data},GetFirmwareVer:{iresult}", method_name, logid, e.ReceiveData, result);
        }
        catch (Exception ex)
        {
            _logger.LogCritical("[{name}]: {error}", method_name, ex.Message);
        }
    }

    #endregion

    /// <inheritdoc />
    public void Stop()
    {
        try
        {
            Instance.AFAP_free();
            _logger.LogInformation("stop pen server...");
        }
        catch (Exception e)
        {
            _logger.LogCritical("stop server error: {error}", e.Message);
        }
    }

    /// <inheritdoc />
    public ConcurrentDictionary<ulong, PenPrensenter> GetCurrentList() => _current;

    #endregion

    #region Implementation of AF_IPortMsg

    /// <inheritdoc />
    public void OnReceivedPortMessage(string id, AFPortMsgType msgtype, string json)
    {
        try
        {
            var xid = id.StringToLong();
            var mac = xid.GetMac();
            var logid = $" id: {xid} , mac: {mac}";
            _logger.LogDebug("[{name}]:{logid} , msgtype: {msgtype} , json: {json}", nameof(OnReceivedPortMessage), logid, Enum.GetName(msgtype), json);
            switch (msgtype)
            {
                case AFPortMsgType.PEN_CONNECTION_SUCCESS:

                    _logger.LogInformation("{mac}触发连接事件,当前笔连接总数量:{count} AllPenClientsCount:{count2}", mac, Instance.AFAP_GetAllConnectedDevice().Count, AllPenClients.Count);

                    if (!_current.ContainsKey(xid))
                    {
                        var isadd = _current.TryAdd(xid, new PenPrensenter(xid));
                        if (!isadd)
                        {
                            _logger.LogDebug("[add device failure]:{logid}", logid);
                        }
                    }

                    break;
                case AFPortMsgType.PEN_CONNECTION_FAILURE:
                    _logger.LogDebug("[connect failure]:{logid}", logid);
                    break;
                case AFPortMsgType.PEN_DISCONNECTED:

                    _logger.LogInformation("{mac}触发断开事件,当前笔连接总数量:{count} AllPenClientsCount:{count2}", mac, Instance.AFAP_GetAllConnectedDevice().Count, AllPenClients.Count);

                    _current.TryGetValue(xid, out var xpen);
                    var isremove = _current.TryRemove(xid);
                    _logger.LogInformation(isremove ? "[disconnect]:{logid}" : "[disconnect]:{logid}, failure", logid);

                    break;
            }

            PushMessage(new ReceivedPortMessageDto
            {
                MacIdStr=id,
                MacIdLong=xid,
                Mac=mac,
                Msgtype=msgtype
            });

        }
        catch (Exception e)
        {
            _logger.LogCritical(e, "{error}", e.Message);
        }
    }

    #endregion

    #region Implementation of AF_IPenEvent

    /// <inheritdoc />
    public void OnReceivedMessage(string id, AFE_EVENT_TYPE type, object eventdata)
    {
        var xdata = new MessageData
        {
            Id = id,
            EventType = (int)type,
            EventData = eventdata
        };
        var xid = xdata.Key;
        var logid = $" id: {xid} , mac: {xdata.Mac} , type: {type} ";
        _logger.LogDebug("[{name}]{logid}", nameof(OnReceivedMessage), logid);
        if (!_current.ContainsKey(xid))
        {
            var isadd = _current.TryAdd(xid, new PenPrensenter(xid));
            _logger.LogDebug(!isadd ? "[add key failure]:{logid} , special" : "[add key success]:{logid} , special", logid);
        }

        switch (type)
        {
            case AFE_EVENT_TYPE.AFE_Dot:
                AFE_Dot_Service(xdata, logid);
                break;
            case AFE_EVENT_TYPE.AFE_GetVersion:
                AFE_GetVersion_Service(xdata, logid);
                break;
            case AFE_EVENT_TYPE.AFE_GetBattery:
                AFE_GetBattery_Service(xdata, logid);
                break;
            case AFE_EVENT_TYPE.AFE_GetDots:
                AFE_GetDots_Service(xdata, logid);
                break;
            case AFE_EVENT_TYPE.AFE_GetWifiSSID:
                AFE_GetWifiSSID_Service(xdata, logid);
                break;
            case AFE_EVENT_TYPE.AFE_UpdateFW:
                AFE_UpdateFW_Service(xdata, logid);
                break;
            case AFE_EVENT_TYPE.AFE_UpdateFWProgress:
                AFE_UpdateFWProgress_Service(xdata, logid);
                break;
            case AFE_EVENT_TYPE.AFE_OfflineTaskReady:
                AFE_OfflineTaskReady_Service(xdata, logid);
                break;
            case AFE_EVENT_TYPE.AFE_ClearStorage:
                AFE_ClearStorage_Service(xdata, logid);
                break;
            case AFE_EVENT_TYPE.AFE_GetStandbyTime:
                AFE_GetStandbyTime_Service(xdata, logid);
                break;
        }
    }

    #region AFE_Dot

    /// <summary>
    /// 获取实时点位
    /// </summary>
    /// <param name="data"></param>
    /// <param name="logid"></param>
    private void AFE_Dot_Service(MessageData data, string logid)
    {
        const string method_name = nameof(AFE_Dot_Service);
        try
        {
            _logger.LogDebug("[{name}]:{logid}, start...", method_name, logid);
            var dot = (AFEDot_)data.EventData;
            var mac = data.Mac;

            #region 实时点位日志

            var originDot = new DotDto
            {
                RawX = dot.X,
                RawY = dot.Y,
                page=dot.page,
                book_no=dot.book_no,
                book_width=dot.book_width,
                book_height=dot.book_height,
                reserved1 = dot.reserved1,
                type=dot.type,
                pr=dot.pr,
                LogTime=DateTime.Now
            };

            HandleDotsForLog(1, originDot);

            _logger.LogInformation("[实时点位-{mac}]【{content}】", mac, originDot.ToJsonString());

            #endregion

            var ispass = _current.TryGetValue(data.Key, out var penter);
            if (!ispass || penter == null)
            {
                return;
            }

            var type = dot.type;
            var page = Convert.ToInt32(dot.page);
            var pendot = new PenDot
            {
                Oid = _snowflake.NewId(),
                X = dot.X,
                Y = dot.Y,
                Time = DateTime.Now,
                Type = type,
                Page = page,
                BookNo = dot.book_no,
                Pressure = dot.pr
            };
            penter.AddDots(1, pendot, _pen_config.IsFix);

            if (type != 2)
            {
                return;
            }

            var dotlist = _mapper.Map<List<PenDot>>(penter.Dots);
            var pendata = new PenDotData
            {
                Mac = mac,
                Page = page,
                Time = DateTime.Now,
                PageId = page > 1000 ? page : 0,
                Dots = dotlist,
                DataType = 1
            };

            //清理当前缓存的点位数据
            penter.Dots.Clear();

            //推送实时点位
            RunSync(async () => await _pen.AddPenLog(pendata).ConfigureAwait(false));
            _logger.LogInformation
            (
                "[{name}]: id: {id} , mac: {mac}, type: {type} , page: {page} , pageid: {pageid}", method_name, data.Id, mac, dot.type.ToString(), pendata.Page.ToString(), pendata.PageId.ToString()
            );
        }
        catch (Exception e)
        {
            _logger.LogCritical(e, "[{name}]: {e}", method_name, e.Message);
        }
    }

    #endregion

    #region AFE_GetVersion

    /// <summary>
    /// 获取版本号
    /// </summary>
    /// <param name="data"></param>
    /// <param name="logid"></param>
    private void AFE_GetVersion_Service(MessageData data, string logid)
    {
        const string method_name = nameof(AFE_GetVersion_Service);
        try
        {
            _logger.LogInformation("[{name}]:{logid}, start...", method_name, logid);
            var xdata = (AFEGetVersion_)data.EventData;
            var bytes = new byte[xdata.count];
            unsafe
            {
                Marshal.Copy((IntPtr)xdata.version, bytes, 0, bytes.Length);
            }

            var vid = Encoding.UTF8.GetString(bytes);
            _logger.LogInformation("[{name}]: id: {id} , sid: {vid}", method_name, data.Id, vid);
            if (!_current.ContainsKey(data.Key))
            {
                return;
            }

            var ispass = _current.TryGetValue(data.Key, out var xpen);
            if (!ispass || xpen == null)
            {
                return;
            }

            vid = vid.Trim();
            xpen.Version = vid;

            RunSync
            (
                async () =>
                {
                    await _pen.UpdateVersion(data.Mac, vid).ConfigureAwait(false);
                    var ver = await _pen.GetVersion(vid);
                    var zdata = new PenVersionData();
                    if (ver != null)
                    {
                        zdata.RawVersion = ver.RawVersion ?? "-";
                        zdata.Version = ver.Version ?? "-";
                    }

                    await PushAsync(data.Mac, PenDataType.Version, zdata.ToJsonString()).ConfigureAwait(false);
                    var latest = await _pen.GetVersionList();

                    //不同型号的笔固件处理
                    var modelcode = xpen.Version.Contains("61WPS", StringComparison.OrdinalIgnoreCase)
                        ? "VoicePen"
                        : "DotPen";
                    var topv = latest.FirstOrDefault(x => x.IsTop && x.ModelCode == modelcode);
                    if (topv == null)
                    {
                        return;
                    }

                    //当前版本和最新版本号一直, 不升级
                    if (topv.RawVersion.Equals(vid, StringComparison.OrdinalIgnoreCase))
                    {
                        return;
                    }

                    var fwpath = topv.BinName;
                    var latestv = topv.RawVersion;
                    if (string.IsNullOrWhiteSpace(xpen.Version))
                    {
                        _logger.LogDebug
                        (
                            "[{name}]:{logid}, pen version is empty", method_name, logid
                        );
                        return;
                    }

                    //是否更新所有设备到最新版本
                    var isall = _pen_config.IsAutoAll;
                    var firmwares = _pen_config.Firmwares;
                    var xlatest = firmwares.FirstOrDefault(x => x.ModelCode == modelcode);
                    if (isall && xlatest != null)
                    {
                        fwpath = xlatest.FileName;
                        latestv = xlatest.BinCode;
                    }

                    var path = Path.Combine(AppContext.BaseDirectory, "Firmware", fwpath);
                    if (!File.Exists(path) || xpen.Version.Trim().Equals(latestv))
                    {
                        return;
                    }

                    var isupdate = false;
                    if (isall)
                    {
                        isupdate = true;
                    }
                    else
                    {
                        if (IsUpdateFirmware(data.Mac))
                        {
                            isupdate = true;
                        }
                    }

                    if (!isupdate)
                    {
                        return;
                    }

                    _logger.LogInformation
                    (
                        "[{name}]:{logid} , oldsid: {version} , update firmware", method_name, logid, xpen.Version
                    );
                    var fwbytes = await File.ReadAllBytesAsync(path);
                    var fwresult = Instance.requestUpdateFW(data.Mac, fwbytes);
                    _logger.LogInformation("[{name}]:{logid} , fwresult: {fwresult}", method_name, logid, fwresult.ToString());
                }
            );
        }
        catch (Exception e)
        {
            _logger.LogCritical(e, "[{name}]: {e}", method_name, e.Message);
        }
    }

    /// <summary>
    /// 是否收到更新版本通知
    /// </summary>
    /// <param name="mac">mac地址</param>
    /// <returns></returns>
    private bool IsUpdateFirmware(string mac)
    {
        try
        {
            var result = _redis_service.HGetString("mac", mac);
            return !string.IsNullOrWhiteSpace(result);
        }
        catch (Exception)
        {
            return false;
        }
    }

    #endregion

    #region AFE_GetBattery

    private void AFE_GetBattery_Service(MessageData data, string logid)
    {
        const string method_name = nameof(AFE_GetBattery_Service);
        try
        {
            _logger.LogDebug("[{name}]:{logid}, start...", method_name, logid);
            var xdata = (AFEGetBattery_)data.EventData;
            var val = xdata.val;
            var status = xdata.status;
            var statetxt = status switch
            {
                1 => "充电中",
                2 => "已充满",
                _ => ""
            };
            if (!_current.ContainsKey(data.Key))
            {
                return;
            }

            var ispass = _current.TryGetValue(data.Key, out var xpen);
            if (!ispass || xpen == null)
            {
                return;
            }

            if (!string.IsNullOrWhiteSpace(xpen.Battery))
            {
                return;
            }

            xpen.Battery = val.ToString();
            var result = RunSync(async () => await _pen.UpdateBatteryState(data.Mac, val.ToString()).ConfigureAwait(false));
            RunSync
            (
                async () =>
                {
                    var zdata = new PenBatteryData
                    {
                        Battery = val,
                        Status = statetxt
                    };
                    await PushAsync(data.Mac, PenDataType.Battery, zdata.ToJsonString()).ConfigureAwait(false);
                }
            );
            _logger.LogDebug
            (
                "[{name}]:{logid}, val: {val}, status: {status}, state: {statetxt} , result: {result}", method_name, logid, val.ToString(), status.ToString(), statetxt, result.ToString()
            );
        }
        catch (Exception e)
        {
            _logger.LogCritical(e, "[{name}]: {e}", method_name, e.Message);
        }
    }

    #endregion

    #region AFE_GetDots

    private void AFE_GetDots_Service(MessageData data, string logid)
    {
        const string method_name = nameof(AFE_GetDots_Service);
        try
        {
            _logger.LogInformation("[{name}]:{logid}, start...", method_name, logid);
            unsafe
            {
                var xdata = (AFEGetDots_)data.EventData;
                var ispass = _current.TryGetValue(data.Key, out var penter);
                if (!ispass || penter == null || xdata.dotCnt <= 0)
                {
                    return;
                }

                for (var i = 0; i < xdata.dotCnt; i++)
                {
                    penter.Count++;
                    var dot = xdata.list[i];
                    var type = dot.type;
                    var page = Convert.ToInt32(dot.page);
                    var pendot = new PenDot
                    {
                        Oid = _snowflake.NewId(),
                        X = dot.X,
                        Y = dot.Y,
                        Time = DateTime.Now,
                        Type = type,
                        Page = page,
                        BookNo = dot.book_no,
                        Pressure = dot.pr
                    };
                    penter.AddDots(2, pendot, _pen_config.IsFix);

                    #region 离线点位日志

                    var originDot = new DotDto
                    {
                        RawX = dot.X,
                        RawY = dot.Y,
                        page=dot.page,
                        book_no=dot.book_no,
                        book_width=dot.book_width,
                        book_height=dot.book_height,
                        reserved1 = dot.reserved1,
                        type=dot.type,
                        pr=dot.pr,
                        LogTime=DateTime.Now
                    };

                    HandleDotsForLog(2, originDot);

                    _logger.LogInformation("[离线点位-{mac}]【{content}】", data.Mac, originDot.ToJsonString());

                    #endregion
                }
            }
        }
        catch (Exception e)
        {
            _logger.LogCritical(e, "[{name}]: {e}", method_name, e.Message);
        }
    }

    #endregion

    #region AFE_GetWifiSSID

    private void AFE_GetWifiSSID_Service(MessageData data, string logid)
    {
        const string method_name = nameof(AFE_GetWifiSSID_Service);
        try
        {
            _logger.LogDebug("[{name}]:{logid}, start...", method_name, logid);
            var xdata = (AFEGetWifiSSID_)data.EventData;
            var bytes = new byte[xdata.count];
            unsafe
            {
                Marshal.Copy((IntPtr)xdata.content, bytes, 0, bytes.Length);
            }

            var ssid = Encoding.UTF8.GetString(bytes);
            _logger.LogInformation("[{name}]:{logid}, ssid: {ssid}", method_name, logid, ssid);
            if (!_current.ContainsKey(data.Key))
            {
                return;
            }

            var ispass = _current.TryGetValue(data.Key, out var xpen);
            if (!ispass || xpen == null)
            {
                return;
            }

            xpen.Ssid = ssid;
            RunSync
            (
                async () =>
                {
                    await _pen.UpdateSsid(data.Mac, ssid).ConfigureAwait(false);
                    var zdata = new PenWifiData
                    {
                        Ssid = ssid
                    };
                    await PushAsync(data.Mac, PenDataType.Ssid, zdata.ToJsonString()).ConfigureAwait(false);
                }
            );
        }
        catch (Exception e)
        {
            _logger.LogCritical(e, "[{name}]: {e}", method_name, e.Message);
        }
    }

    #endregion

    #region AFE_UpdateFW

    private void AFE_UpdateFW_Service(MessageData data, string logid)
    {
        const string method_name = nameof(AFE_UpdateFW_Service);
        try
        {
            var xdata = (AFEUpdateFW_)data.EventData;
            _logger.LogInformation("[{name}]:{logid}, fwresult: {res}", method_name, logid, xdata.res.ToString());
        }
        catch (Exception e)
        {
            _logger.LogCritical(e, "[{name}]: {e}", method_name, e.Message);
        }
    }

    #endregion

    #region AFE_UpdateFWProgress

    private void AFE_UpdateFWProgress_Service(MessageData data, string logid)
    {
        const string method_name = nameof(AFE_UpdateFWProgress_Service);
        try
        {
            var xdata = (AFEUpdateFWProgress_)data.EventData;
            _logger.LogInformation("[{name}]:{logid}, progress: {progress}", method_name, logid, xdata.progress.ToString());
        }
        catch (Exception e)
        {
            _logger.LogCritical(e, "[{name}]: {e}", method_name, e.Message);
        }
    }

    #endregion

    #region AFE_OfflineTaskReady

    private void AFE_OfflineTaskReady_Service(MessageData data, string logid)
    {
        const string method_name = nameof(AFE_OfflineTaskReady_Service);
        try
        {
            var xdata = (AFEOfflineTaskReady_)data.EventData;
            _logger.LogInformation("[{name}]:{logid}, state: {state}", method_name, logid, xdata.state.ToString());
            var ispass = _current.TryGetValue(data.Key, out var pen);
            if (!ispass || pen == null)
            {
                return;
            }

            if (string.IsNullOrWhiteSpace(pen.Ssid))
            {
                Instance.AFAP_GetWifiSSID(data.Mac);
            }

            if (string.IsNullOrWhiteSpace(pen.Battery))
            {
                Instance.AFAP_GetBattery(data.Mac);
            }

            if (string.IsNullOrWhiteSpace(pen.Version))
            {
                Instance.AFAP_GetFirmwareVer(data.Mac);
            }

            //获取休眠时间
            Instance.AFAP_RequestGetStandbyTime(data.Mac);

            if (!string.IsNullOrWhiteSpace(pen.Version) && pen.Version.Contains("62RC", StringComparison.OrdinalIgnoreCase))
            {
                AFMediaPlayer.RequestRecorderErase(data.Mac);
            }
            //是否有离线数据
            var is_exists_offline = pen.OfflineDots.IsEmpty;
            if (is_exists_offline)
            {
                return;
            }

            var isclear = RunSync
            (
                async () =>
                {
                    var dots = pen.OfflineDots;
                    var total_count = dots.Count;
                    var userrole = await _pen_mapping_redis_service.HGetPenRoleAsync(data.Mac);
                    if (userrole != 1)
                    {
                        var diclist = new ConcurrentDictionary<int, List<PenDot>>();
                        var xylist = new List<PenDot>();
                        var index = 1;
                        foreach (var dot in dots)
                        {
                            xylist.Add(dot);
                            if (dot.Type != 2)
                            {
                                continue;
                            }

                            diclist.TryAdd(index, xylist);
                            xylist = [];
                            index++;
                        }
                        foreach (var item in diclist)
                        {
                            if (item.Value.Count <= 0)
                            {
                                continue;
                            }

                            var current = item.Value.FirstOrDefault();
                            if (current == null)
                            {
                                continue;
                            }

                            var pendata = new PenDotData
                            {
                                Mac = data.Mac,
                                Page = current.Page,
                                Time = DateTime.Now,
                                PageId = current.Page > 1000 ? current.Page : 0,
                                Dots = item.Value.ToList(),
                                DataType = 2
                            };

                            //推送离线点位实时数据
                            await _pen.AddPenLogOffline(pendata).ConfigureAwait(false);
                            _logger.LogInformation
                            (
                                "[add offline dots]: id: {id} , mac: {mac} , page: {current_page} , dotcounts: {count}, add offline data", data.Id, data.Mac, current.Page.ToString(), item.Value.Count.ToString()
                            );
                        }

                        pen.OfflineDots.Clear();
                    }
                    else
                    {
                        //勾选学号笔迹
                        var dic_check_studentno_dots = new ConcurrentDictionary<int, List<PenDot>>();
                        //批改笔迹
                        var dic_correct_paper_dots = new ConcurrentDictionary<int, List<PenDot>>();
                        var current_index = 0;
                        var current_pageid = 10;
                        foreach (var item in dots)
                        {
                            if (item.Page is 5 or 6)
                            {
                                continue;
                            }

                            var page = item.Page;
                            //过滤选中学号纸之前的所有无效点位数据
                            if (current_index == 0 && page != 10)
                            {
                                continue;
                            }

                            if (current_pageid != page || current_index == 0)
                            {
                                if (page == 10 || current_index == 0)
                                {
                                    current_index++;
                                }
                            }

                            if (page == 10)
                            {
                                if (!dic_check_studentno_dots.TryGetValue(current_index, out var value))
                                {
                                    value = [];
                                    dic_check_studentno_dots[current_index] = value;
                                }

                                value.Add(item);
                            }
                            else
                            {
                                //在批改实际页码
                                if (!dic_correct_paper_dots.TryGetValue(current_index, out var value))
                                {
                                    value = [];
                                    dic_correct_paper_dots[current_index] = value;
                                }

                                value.Add(item);
                            }

                            //缓存上一次批改的页码
                            current_pageid = page;
                        }

                        var pendata = new OfflineCorrectPenDotData
                        {
                            RequestId = _generator.NewStringId(),
                            Mac = data.Mac,
                            StudentNoDots = dic_check_studentno_dots,
                            CorrectDots = dic_correct_paper_dots
                        };
                        await _pen.PushTeacherOfflineDotsAsync(pendata).ConfigureAwait(false);
                        _logger.LogInformation
                        (
                            "[push teacher offline dots]: id: {id} , mac: {mac} , requestid: {requestid} , add offline data , data: {data}", data.Id, data.Mac, pendata.RequestId, pendata.ToJsonString()
                        );
                        pen.OfflineDots.Clear();
                    }

                    return total_count;
                }
            );
            if (isclear <= 0)
            {
                return;
            }
            //离线数据传输完成,清理笔中的离线数据
            Instance.AFAP_DeleteOfflineData(data.Mac);
            _logger.LogInformation("mac:{} 调用清除离线笔迹的方法 AFAP_DeleteOfflineData",data.Mac);
        }
        catch (Exception e)
        {
            _logger.LogCritical(e, "[{name}]: {e}", method_name, e.Message);
        }
    }

    #endregion

    #region AFE_ClearStorage

    private void AFE_ClearStorage_Service(MessageData data, string logid)
    {
        const string method_name = nameof(AFE_ClearStorage_Service);
        try
        {
            var xdata = (AFEClearStorage_)data.EventData;
            _logger.LogInformation("[{name}]:{logid}, clear state: {state}", method_name, logid, xdata.bSuccess.ToString());
        }
        catch (Exception e)
        {
            _logger.LogCritical(e, "[{name}]: {e}", method_name, e.Message);
        }
    }

    #endregion

    #region AFE_GetStandbyTime

    private void AFE_GetStandbyTime_Service(MessageData data, string logid)
    {
        const string method_name = nameof(AFE_GetStandbyTime_Service);
        try
        {
            var xdata = (AFEGetStandbyTime_)data.EventData;
            _logger.LogDebug
            (
                "[{name}]:{logid}, standby: {sec_standby} , powerdown: {min_powerdown}", method_name, logid, xdata.sec_standby.ToString(), xdata.min_powerdown.ToString()
            );
            if (!_current.ContainsKey(data.Key))
            {
                return;
            }

            var ispass = _current.TryGetValue(data.Key, out var xpen);
            if (!ispass || xpen == null)
            {
                return;
            }

            xpen.StandBy = xdata.min_powerdown;
            if (xpen.StandBy != _pen_config.StandBy)
            {
                Instance.AFAP_RequestSetPowerDownTime(data.Mac, _pen_config.StandBy);
            }
        }
        catch (Exception e)
        {
            _logger.LogCritical(e, "[{name}]: {e}", method_name, e.Message);
        }
    }

    #endregion

    #endregion

    #region Implementation of AF_IAFPenUpdaterReport

    /// <inheritdoc />
    public void onAFPenUpdaterReport(ulong id, int status, int progress)
    {
        const string method_name = nameof(onAFPenUpdaterReport);
        var xid = id.LongToString();
        var mac = xid.ToMac();
        _logger.LogInformation
        (
            "[{name}] id: {xid} , mac: {mac} , status: {status} , progress: {progress}", method_name, xid, mac, status.ToString(), progress.ToString()
        );
        var xstatus = (AFUpdaterReport)status;
        var logid = $" id: {xid} , mac: {mac} , status: {status} , progress: {progress}";
        var showtxt = "";
        showtxt = xstatus switch
        {
            AFUpdaterReport.WriteOTAReport => "Report",
            AFUpdaterReport.WriteOTADone => "Finished",
            AFUpdaterReport.FWWriteReport => "Write",
            AFUpdaterReport.StatusWriteOTABatLowErr => "LowBattery Error",
            _ => showtxt
        };
        RunSync
        (
            async () =>
            {
                //升级完成清理升级标识
                if (progress == 100 || xstatus == AFUpdaterReport.FWWriteReport)
                {
                    await _redis_service.HDelAsync("mac", mac).ConfigureAwait(false);
                }
                _logger.LogInformation
    (
        "[{name}] id: {xid} , mac: {mac} , status: {status} , progress: {progress}", method_name, xid, mac, status.ToString(), progress.ToString()
    );
                var zdata = new PenProgressData
                {
                    Status = xstatus.GetHashCode(),
                    Progress = progress
                };
                await PushAsync(mac, PenDataType.Progress, zdata.ToJsonString()).ConfigureAwait(false);
            }
        );
        _logger.LogInformation("[{name}]: {logid} , {showtxt}", method_name, logid, showtxt);
    }

    #endregion

    #region Implementation of AF_IAFPenMediaPlayer

    /// <inheritdoc />
    public void onMediaPlayerCallback(string mac, AFE_MediaPlayer_EVENT_TYPE type, int state, byte[] bytes)
    {
        if (type == AFE_MediaPlayer_EVENT_TYPE.MediaPlayerEventRecorderErase)
        {
            return;
        }

        const string method_name = nameof(onMediaPlayerCallback);
        var bytes_count = bytes is
        {
            Length: > 0
        }
            ? bytes.Length
            : 0;
        _logger.LogInformation("[{method_name}]: mac: {mac} , type: {type} , state: {state} , bytes: {bytes_length}", method_name, mac, Enum.GetName(type), state.ToString(), bytes_count.ToString());
        if (type != AFE_MediaPlayer_EVENT_TYPE.MediaPlayerEventRecorderFileCnt && type != AFE_MediaPlayer_EVENT_TYPE.MediaPlayerEventRecorderDownloadFile && type != AFE_MediaPlayer_EVENT_TYPE.MediaPlayerEventRecorderStop
            &&type!=AFE_MediaPlayer_EVENT_TYPE.MediaPlayerEventPlayerGetStatus)
        {
            return;
        }

        var xmac = mac.ToMac();

        switch (type)
        {
            case AFE_MediaPlayer_EVENT_TYPE.MediaPlayerEventRecorderFileCnt:
                {
                    if (state > 0)
                    {
                        AFMediaPlayer.RequestRecorderDownloadFile(mac, state);
                    }

                    break;
                }
            case AFE_MediaPlayer_EVENT_TYPE.MediaPlayerEventRecorderDownloadFile when bytes_count <= 0:
                return;
            case AFE_MediaPlayer_EVENT_TYPE.MediaPlayerEventRecorderDownloadFile:
                {
                    var ispass = _current_record.TryGetValue(xmac, out var data);
                    if (!ispass || data == null)
                    {
                        return;
                    }

                    RunSync
                    (
                        async () =>
                        {
                            var basedir = Path.Combine(AppContext.BaseDirectory, "Audio");
                            if (!Directory.Exists(basedir))
                            {
                                Directory.CreateDirectory(basedir);
                            }

                            var name = $"{_generator.NewId():N}.amr";
                            var file = Path.Combine(basedir, name);
                            await File.WriteAllBytesAsync(file, bytes);
                            var url = await _pen.ObsUpload(file, name, "audio/amr").ConfigureAwait(false);
                            if (string.IsNullOrWhiteSpace(url))
                            {
                                return;
                            }

                            _logger.LogInformation("[{name}]: mac: {xmac} , type: {type}", method_name, xmac, Enum.GetName(type));
                            var xdata = _mapper.Map<PenRecordData>(data);
                            xdata.FileUrl = url;
                            await _pen.PenRecord(xdata).ConfigureAwait(false);
                        }
                    );
                    break;
                }
            case AFE_MediaPlayer_EVENT_TYPE.MediaPlayerEventPlayerGetStatus:
                {
                    if (state==3&&IsExistRecord(xmac))
                    {
                        RemoveRecord(xmac);
                        AFMediaPlayer.RequestRecorderStart(xmac);
                    }

                    break;
                }
        }
    }

    #endregion

    #region Business

    /// <summary>
    /// 推送实时消息
    /// </summary>
    /// <param name="mac"></param>
    /// <param name="type"></param>
    /// <param name="data"></param>
    private async Task PushAsync(string mac, PenDataType type, string data)
    {
        try
        {
            if (AllPenClients.IsEmpty)
            {
                return;
            }

            var ispass = AllPenClients.TryGetValue(mac, out var clients);
            if (!ispass || clients is not { Count: > 0 })
            {
                return;
            }
            await Parallel.ForEachAsync
            (
                clients, _parallel_options, async (x, token) =>
                {
                    var issocket = AllSocketClients.TryGetValue(x, out var socket);
                    if (issocket && socket is { State: WebSocketState.Open })
                    {
                        var pendata = new PenSocketData
                        {
                            ClientId = x,
                            Mac = mac,
                            TypeId = type,
                            Content = data
                        };
                        await SendAsync(socket, pendata.ToJsonString(), token).ConfigureAwait(false);
                    }
                }
            );
        }
        catch (Exception e)
        {
            _logger.LogCritical(e, "{error}", e.Message);
        }
    }

    /// <summary>
    /// 实时监控
    /// </summary>
    /// <param name="mac">编号</param>
    /// <param name="state">状态</param>
    private async Task MonitorAsync(string mac, PenStateData state)
    {
        try
        {
            if (AllMonitorClients.IsEmpty)
            {
                return;
            }

            await Parallel.ForEachAsync
            (
                AllMonitorClients, _parallel_options, async (x, token) =>
                {
                    var socket = x.Value;
                    if (socket is { State: WebSocketState.Open })
                    {
                        var pendata = new PenMonitorData
                        {
                            TypeId = 2,
                            Content = new PenMonitorContentData
                            {
                                Mac = mac,
                                State = state
                            }
                        };
                        await SendAsync(socket, pendata.ToJsonString(), token).ConfigureAwait(false);
                    }
                }
            );
        }
        catch (Exception e)
        {
            _logger.LogCritical(e, "{error}", e.Message);
        }
    }

    /// <summary>
    /// 获取录制开始相关提示音配置
    /// </summary>
    /// <returns></returns>
    private async Task<VoiceTipsDto> GetRecordStartTipsConfigAsync()
    {
        var key = $"PenServerRecordStartTips";

        return await _redis_service.GetAsync<VoiceTipsDto>(key);
    }


    #region  录制记录

    static readonly ConcurrentDictionary<string, int> macRecords = new ConcurrentDictionary<string, int>();

    static void AddRecord(string mac, int value)
    {
        macRecords.AddOrUpdate(mac, value, (k, v) => v+1);
    }

    static void RemoveRecord(string mac)
    {
        macRecords.TryRemove(mac, out int value);
    }

    static bool IsExistRecord(string mac)
    {
        return macRecords.ContainsKey(mac);
    }

    #endregion

    #region 日志相关

    /// 点位数据(为日志方法)
    /// </summary>
    /// <param name="type">1.实时 2.离线</param>
    /// <param name="dot"></param>
    private static void HandleDotsForLog(int type, DotDto dot)
    {
        var x = dot.RawX;
        var y = dot.RawY;
        var page = dot.page;
        var result = FixDotForLog(page, x, y, false);
        dot.X = result.Item1;
        dot.Y = result.Item2;
    }

    /// <summary>
    /// 点位纠偏
    /// </summary>
    /// <param name="page">页码</param>
    /// <param name="x">X</param>
    /// <param name="y">Y</param>
    /// <param name="isfix"></param>
    /// <returns></returns>
    private static (ushort, ushort) FixDotForLog(uint page, int x, int y, bool isfix = false)
    {
        var fx = Convert.ToUInt16(x / 3);
        var fy = Convert.ToUInt16(y / 3);
        if (page is not (5 or 6))
        {
            return (fx, fy);
        }

        var xfx = fx;
        var xfy = fy;
        if (isfix)
        {
            if (page == 5)
            {
                fx = Convert.ToUInt16((x - 36) / 3);
                fy = Convert.ToUInt16((y + 36) / 3);
            }
            else
            {
                fx = Convert.ToUInt16((x + 9) / 3);
                fy = Convert.ToUInt16((y - 30) / 3);
            }
        }

        if (fx <= 0)
        {
            fx = xfx;
        }

        if (fy <= 0)
        {
            fy = xfy;
        }

        return (fx, fy);
    }

    #endregion

    #endregion

    /// <summary>
    /// Get Pen
    /// </summary>
    /// <param name="macidstr"></param>
    /// <returns></returns>
    public static PenPrensenter GetPen(string macidstr)
    {
        var xid = macidstr.StringToLong();

        _current.TryGetValue(xid, out var zpen);

        return zpen;
    }
}