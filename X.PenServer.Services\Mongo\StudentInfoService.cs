﻿// -- Function: StudentInfoService.cs
// --- Project: X.PenServer.Services
// ---- Remark: 
// ---- Author: Lucifer
// ------ Date: 2023/10/31 15:29

namespace X.PenServer.Services.Mongo;

/// <inheritdoc cref="IStudentInfoService" />
public class StudentInfoService : MongoService<StudentInfo>, IStudentInfoService
{
    /// <inheritdoc />
    public StudentInfoService(IMongoConfig config) : base(config)
    {
    }

    /// <inheritdoc />
    protected override void CreateIndex(IMongoCollection<StudentInfo> collection)
    {
        var builder = Builders<StudentInfo>.IndexKeys
            .Ascending(x => x.UserId)
            .Ascending(x => x.StudentNo)
            .Ascending(x => x.SchoolId)
            .Ascending(x => x.UserName)
            .Ascending(x => x.RealName)
            .Ascending(x => x.Mobile);
        collection.Indexes.CreateIndex(builder, collection.CollectionNamespace.CollectionName);
    }

    /// <inheritdoc />
    public async Task<StudentInfo> GetStudentInfoAsync(string classid, string studentno)
    {
        return await GetAsync(x => x.ClassId.Equals(classid) && x.StudentNo.Equals(studentno));
    }

    /// <inheritdoc />
    public async Task<StudentInfo> GetStudentInfoAsync(string studentid)
    {
        return await GetAsync(x => x.UserId.Equals(studentid));
    }
}