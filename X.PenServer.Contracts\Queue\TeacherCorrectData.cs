﻿using System.Text.Json.Serialization;

namespace X.PenServer.Contracts.Queue;

/// <summary>
/// 教师批改笔迹
/// </summary>
public class TeacherCorrectData
{
	/// <summary>
	/// 正在批改的试卷Id
	/// </summary>
	[JsonPropertyName(nameof(PaperId))]
	[JsonInclude]
	public string PaperId { get; set; }

	/// <summary>
	/// 批改的学生的Id
	/// </summary>
	[Json<PERSON>ropertyName(nameof(StudentId))]
	[JsonInclude]
	public string StudentId { get; set; }

	[JsonPropertyName(nameof(PageId))]
	[JsonInclude]
	public int PageId { get; set; }

	/// <summary>
	/// 笔迹开始时间
	/// </summary>
	[JsonPropertyName(nameof(StartTime))]
	[JsonInclude]
	public DateTime StartTime { get; set; }

	/// <summary>
	/// 笔迹结束时间
	/// </summary>
	[JsonPropertyName(nameof(EndTime))]
	[JsonInclude]
	public DateTime EndTime { get; set; }
}