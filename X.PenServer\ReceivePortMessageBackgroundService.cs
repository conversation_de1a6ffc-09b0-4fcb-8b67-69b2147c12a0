﻿using AFPen.Public;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System.Net.WebSockets;
using X.PenServer.Contracts;
using X.PenServer.Infrastructure;
using X.PenServer.Interfaces;
using X.PenServer.Interfaces.Redis;
using static System.Threading.Tasks.Task;
using static X.PenServer.Implement.PenService;
using static X.PenServer.Middlewares.MiddlewareExtensions;
using static X.PenServer.Middlewares.PenServerSocketMiddleware;
using static X.PenServer.Middlewares.RealtimeMonitorMiddleware;
using static X.PenServer.Storage.ReceivePortMessageStorage;

namespace X.PenServer
{
    public class ReceivePortMessageBackgroundService : IHostedService, IDisposable
    {
        readonly ILogger<ReceivePortMessageBackgroundService> _logger;
        Timer _timer;
        readonly IPenBusiness _pen;
        readonly ParallelOptions _parallel_options;
        readonly IPenMappingRedisService _pen_mapping_redis_service;

        public ReceivePortMessageBackgroundService(
            ILogger<ReceivePortMessageBackgroundService> logger,
            IPenBusiness pen,
            ParallelOptions parallel_options,
            IPenMappingRedisService pen_mapping_redis_service
            )
        {
            _logger=logger;
            _pen=pen;
            _parallel_options=parallel_options;
            _pen_mapping_redis_service=pen_mapping_redis_service;
        }

        public Task StartAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Job ReceivePortMessageBackgroundService 启动");

            _timer = new Timer(async (object obj) => { await Progress(obj); }, null, TimeSpan.Zero, TimeSpan.FromSeconds(5));

            return CompletedTask;
        }

        private async Task Progress(object obj)
        {
            while (GetMessageQueue().TryDequeue(out ReceivedPortMessageDto item))
            {
                try
                {
                    if (item==null)
                    {
                        continue;
                    }

                    if (!(item.Msgtype==AFPortMsgType.PEN_CONNECTION_SUCCESS
                        ||item.Msgtype==AFPortMsgType.PEN_DISCONNECTED))
                    {
                        continue;
                    }

                    var pen = GetPen(item.MacIdStr);
                    var logid = $" id: {item.MacIdLong} , mac: {item.Mac}";
                    var type = "1";
                    var logmsg = "connect";

                    var zdata = new PenStateData
                    {
                        State = 1
                    };

                    if (item.Msgtype==AFPortMsgType.PEN_DISCONNECTED)
                    {
                        type="0";
                        logmsg="disconnect";
                        zdata.State = 0;
                    }
                    var userId = await GetUserIdAsync(item.Mac);

                    if (string.IsNullOrWhiteSpace(userId))
                    {
                        _logger.LogInformation("点阵笔未绑定: {}", logid);
                    }
                    else
                    {
                        var result = await _pen.UpdateConnectState(item.Mac, type).ConfigureAwait(false);
                        _logger.LogInformation("[update state from {}]:{logid} type:{}", logmsg, logid, type);

                        if (item.Msgtype==AFPortMsgType.PEN_CONNECTION_SUCCESS)
                        {
                            var usertype = await _pen_mapping_redis_service.HGetPenRoleAsync(item.Mac);

                            await _pen.SyncUserLoginLogAsync(userType: usertype, userId: userId);
                        }
                    }

                    //实时推送更新状态
                    await PushAsync(item.Mac, PenDataType.State, zdata.ToJsonString()).ConfigureAwait(false);
                    //实时监控
                    if (pen != null)
                    {
                        await MonitorAsync(item.Mac, zdata).ConfigureAwait(false);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("ReceivePortMessageBackgroundService Ex:{}", ex.ToString());
                }
            }
        }

        public Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Job ReceivePortMessageBackgroundService 停止");

            return CompletedTask;
        }

        public void Dispose()
        {
            _timer?.Dispose();
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 推送实时消息
        /// </summary>
        /// <param name="mac"></param>
        /// <param name="type"></param>
        /// <param name="data"></param>
        private async Task PushAsync(string mac, PenDataType type, string data)
        {
            try
            {
                if (AllPenClients.IsEmpty)
                {
                    return;
                }

                var ispass = AllPenClients.TryGetValue(mac, out var clients);
                if (!ispass || clients is not { Count: > 0 })
                {
                    return;
                }
                await Parallel.ForEachAsync
                (
                    clients, _parallel_options, async (x, token) =>
                    {
                        var issocket = AllSocketClients.TryGetValue(x, out var socket);
                        if (issocket && socket is { State: WebSocketState.Open })
                        {
                            var pendata = new PenSocketData
                            {
                                ClientId = x,
                                Mac = mac,
                                TypeId = type,
                                Content = data
                            };
                            await SendAsync(socket, pendata.ToJsonString(), token).ConfigureAwait(false);
                        }
                    }
                );
            }
            catch (Exception e)
            {
                _logger.LogCritical(e, "{error}", e.Message);
            }
        }

        /// <summary>
        /// 实时监控
        /// </summary>
        /// <param name="mac">编号</param>
        /// <param name="state">状态</param>
        private async Task MonitorAsync(string mac, PenStateData state)
        {
            try
            {
                if (AllMonitorClients.IsEmpty)
                {
                    return;
                }

                await Parallel.ForEachAsync
                (
                    AllMonitorClients, _parallel_options, async (x, token) =>
                    {
                        var socket = x.Value;
                        if (socket is { State: WebSocketState.Open })
                        {
                            var pendata = new PenMonitorData
                            {
                                TypeId = 2,
                                Content = new PenMonitorContentData
                                {
                                    Mac = mac,
                                    State = state
                                }
                            };
                            await SendAsync(socket, pendata.ToJsonString(), token).ConfigureAwait(false);
                        }
                    }
                );
            }
            catch (Exception e)
            {
                _logger.LogCritical(e, "{error}", e.Message);
            }
        }

        /// <summary>
        /// GET USERID
        /// </summary>
        /// <param name="mac"></param>
        /// <returns></returns>
        private async Task<string> GetUserIdAsync(string mac)
        {
            return await _pen_mapping_redis_service.HGetMappingUserAsync(mac);
        }
    }
}
