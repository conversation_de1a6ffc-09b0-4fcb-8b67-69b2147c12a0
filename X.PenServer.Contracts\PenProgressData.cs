﻿//  -- Function: PenProgressData.cs
//  --- Project: PenServer
//  ---- Remark:
//  ---- Author: Lucifer
//  ------ Date: 2023/03/31 15:06

using System.Text.Json.Serialization;

namespace X.PenServer.Contracts;

/// <summary>
/// 升级进度数据
/// </summary>
[Serializable]
public class PenProgressData
{
	/// <summary>
	/// 状态
	/// </summary>
	[JsonPropertyName(nameof(Status))]
	[JsonInclude]
	public int Status { get; set; }

	/// <summary>
	/// 进度
	/// </summary>
	[JsonPropertyName(nameof(Progress))]
	[JsonInclude]
	public int Progress { get; set; }
}