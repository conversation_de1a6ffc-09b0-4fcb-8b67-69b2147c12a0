﻿// -- Function: InitMatrixService.cs
// --- Project: X.PenServer.PenQueue
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2024/2/20 15:12

using X.PenServer.Interfaces.Redis;

namespace X.PenServer.PenQueue.Implement;

using Infrastructure;

/// <summary>
/// 矩阵服务
/// </summary>
public static class MatrixService
{
	#region 初始化数据

	/// <summary>
	/// 英语单元答题卡
	/// </summary>
	public static IReadOnlyList<MatrixRegion> EnglishUnitCardMatrices { get; private set; }

	/// <summary>
	/// 通用互动答题卡
	/// </summary>
	public static MatrixRegion UniversalCardMatrix { get; private set; }

	/// <summary>
	/// 练习册标签纸
	/// </summary>
	public static List<MatrixItem> PaperTagStudentNoMatrices { get; private set; }

	/// <summary>
	/// 练习薄
	/// </summary>
	public static List<MatrixItemRange> WorkbookMatrices { get; private set; }

	/// <summary>
	/// 异步初始化矩阵数据
	/// </summary>
	public static async Task InitAsync()
	{
		var provider = AutofacProvider.GetService<ICommonRedisService>();
		EnglishUnitCardMatrices = await provider.HGetAsync<List<MatrixRegion>>(RedisKeys.MATRIX, RedisKeys.ENGLISH_UNIT_CARD).ConfigureAwait(false);
		UniversalCardMatrix = await provider.HGetAsync<MatrixRegion>(RedisKeys.MATRIX, RedisKeys.UNIVERSAL_CARD).ConfigureAwait(false);
		PaperTagStudentNoMatrices = await provider.HGetAsync<List<MatrixItem>>(RedisKeys.MATRIX, RedisKeys.PAPER_TAG_STUDENT_NO).ConfigureAwait(false);
		WorkbookMatrices = await provider.HGetAsync<List<MatrixItemRange>>(RedisKeys.MATRIX, RedisKeys.WORKBOOK).ConfigureAwait(false);
	}

	#endregion

	#region 英语单元答题卡

	#region 是否在有效区域作答

	/// <summary>
	/// 判断英语答题卡是否在有效区域作答
	/// </summary>
	/// <param name="coordy">X</param>
	/// <param name="coordx">Y</param>
	/// <param name="page">页码</param>
	/// <param name="is_include_pageno_region">是否验证卷码区域</param>
	/// <returns></returns>
	public static bool IsInEffectiveEnglishUnitCard(float coordx, float coordy, int page, bool is_include_pageno_region = false)
	{
		var regions = is_include_pageno_region ? EnglishUnitCardMatrices.Where(x => x.RegionType != 8) : EnglishUnitCardMatrices.Where(x => x.RegionType != 7 && x.RegionType != 8);
		return regions.Select(region => region.IsHitRegion(coordx, coordy, page)).Any(ishit => ishit);
	}

	#endregion

	#region 定位题目区域

	/// <summary>
	/// 定位题目区域
	/// </summary>
	/// <param name="coordx">X</param>
	/// <param name="coordy">Y</param>
	/// <param name="page">页码</param>
	/// <returns></returns>
	public static MatrixDetail EnglishUnitLocateItemRegion(float coordx, float coordy, int page)
	{
		var regions = EnglishUnitCardMatrices.Where(x => x.RegionType != 7 && x.RegionType != 8);
		return (from region in regions
				let is_hit = region.IsHitRegion(coordx, coordy, page)
				where is_hit
				let matrix = region.Ranges.Locate(coordx, coordy, page)
				where matrix != null
				let item = matrix.Items.Locate(coordx, coordy, page)
				where item != null
				select new MatrixDetail
				{
					Item = item,
					RegionType = region.RegionType,
					ItemNo = matrix.ItemNo,
					RegionName = region.Name,
					Score = matrix.Score,
					IsMultiScore =
						matrix.Items.Count(x => x.Type == 2) > 1 &&
						matrix.Items.Count(x => x.Type == 1) == 1
				}).FirstOrDefault();
	}

	#endregion

	#region 定位主观题目区域

	/// <summary>
	/// 定位主观题目区域
	/// </summary>
	/// <param name="coordx">X</param>
	/// <param name="coordy">Y</param>
	/// <param name="page">页码</param>
	/// <returns></returns>
	public static MatrixDetail EnglishUnitLocateSubjectiveItemRegion(float coordx, float coordy, int page)
	{
		var regions = EnglishUnitCardMatrices.Where(x => x.RegionType != 1 && x.RegionType != 7 && x.RegionType != 8);
		return (from region in regions
				let is_hit = region.IsHitRegion(coordx, coordy, page)
				where is_hit
				let matrix = region.Ranges.Locate(coordx, coordy, page)
				where matrix != null
				let item = matrix.Items.Locate(coordx, coordy, page)
				where item != null
				select new MatrixDetail
				{
					Item = item,
					RegionType = region.RegionType,
					ItemNo = matrix.ItemNo,
					RegionName = region.Name,
					Score = matrix.Score,
					IsMultiScore =
						matrix.Items.Count(x => x.Type == 2) > 1 &&
						matrix.Items.Count(x => x.Type == 1) == 1
				}).FirstOrDefault();
	}

	#endregion

	#region 获取学号

	/// <summary>
	/// 获取学号
	/// </summary>
	/// <param name="coordx">X</param>
	/// <param name="coordy">Y</param>
	/// <param name="page">页码</param>
	/// <returns></returns>
	public static MatrixItem LocateStudentNoRegion(float coordx, float coordy, int page)
	{
		var region = EnglishUnitCardMatrices.FirstOrDefault(x => x.RegionType == 8);
		if (region == null || !region.IsHitRegion(coordx, coordy, page))
		{
			return null;
		}

		var range = region.Ranges.FirstOrDefault();
		if (range == null || !range.IsHitRegion(coordx, coordy, page))
		{
			return null;
		}

		var result = range.Items.Locate(coordx, coordy, page);
		return result;
	}

	#endregion

	#endregion

	#region 通用互动答题卡

	#region 是否在有效区域作答

	/// <summary>
	/// 判断答题卡是否在有效区域作答
	/// </summary>
	/// <param name="coordy">X</param>
	/// <param name="coordx">Y</param>
	/// <param name="page">页码</param>
	/// <returns></returns>
	public static bool IsInEffectiveCard(float coordx, float coordy, int page)
	{
		var regions = UniversalCardMatrix.Ranges;
		return regions.Select(region => region.IsHitRegion(coordx, coordy, page)).Any(ishit => ishit);
	}

	#endregion

	#region 定位题目

	/// <summary>
	/// 定位题目
	/// </summary>
	/// <param name="coordx">X</param>
	/// <param name="coordy">Y</param>
	/// <param name="page">页码</param>
	/// <returns></returns>
	public static MatrixDetail LocateItemRegion(float coordx, float coordy, int page)
	{
		var ranges = UniversalCardMatrix.Ranges;
		return (from range in ranges
				let is_hit = range.IsHitRegion(coordx, coordy, page)
				where is_hit
				let item = range.Items.Locate(coordx, coordy, page)
				where item != null
				select new MatrixDetail
				{
					Item = item,
					RegionType = UniversalCardMatrix.RegionType,
					ItemNo = range.ItemNo,
					RegionName = UniversalCardMatrix.Name,
					Score = 0,
					IsMultiScore = false
				}).FirstOrDefault();
	}

	#endregion

	#endregion

	#region 练习薄

	#region 定位行列区域

	/// <summary>
	/// 定位行列区域
	/// </summary>
	/// <param name="coordx">X</param>
	/// <param name="coordy">Y</param>
	/// <returns></returns>
	public static MatrixItem LocateWorkbookRegion(float coordx, float coordy)
	{
		var ranges = WorkbookMatrices;
		return (from range in ranges
				let item = range.Items.Locate(coordx, coordy)
				where item != null
				select item).FirstOrDefault();
	}

	#endregion

	#endregion
}