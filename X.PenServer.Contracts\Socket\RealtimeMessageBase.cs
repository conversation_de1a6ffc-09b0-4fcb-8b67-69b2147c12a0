﻿//   -- Function: RealtimeMessageBase.cs
//   --- Project: Uwoo.ContractModels
//   ---- Remark:
//   ---- Author: Lucifer
//   ------ Date: 2023/09/19 16:54

using System.Text.Json.Serialization;

namespace X.PenServer.Contracts.Socket;

public class RealtimeMessageBase
{
	/// <summary>
	/// 班级id
	/// </summary>
	[JsonPropertyName(nameof(ClassId))]
	[JsonInclude]
	public string ClassId { get; set; }

	/// <summary>
	/// 用户id
	/// </summary>
	[JsonPropertyName(nameof(UserId))]
	[JsonInclude]
	public string UserId { get; set; }

	/// <summary>
	/// 用户类型
	/// </summary>
	/// <remarks>0:学生 1:教师</remarks>
	[JsonPropertyName("userType")]
	[JsonInclude]
	public int UserType { get; set; }
}