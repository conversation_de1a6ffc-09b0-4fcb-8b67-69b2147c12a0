﻿// -- Function: RealtimeEachCorrectModel.cs
//  --- Project: X.PenServer.Contracts
//  ---- Remark: 
//  ---- Author: Lucifer
//  ------ Date: 2024/05/30 14:05

using System.Text.Json.Serialization;

namespace X.PenServer.Contracts.Socket;

/// <summary>
/// 互批信息
/// </summary>
public class RealtimeEachCorrectModel
{
    /// <summary>
    /// 试卷id
    /// </summary>
    [JsonPropertyName(nameof(PaperId))]
    [JsonInclude]
    public string PaperId { get; set; }

    /// <summary>
    /// 班级id
    /// </summary>
    [JsonPropertyName(nameof(ClassId))]
    [JsonInclude]
    public string ClassId { get; set; }

    /// <summary>
    /// 页码id
    /// </summary>
    [JsonPropertyName(nameof(PageId))]
    [JsonInclude]
    public int PageId { get; set; }

    /// <summary>
    /// 学生id
    /// </summary>
    [JsonPropertyName(nameof(StudentId))]
    [JsonInclude]
    public string StudentId { get; set; }

    /// <summary>
    /// 学生名称
    /// </summary>
    [JsonPropertyName(nameof(StudentName))]
    [JsonInclude]
    public string StudentName { get; set; }

    /// <summary>
    /// 学号
    /// </summary>
    [JsonPropertyName(nameof(StudentNo))]
    [JsonInclude]
    public string StudentNo { get; set; }

    /// <summary>
    /// 当前被批阅的学生id
    /// </summary>
    [JsonPropertyName(nameof(ByStudentId))]
    [JsonInclude]
    public string ByStudentId { get; set; }

    /// <summary>
    /// 当前被批阅的学号
    /// </summary>
    [JsonPropertyName(nameof(ByStudentNo))]
    [JsonInclude]
    public string ByStudentNo { get; set; }
}