﻿// -- Function: RealtimeCorrectStudentConsumer.cs
// --- Project: X.PenServer.Sockets
// ---- Remark: 
// ---- Author: Lucifer
// ------ Date: 2023/10/09 15:08

using System.Collections.Immutable;
using System.Net.WebSockets;
using MassTransit;
using X.PenServer.Contracts.Realtime;
using X.PenServer.Contracts.Socket;
using static X.PenServer.Middlewares.MiddlewareExtensions;
using static X.PenServer.Middlewares.RealtimePenSocketMiddleware;

namespace X.PenServer.Sockets.Consumers;

using Infrastructure;

/// <summary>
/// 正在批改的学生队列
/// </summary>

// ReSharper disable once ClassNeverInstantiated.Global
public class RealtimeCorrectStudentConsumer : IConsumer<Batch<RealtimeCorrectStudentModel>>
{
    private readonly ParallelOptions _parallel_options;

    public RealtimeCorrectStudentConsumer(ParallelOptions parallel_options)
    {
        _parallel_options = parallel_options;
    }

    /// <inheritdoc />
    public async Task Consume(ConsumeContext<Batch<RealtimeCorrectStudentModel>> context)
    {
        foreach (var item in context.Message)
        {
            var data = item.Message;
            var wids = GetClassClients(data.ClassId);
            if (wids is null || wids.IsEmpty)
            {
                continue;
            }

            var clients = ImmutableHashSet.Create<WebSocket>();
            clients = wids.Select(GetSocketClient).Where(socket => socket is
            {
                State: WebSocketState.Open
            }).Aggregate(clients, (current, socket) => current.Add(socket));
            if (clients.Count <= 0)
            {
                continue;
            }

            var xdata = new SendData
            {
                Type = "205",
                ClassId = data.ClassId,
                Content = data.ToJsonString()
            };
            var jdata = xdata.ToJsonString();
            await Parallel.ForEachAsync
            (
                clients, _parallel_options, async (x, xtoken) => { await SendAsync(x, jdata, xtoken).ConfigureAwait(false); }
            );
        }

        await context.ConsumeCompleted.ConfigureAwait(false);
    }
}