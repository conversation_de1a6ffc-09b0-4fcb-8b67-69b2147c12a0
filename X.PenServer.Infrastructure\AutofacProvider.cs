﻿// -- Function: AutofacProvider.cs
// --- Project: X.PenServer.Infrastructure
// ---- Remark: 
// ---- Author: Lucifer
// ------ Date: 2023/10/24 15:30

using Autofac;

namespace X.PenServer.Infrastructure;

public static class AutofacProvider
{
    /// <summary>
    /// Autofac容器
    /// </summary>
    public static ILifetimeScope Container { get; set; }

    /// <summary>
    /// 解析服务
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    public static T GetService<T>() where T : class
    {
        var ispass = Container.TryResolve<T>(out var service);
        return ispass ? service : null;
    }
}