﻿//  -- Function: PenData.cs
//  --- Project: PenServer
//  ---- Remark: 
//  ---- Author: Lucifer
//  ------ Date: 2023/03/31 14:49

using System.Text.Json.Serialization;

namespace X.PenServer.Contracts;

public class PenSocketData
{
	/// <summary>
	/// Mac地址
	/// </summary>
	[JsonPropertyName(nameof(Mac))]
	[JsonInclude]
	public string Mac { get; set; }

	/// <summary>
	/// 客户端id
	/// </summary>
	[JsonPropertyName(nameof(ClientId))]
	[JsonInclude]
	public string ClientId { get; set; }

	/// <summary>
	/// 消息类型
	/// </summary>
	[Json<PERSON>ropertyName(nameof(TypeId))]
	[JsonInclude]
	public PenDataType TypeId { get; set; }

	/// <summary>
	/// 消息内容
	/// </summary>
	[JsonPropertyName(nameof(Content))]
	[JsonInclude]
	public string Content { get; set; }
}