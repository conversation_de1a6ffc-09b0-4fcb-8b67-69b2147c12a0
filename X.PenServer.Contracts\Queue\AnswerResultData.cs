﻿// -- Function: AnswerResultData.cs
// --- Project: X.PenServer.Contracts
// ---- Remark: 
// ---- Author: Lucifer
// ------ Date: 2024/1/31 16:29
namespace X.PenServer.Contracts.Queue;

using System.Text.Json.Serialization;

/// <summary>
/// 答题卡作答结果
/// </summary>
public class AnswerResultData
{
	/// <summary>
	/// 试卷id
	/// </summary>
	[JsonPropertyName(nameof(PaperId))]
	[JsonInclude]
	public string PaperId { get; set; }

	/// <summary>
	/// 题目编号
	/// </summary>
	[JsonPropertyName(nameof(ItemNo))]
	[JsonInclude]
	public int ItemNo { get; set; }

	/// <summary>
	/// 点阵笔编号
	/// </summary>
	/// <remarks>学生作答时为学生笔编号, 教师批阅时为教师笔编号</remarks>
	[JsonPropertyName(nameof(Mac))]
	[JsonInclude]
	public string Mac { get; set; }

	/// <summary>
	/// 业务页码id
	/// </summary>
	[JsonPropertyName(nameof(PageId))]
	[JsonInclude]
	public int PageId { get; set; }

	/// <summary>
	/// 实际采集到的页码
	/// </summary>
	[JsonPropertyName(nameof(Page))]
	[JsonInclude]
	public int Page { get; set; }

	/// <summary>
	/// 作答结果
	/// </summary>
	[JsonPropertyName(nameof(Result))]
	[JsonInclude]
	public string Result { get; set; }

	/// <summary>
	/// 题目总分值
	/// </summary>
	[JsonPropertyName(nameof(Score))]
	[JsonInclude]
	public float Score { get; set; }

	/// <summary>
	/// 题目是否包含多个分值
	/// </summary>
	[JsonPropertyName(nameof(IsMultiScore))]
	[JsonInclude]
	public bool IsMultiScore { get; set; }
}