﻿<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>disable</Nullable>
        <ErrorOnDuplicatePublishOutputFiles>false</ErrorOnDuplicatePublishOutputFiles>
        <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
        <AppendRuntimeIdentifierToOutputPath>false</AppendRuntimeIdentifierToOutputPath>
        <!--auto version start-->
        <Deterministic>false</Deterministic>
        <GenerateAssemblyFileVersionAttribute>false</GenerateAssemblyFileVersionAttribute>
        <GenerateAssemblyInformationalVersionAttribute>false</GenerateAssemblyInformationalVersionAttribute>
        <AssemblyVersion>1.0.*</AssemblyVersion>
        <!--auto version end-->
    </PropertyGroup>
    <PropertyGroup>
        <Copyright>Copyright © Lucifer. All rights reserved.</Copyright>
        <PackageId>X.PenServer.Protos</PackageId>
        <PackageProjectUrl>https://github.com/X-Lucifer</PackageProjectUrl>
        <RepositoryUrl>https://github.com/X-Lucifer</RepositoryUrl>
        <Company>X Lucifer</Company>
        <Authors>Lucifer</Authors>
    </PropertyGroup>
    <ItemGroup>
        <Protobuf Include="*.proto" GrpcServices="Server, Client" />
    </ItemGroup>
    <ItemGroup>
      <PackageReference Include="Google.Protobuf" Version="3.27.2" />   
      <PackageReference Include="Grpc.Net.Client" Version="2.64.0" />   
      <PackageReference Include="Grpc.Tools" Version="2.65.0">
        <PrivateAssets>all</PrivateAssets>
        <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      </PackageReference>
      <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.1" />
      <PackageReference Include="Microsoft.Identity.Client.Extensions.Msal" Version="4.62.0" />
      <PackageReference Include="Microsoft.IdentityModel.Abstractions" Version="8.0.1" />
      <PackageReference Include="Microsoft.NETCore.Platforms" Version="7.0.4" />
      <PackageReference Include="System.Diagnostics.DiagnosticSource" Version="8.0.1" />
      <PackageReference Include="System.Security.Cryptography.ProtectedData" Version="8.0.0" />
    </ItemGroup>
</Project>