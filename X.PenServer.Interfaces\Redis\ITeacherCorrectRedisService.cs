﻿// -- Function: ITeacherCorrectRedisService.cs
// --- Project: X.PenServer.Interfaces
// ---- Remark: 
// ---- Author: Lucifer
// ------ Date: 2023/10/31 11:34

using X.Elegant.Redis.Infrastructure;
using X.PenServer.Contracts.Redis;
using X.PenServer.Interfaces.Lifecycle;
using X.PenServer.Models.Mongo;

namespace X.PenServer.Interfaces.Redis;

/// <summary>
/// 教师批改缓存
/// </summary>
public interface ITeacherCorrectRedisService : IRedisService, ISingletonService
{
    /// <summary>
    /// 设置当前正在批改的学生和试卷信息
    /// </summary>
    /// <param name="teacherid">教师id</param>
    /// <param name="paperid">试卷id</param>
    /// <param name="classid">班级id</param>
    /// <param name="studentid">学生id</param>
    /// <param name="pageno">批改的页码</param>
    /// <returns></returns>
    Task SetCorrectStudentAsync(string teacherid, string paperid, string classid, string studentid, int pageno,string studentNo=null);

    /// <summary>
    /// 获取当前正在批改的学生和试卷信息
    /// </summary>
    /// <param name="teacherid">教师id</param>
    /// <returns></returns>
    Task<TeacherCorrectingStudent> GetCorrectStudentAsync(string teacherid);

    /// <summary>
    /// 获取当前正在批改的学生
    /// </summary>
    /// <param name="teacherid">教师用户id</param>
    /// <returns></returns>
    Task<string> HGetCorrectStudentAsync(string teacherid);

    /// <summary>
    /// 设置教师正在批改的学生
    /// </summary>
    /// <param name="teacherid">教师id</param>
    /// <param name="studentid">学生id</param>
    /// <returns></returns>
    Task HSetCorrectStudentAsync(string teacherid, string studentid);

    /// <summary>
    /// 设置批阅学生状态
    /// </summary>
    /// <param name="paperid">试卷id</param>
    /// <param name="studentid">学生id</param>
    /// <param name="status">状态: 0.未开始 1.作答中 2.已识别 3.已批阅</param>
    /// <returns></returns>
    Task HSetCorrectStudentStatusAsync(string paperid, string studentid, int status);

    /// <summary>
    /// 获取批阅学生状态
    /// </summary>
    /// <param name="paperid">试卷id</param>
    /// <param name="studentid">学生id</param>
    /// <returns>状态: 0.未开始 1.作答中 2.已识别 3.已批阅</returns>
    Task<int> HGetCorrectStudentStatusAsync(string paperid, string studentid);

    /// <summary>
    /// 设置学生订正试卷记录
    /// </summary>
    /// <param name="studentid">用户id</param>
    /// <param name="paperid">试卷id</param>
    /// <returns></returns>
    Task HSetStudentCorrectPaperAsync(string studentid, string paperid);

    /// <summary>
    /// 获取当前批阅班级
    /// </summary>
    /// <param name="teacherid">教师id</param>
    /// <returns></returns>
    Task<string> HGetCorrectClassIdAsync(string teacherid);

    /// <summary>
    /// 获取当前批阅的学生信息
    /// </summary>
    /// <param name="studentid">当前学生id</param>
    /// <returns></returns>
    Task<StudentInfo> GetCorrectStudentInfoAsync(string studentid);

    /// <summary>
    /// 设置当前批阅的学生信息
    /// </summary>
    /// <param name="studentid">当前学生id</param>
    /// <param name="by_correct_student">当前被批阅学生信息</param>
    /// <returns></returns>
    Task SetCorrectStudentInfoAsync(string studentid, StudentInfo by_correct_student);

    /// <summary>
    /// 设置当前学生被哪些学生批阅信息
    /// </summary>
    /// <param name="by_correct_studentid">当前被批阅的学生id</param>
    /// <param name="student">学生信息</param>
    /// <returns></returns>
    Task SAddByCorrectStudentInfoAsync(string by_correct_studentid, StudentInfo student);
}