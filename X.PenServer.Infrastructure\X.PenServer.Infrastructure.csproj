<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>disable</Nullable>
    <ErrorOnDuplicatePublishOutputFiles>false</ErrorOnDuplicatePublishOutputFiles>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <AppendRuntimeIdentifierToOutputPath>false</AppendRuntimeIdentifierToOutputPath>
    <!--auto version start-->
    <Deterministic>false</Deterministic>
    <GenerateAssemblyFileVersionAttribute>false</GenerateAssemblyFileVersionAttribute>
    <GenerateAssemblyInformationalVersionAttribute>false</GenerateAssemblyInformationalVersionAttribute>
    <AssemblyVersion>1.0.*</AssemblyVersion>
    <!--auto version end-->
  </PropertyGroup>
  <PropertyGroup>
    <Copyright>Copyright © Lucifer. All rights reserved.</Copyright>
    <PackageId>X.PenServer.Infrastructure</PackageId>
    <PackageProjectUrl>https://github.com/X-Lucifer</PackageProjectUrl>
    <RepositoryUrl>https://github.com/X-Lucifer</RepositoryUrl>
    <Company>X Lucifer</Company>
    <Authors>Lucifer</Authors>
    <AutoIncrementPackageRevision>true</AutoIncrementPackageRevision>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Autofac" Version="8.0.0" />
    <PackageReference Include="Autofac.Extensions.DependencyInjection" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks" Version="8.0.7" />
    <PackageReference Include="StackExchange.Redis" Version="2.8.2" />
    <PackageReference Include="System.Diagnostics.DiagnosticSource" Version="8.0.1" />
    <PackageReference Include="System.IO.Pipelines" Version="8.0.0" />
    <PackageReference Include="X.Elegant.Redis" Version="1.0.6" />
    <PackageReference Include="Masa.BuildingBlocks.Data" Version="1.0.0" />
    <PackageReference Include="Masa.Contrib.Data.IdGenerator.Snowflake" Version="1.0.0" />
    <PackageReference Include="Masa.Contrib.Data.IdGenerator.SequentialGuid" Version="1.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\X.PenServer.Models\X.PenServer.Models.csproj" />
  </ItemGroup>
  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
  </ItemGroup>
</Project>
