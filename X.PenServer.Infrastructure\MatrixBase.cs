﻿// -- Function: MatrixBase.cs
// --- Project: X.PenServer.Infrastructure
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2024/2/20 14:43
namespace X.PenServer.Infrastructure;

using System.Text.Json.Serialization;

/// <summary>
/// 矩阵四点坐标
/// </summary>
public abstract class MatrixBase
{
	/// <summary>
	/// 当前铺码页码
	/// </summary>
	/// <remarks>练习薄为当前行号</remarks>
	[JsonInclude]
	[JsonPropertyName(nameof(Page))]
	public int Page { get; set; }

	/// <summary>
	/// 左上
	/// </summary>
	[JsonInclude]
	[JsonPropertyName(nameof(TopLeft))]
	public MatrixPoint TopLeft { get; set; }

	/// <summary>
	/// 右上
	/// </summary>
	[JsonInclude]
	[JsonPropertyName(nameof(TopRight))]
	public MatrixPoint TopRight { get; set; }

	/// <summary>
	/// 右下
	/// </summary>
	[JsonInclude]
	[JsonPropertyName(nameof(BottomRight))]
	public MatrixPoint BottomRight { get; set; }

	/// <summary>
	/// 左下
	/// </summary>
	[JsonInclude]
	[JsonPropertyName(nameof(BottomLeft))]
	public MatrixPoint BottomLeft { get; set; }
}