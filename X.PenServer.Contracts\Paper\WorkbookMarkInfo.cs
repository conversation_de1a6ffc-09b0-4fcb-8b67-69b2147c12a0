﻿// -- Function: WorkbookMarkInfo.cs
// --- Project: X.PenServer.Contracts
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2024/04/04 17:04:49

namespace X.PenServer.Contracts.Paper;

/// <summary>
/// 题干标注信息
/// </summary>
public class WorkbookMarkInfo
{
    /// <summary>
    /// 试卷id
    /// </summary>
    public string PaperId { get; set; }

    /// <summary>
    /// 题目id
    /// </summary>
    public string ItemId { get; set; }

    /// <summary>
    /// 试卷页面id
    /// </summary>
    public string WorkbookPageId { get; set; }

    /// <summary>
    /// 页码
    /// </summary>
    public int Page { get; set; }

    /// <summary>
    /// 题型
    /// </summary>
    public int ItemTypeId { get; set; }

    /// <summary>
    /// 题干坐标区域
    /// </summary>
    public string Range { get; set; }
}