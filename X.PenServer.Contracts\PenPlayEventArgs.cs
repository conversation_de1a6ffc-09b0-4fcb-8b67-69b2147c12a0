﻿// -- Function: PenPlayEventArgs.cs
// --- Project: X.PenServer.Contracts
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2024/3/5 14:28
// ReS<PERSON>per disable InconsistentNaming
// ReSharper disable ClassNeverInstantiated.Global

namespace X.PenServer.Contracts;

using System.Text.Json.Serialization;

/// <summary>
/// 播放事件参数
/// </summary>
public class PenPlayEventArgs : PenMediaBaseEventArgs
{
	/// <summary>
	/// 文件url
	/// </summary>
	[JsonPropertyName(nameof(Url))]
	[JsonInclude]
	public string Url { get; set; }
}