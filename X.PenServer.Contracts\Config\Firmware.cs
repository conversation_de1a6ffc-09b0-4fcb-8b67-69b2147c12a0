﻿// -- Function: Firmware.cs
// --- Project: X.PenServer.Contracts
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2023/10/24 16:50

// ReS<PERSON>per disable ClassNeverInstantiated.Global

using System.Text.Json.Serialization;

namespace X.PenServer.Contracts.Config;

/// <summary>
/// 固件信息
/// </summary>
public class Firmware
{
    /// <summary>
    /// 固件型号
    /// <remarks>
    /// dotpen: 点阵笔 <br/>
    /// voicepen: 语音笔
    /// </remarks>
    /// </summary>
    [JsonInclude]
    [JsonPropertyName(nameof(ModelCode))]
    public string ModelCode { get; set; }

    /// <summary>
    /// 最新固件版本编码
    /// </summary>
    [JsonInclude]
    [JsonPropertyName(nameof(BinCode))]
    public string BinCode { get; set; }

    /// <summary>
    /// 最新固件文件名
    /// </summary>
    [JsonInclude]
    [JsonPropertyName(nameof(FileName))]
    public string FileName { get; set; }
}