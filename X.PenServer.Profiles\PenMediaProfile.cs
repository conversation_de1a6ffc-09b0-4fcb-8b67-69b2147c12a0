﻿// -- Function: PenMediaProfile.cs
// --- Project: X.PenServer.Profiles
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2024/3/5 15:22
// ReSharper disable InconsistentNaming

namespace X.PenServer.Profiles;

using Contracts;
using Contracts.Queue;
using Protos;

/// <summary>
/// 点阵笔媒体
/// </summary>
public class PenMediaProfile : ProfileBase
{
	public PenMediaProfile()
	{
		//播放
		CreateMap<PenMediaRequest, PenPlayEventArgs>().ReverseMap();
		//录制
		CreateMap<PenMediaRequest, PenRecordEventArgs>().ReverseMap();
		//录制队列
		CreateMap<PenRecordEventArgs, PenRecordData>().ReverseMap();
	}
}