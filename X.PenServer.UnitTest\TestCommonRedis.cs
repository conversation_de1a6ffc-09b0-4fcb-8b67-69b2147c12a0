﻿// -- Function: TestCommonRedis.cs
// --- Project: X.PenServer.UnitTest
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2024/06/04 15:06

using Masa.BuildingBlocks.Data;
using System.Security.Cryptography;
using X.PenServer.Contracts.Paper;
using X.PenServer.Contracts.Tips;
using X.PenServer.Infrastructure;
using X.PenServer.Interfaces;
using X.PenServer.Interfaces.Redis;
using Xunit.Abstractions;
using Xunit.Microsoft.DependencyInjection.Abstracts;

namespace X.PenServer.UnitTest;

/// <inheritdoc />
public class TestCommonRedis : TestBed<TestFixture>
{
    private readonly ICommonRedisService _redis_service;
    private readonly ITestOutputHelper _test_output_helper;
    private readonly ISnowflakeGenerator _snowflake_generator;
    private readonly ISequentialGuidGenerator _guid_generator;
    private readonly IDapperService _dapper_service;

    /// <inheritdoc />
    public TestCommonRedis(ITestOutputHelper test_output_helper, TestFixture test_fixture
        
        ) : base(test_output_helper, test_fixture)
    {
        _test_output_helper = test_output_helper;
        _snowflake_generator = test_fixture.GetService<ISnowflakeGenerator>(test_output_helper);
        _guid_generator = test_fixture.GetService<ISequentialGuidGenerator>(test_output_helper);
        _redis_service = test_fixture.GetService<ICommonRedisService>(test_output_helper);
        _dapper_service=test_fixture.GetService<IDapperService>(test_output_helper);
    }

    [Fact]
    public async Task MockAsync()
    {
        var paperid = _snowflake_generator.NewId();
        var studentid = _snowflake_generator.NewId();
        var pageid = RandomNumberGenerator.GetInt32(1000, 9999);
        var xkey = $"{paperid}|{studentid}|{pageid}";
        var student_key = $"PenWebSocket|{xkey}";
        for (var i = 0; i < 3; i++)
        {
            var rid = _guid_generator.NewStringId();
            await _redis_service.SAddAsync(student_key, rid);
        }

        await _redis_service.ExpireAsync(student_key, TimeSpan.FromMinutes(10));
        var exists = await _redis_service.ExistsAsync(student_key);
        Assert.True(exists);
        var ttl = await _redis_service.TtlAsync(student_key);
        Assert.InRange(ttl!.Value, TimeSpan.Zero, TimeSpan.FromMinutes(10));
        var list = await _redis_service.SMembersStringAsync(student_key);
        foreach (var item in list)
        {
            _test_output_helper.WriteLine(item);
        }

        await _redis_service.RemoveAsync(student_key);

        var english_card_matrix = await _redis_service.HGetAsync<List<MatrixRegion>>(RedisKeys.MATRIX, RedisKeys.ENGLISH_UNIT_CARD);
        var unicard_matrix = await _redis_service.HGetAsync<MatrixRegion>(RedisKeys.MATRIX, RedisKeys.UNIVERSAL_CARD);
        var tagno_matrix = await _redis_service.HGetAsync<List<MatrixItem>>(RedisKeys.MATRIX, RedisKeys.PAPER_TAG_STUDENT_NO);
        var workbook_matrix = await _redis_service.HGetAsync<List<MatrixItemRange>>(RedisKeys.MATRIX, RedisKeys.WORKBOOK);
        _test_output_helper.WriteLine($"english: {english_card_matrix.ToJson()}");
        _test_output_helper.WriteLine($"unicard: {unicard_matrix.ToJson()}");
        _test_output_helper.WriteLine($"tagno: {tagno_matrix.ToJson()}");
        _test_output_helper.WriteLine($"workbook: {workbook_matrix.ToJson()}");
    }

    [Fact]
    public void SetVoiceTips()
    {
        var tips = new VoiceTipsDto
        {
            SleepMilliseconds=2600,
            VoiceUrl="https://dotpenvoice.obs.cn-east-2.myhuaweicloud.com/recordtips.mp3"
        };

        var key = $"PenServerRecordStartTips";

        _redis_service.Set(key, tips.ToJson());
    }

    [Fact]
    public void GetVoiceTips()
    {
        var result =GetCoordInfo(
            x:216,
            y:784,
            paperid: "1916064130929512449",
            currentPageNo:2,
            page:6
            ).ConfigureAwait(false).GetAwaiter().GetResult();

        var key = $"PenServerRecordStartTips";

        //var tips = _redis_service.Get<VoiceTipsDto>(key);

        //Assert.True(tips?.VoiceUrl=="https://dotpenvoice.obs.cn-east-2.myhuaweicloud.com/recordtips.mp3");
    }

    private async Task<PaperCoordInfo> GetCoordInfo(double x, double y, string paperid, int currentPageNo, int page)
    {
        if (currentPageNo==0)
        {
            currentPageNo=1;
        }

        const string sql = """
                           SELECT ivc.WorkBookPageId,ivc.Range,wp.Page
                           FROM [dbo].[Exam_Item_Voice_Coord] ivc
                           JOIN dbo.WorkbookPage wp ON wp.id=ivc.WorkBookPageId
                           WHERE [Range] IS NOT NULL
                                 AND LEN([Range]) > 0
                              	  AND ivc.[PaperId] = @paperid;
                           """;
        var list = await _dapper_service.QueryListAsync<PaperCoordInfo>
        (
            sql, new
            {
                paperid
            }
        );
        if (list == null || !list.Any())
        {
            return null;
        }

        var workBookPageList = list.Select(z => new { z.WorkBookPageId,z.Page}).Distinct().Select(z => new WorkBookPageDto { WorkBookPageId=z.WorkBookPageId,Page=z.Page }).ToList();

        foreach (var item in workBookPageList)
        {
            item.Index= item.Page;
            item.PageId=5;

            if (item.Page % 2 == 0)
            {
                item.PageId=6;
            }
        }

        foreach (var item in list)
        {
            var findPage = workBookPageList.First(z => z.WorkBookPageId==item.WorkBookPageId);
            item.Index=findPage.Index;
            item.PageId=findPage.PageId;
        }

        var xlist = list
            .Where(z => z.Index>=currentPageNo&&z.PageId==page)
            .ToList();

        try
        {
            return (from item in xlist
                    let ranges = Newtonsoft.Json.JsonConvert.DeserializeObject<List<XPoint>>(item.Range)
                    where ranges is
                    {
                        Count: 4
                    }
                    let pa = ranges[0]
                    let pc = ranges[2]
                    let isin = IsInMatrix(pa, pc, new XPoint(Convert.ToInt32(x), Convert.ToInt32(y)))
                    where isin
                    select item).FirstOrDefault();
        }
        catch (Exception ex)
        {

            throw;
        }
    }

    /// <summary>
    /// 判断点位是否在指定矩阵之内
    /// </summary>
    /// <param name="dot_a">点位区域A</param>
    /// <param name="dot_c">点位区域C</param>
    /// <param name="point">点位坐标</param>
    /// <returns></returns>
    private static bool IsInMatrix(XPoint dot_a, XPoint dot_c, XPoint point)
    {
        var is_inmatrix = point.X >= dot_a.X
                          && point.X <= dot_c.X
                          && point.Y >= dot_a.Y
                          && point.Y <= dot_c.Y;
        return is_inmatrix;
    }
}