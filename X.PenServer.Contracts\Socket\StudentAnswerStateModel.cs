﻿using System.Text.Json.Serialization;
// ReSharper disable InconsistentNaming

namespace X.PenServer.Contracts.Socket
{
	/// <summary>
	/// 学生作答状态
	/// </summary>
	public class StudentAnswerStateModel
	{
		[JsonPropertyName(nameof(MDType))]
		[JsonInclude]
		public int MDType { get; set; }

		[JsonPropertyName(nameof(UserId))]
		[JsonInclude]
		public string UserId { get; set; }	

		/// <summary>
		/// 试卷id
		/// </summary>
		[JsonPropertyName(nameof(PaperId))]
		[JsonInclude]
		public string PaperId { get; set; }

		/// <summary>
		/// 实际采集到的页码id
		/// </summary>
		/// <remarks>明鼎实际返回的页码id</remarks>
		[JsonPropertyName(nameof(PageId))]
		[JsonInclude]
		public int PageId { get; set; }
	}
}
