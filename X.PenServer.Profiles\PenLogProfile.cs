﻿// -- Function: PenLogProfile.cs
// --- Project: X.PenServer.Profiles
// ---- Remark: 
// ---- Author: Lucifer
// ------ Date: 2023/11/16 11:33
// ReSharper disable InconsistentNaming

namespace X.PenServer.Profiles;

using Contracts.Queue;
using Models.Mongo;

/// <summary>
/// 点位笔迹
/// </summary>
public class PenLogProfile : ProfileBase
{
	public PenLogProfile()
	{
		//作答点位笔迹
		CreateMap<DotBase, PenDot>()
			.ForMember(x => x.Oid, map => map.MapFrom(src => src.Mid))
			.ForMember(x => x.Time, map => map.MapFrom(src => src.AddTime))
			.ReverseMap();

		CreateMap<DotBase, WorkbookPenDot>()
			.ForMember(x => x.Oid, map => map.MapFrom(src => src.Mid))
			.ForMember(x => x.Time, map => map.MapFrom(src => src.AddTime))
			.ReverseMap();
	}
}