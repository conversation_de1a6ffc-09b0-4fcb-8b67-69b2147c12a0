﻿// -- Function: CardAnswerResultData.cs
//  --- Project: X.PenServer.Contracts
//  ---- Remark: 
//  ---- Author: Lucifer
//  ------ Date: 2024/05/20 11:05

using System.Text.Json.Serialization;

namespace X.PenServer.Contracts.Queue;

public class CardAnswerResultData
{
    /// <summary>
    /// 请求id
    /// </summary>
    [JsonPropertyName(nameof(RequestId))]
    [JsonInclude]
    public string RequestId { get; set; }

    /// <summary>
    /// 试卷id
    /// </summary>
    [JsonPropertyName(nameof(PaperId))]
    [JsonInclude]
    public string PaperId { get; set; }

    /// <summary>
    /// 题目编号
    /// </summary>
    [JsonPropertyName(nameof(ItemNo))]
    [JsonInclude]
    public int ItemNo { get; set; }

    /// <summary>
    /// 点阵笔编号
    /// </summary>
    [JsonPropertyName(nameof(Mac))]
    [JsonInclude]
    public string Mac { get; set; }

    /// <summary>
    /// 用户id
    /// </summary>
    [JsonPropertyName(nameof(UserId))]
    [JsonInclude]
    public string UserId { get; set; }

    /// <summary>
    /// 作答结果
    /// </summary>
    [JsonPropertyName(nameof(Result))]
    [JsonInclude]
    public string Result { get; set; }

    /// <summary>
    /// 是否为橡皮擦清理之后作答的结果
    /// </summary>
    [JsonPropertyName(nameof(IsEraser))]
    [JsonInclude]
    public bool IsEraser { get; set; }
}