﻿// -- Function: OfflineCorrectPenDotData.cs
// --- Project: X.PenServer.Contracts
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2024/4/9 16:56
namespace X.PenServer.Contracts.Queue;

using System.Collections.Concurrent;
using System.Text.Json.Serialization;

/// <summary>
/// 离线批改点位数据
/// </summary>
public class OfflineCorrectPenDotData
{
	/// <summary>
	/// 请求id
	/// </summary>
	[JsonPropertyName(nameof(RequestId))]
	[JsonInclude]
	public string RequestId { get; set; }

	/// <summary>
	/// Mac地址
	/// </summary>
	[JsonPropertyName(nameof(Mac))]
	[JsonInclude]
	public string Mac { get; set; }

	/// <summary>
	/// 勾选学号笔迹
	/// </summary>
	[JsonPropertyName(nameof(StudentNoDots))]
	[JsonInclude]
	public ConcurrentDictionary<int, List<PenDot>> StudentNoDots { get; set; }

	/// <summary>
	/// 实际批改笔迹
	/// </summary>
	[JsonPropertyName(nameof(CorrectDots))]
	[JsonInclude]
	public ConcurrentDictionary<int, List<PenDot>> CorrectDots { get; set; }
}