﻿// -- Function: IPenMappingRedisService.cs
// --- Project: X.PenServer.Interfaces
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2023/10/30 17:27

using X.Elegant.Redis.Infrastructure;
using X.PenServer.Interfaces.Lifecycle;

namespace X.PenServer.Interfaces.Redis;

/// <summary>
/// 点阵笔和用户映射服务
/// </summary>
public interface IPenMappingRedisService : IRedisService, ISingletonService
{
    /// <summary>
    /// 获取点阵笔绑定的用户
    /// </summary>
    /// <param name="mac">mac</param>
    /// <returns></returns>
    Task<string> HGetMappingUserAsync(string mac);

    /// <summary>
    /// 添加点阵笔和用户绑定
    /// </summary>
    /// <param name="mac"></param>
    /// <param name="userid"></param>
    /// <returns></returns>
    Task HSetMappingUserAsync(string mac, string userid);

    /// <summary>
    /// 获取点阵笔用户类型
    /// </summary>
    /// <param name="mac">点阵笔编号</param>
    /// <returns></returns>
    Task<int> HGetPenRoleAsync(string mac);

    /// <summary>
    /// 设置点阵笔用户类型
    /// </summary>
    /// <param name="mac"></param>
    /// <param name="role"></param>
    /// <returns></returns>
    Task HSetPenRoleAsync(string mac, int role);
}