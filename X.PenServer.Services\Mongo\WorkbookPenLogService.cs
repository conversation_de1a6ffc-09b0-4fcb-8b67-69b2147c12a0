﻿// -- Function: WorkbookPenLogService.cs
// --- Project: X.PenServer.Services
// ---- Remark:
// ---- Author: Lucifer
// ------ Date: 2024/3/19 14:35
namespace X.PenServer.Services.Mongo;

/// <inheritdoc cref="IWorkbookPenLogService" />
public class WorkbookPenLogService : MongoAutoService<WorkbookPenLog>, IWorkbookPenLogService
{
	/// <inheritdoc />
	public WorkbookPenLogService(IMongoConfig config) : base(config)
	{
	}

	/// <inheritdoc />
	protected override void CreateIndex(IMongoCollection<WorkbookPenLog> collection)
	{
		var paperid_builder = Builders<WorkbookPenLog>.IndexKeys
			.Ascending(x => x.PaperId)
			.Ascending(x => x.ItemId)
			.Ascending(x => x.UserId)
			.Ascending(x => x.Mac)
			.Ascending(x => x.LineNo)
			.Ascending(x => x.Blank)
			.Ascending(x => x.Page)
			.Ascending(x => x.AddTime);
		collection.Indexes.CreateIndex(paperid_builder, collection.CollectionNamespace.CollectionName + "_PaperId_Key");

		var itemid_builder = Builders<WorkbookPenLog>.IndexKeys
			.Ascending(x => x.ItemId)
			.Ascending(x => x.UserId)
			.Ascending(x => x.Mac)
			.Ascending(x => x.PaperId)
			.Ascending(x => x.LineNo)
			.Ascending(x => x.Blank)
			.Ascending(x => x.PageId)
			.Ascending(x => x.AddTime);
		collection.Indexes.CreateIndex(itemid_builder, collection.CollectionNamespace.CollectionName + "_ItemId_Key");
	}
}